package com.jycb.jycbz.modules.device.controller.admin;

import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.modules.device.service.DeviceOwnershipValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备归属验证控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/device/ownership")
@RequiredArgsConstructor
@Tag(name = "设备归属验证", description = "设备归属验证相关接口")
public class DeviceOwnershipController {

    private final DeviceOwnershipValidationService deviceOwnershipValidationService;

    /**
     * 验证设备是否属于指定门店
     */
    @GetMapping("/validate/shop")
    @Operation(summary = "验证设备是否属于指定门店")
    public Result<Boolean> validateDeviceBelongsToShop(
            @Parameter(description = "设备ID") @RequestParam Integer deviceId,
            @Parameter(description = "门店ID") @RequestParam Integer shopId) {
        
        boolean belongs = deviceOwnershipValidationService.validateDeviceBelongsToShop(deviceId, shopId);
        return Result.success(belongs);
    }

    /**
     * 验证设备是否属于指定合作商
     */
    @GetMapping("/validate/partner")
    @Operation(summary = "验证设备是否属于指定合作商")
    public Result<Boolean> validateDeviceBelongsToPartner(
            @Parameter(description = "设备ID") @RequestParam Integer deviceId,
            @Parameter(description = "合作商ID") @RequestParam Integer partnerId) {
        
        boolean belongs = deviceOwnershipValidationService.validateDeviceBelongsToPartner(deviceId, partnerId);
        return Result.success(belongs);
    }

    /**
     * 验证设备列表是否都属于指定门店
     */
    @PostMapping("/validate/shop/batch")
    @Operation(summary = "批量验证设备是否属于指定门店")
    public Result<DeviceOwnershipValidationService.DeviceOwnershipValidationResult> validateDevicesBelongToShop(
            @Parameter(description = "设备ID列表") @RequestBody List<Integer> deviceIds,
            @Parameter(description = "门店ID") @RequestParam Integer shopId) {
        
        DeviceOwnershipValidationService.DeviceOwnershipValidationResult result = 
            deviceOwnershipValidationService.validateDevicesBelongToShop(deviceIds, shopId);
        return Result.success(result);
    }

    /**
     * 验证设备列表是否都属于指定合作商
     */
    @PostMapping("/validate/partner/batch")
    @Operation(summary = "批量验证设备是否属于指定合作商")
    public Result<DeviceOwnershipValidationService.DeviceOwnershipValidationResult> validateDevicesBelongToPartner(
            @Parameter(description = "设备ID列表") @RequestBody List<Integer> deviceIds,
            @Parameter(description = "合作商ID") @RequestParam Integer partnerId) {
        
        DeviceOwnershipValidationService.DeviceOwnershipValidationResult result = 
            deviceOwnershipValidationService.validateDevicesBelongToPartner(deviceIds, partnerId);
        return Result.success(result);
    }

    /**
     * 获取设备的完整归属链
     */
    @GetMapping("/chain/{deviceId}")
    @Operation(summary = "获取设备的完整归属链")
    public Result<DeviceOwnershipValidationService.DeviceOwnershipChain> getDeviceOwnershipChain(
            @Parameter(description = "设备ID") @PathVariable Integer deviceId) {
        
        DeviceOwnershipValidationService.DeviceOwnershipChain chain = 
            deviceOwnershipValidationService.getDeviceOwnershipChain(deviceId);
        return Result.success(chain);
    }

    /**
     * 验证设备归属链的一致性
     */
    @GetMapping("/validate/consistency/{deviceId}")
    @Operation(summary = "验证设备归属链的一致性")
    public Result<DeviceOwnershipValidationService.DeviceOwnershipValidationResult> validateDeviceOwnershipConsistency(
            @Parameter(description = "设备ID") @PathVariable Integer deviceId) {
        
        DeviceOwnershipValidationService.DeviceOwnershipValidationResult result = 
            deviceOwnershipValidationService.validateDeviceOwnershipConsistency(deviceId);
        return Result.success(result);
    }

    /**
     * 批量验证设备归属链的一致性
     */
    @PostMapping("/validate/consistency/batch")
    @Operation(summary = "批量验证设备归属链的一致性")
    public Result<DeviceOwnershipValidationService.DeviceOwnershipValidationResult> batchValidateDeviceOwnershipConsistency(
            @Parameter(description = "设备ID列表") @RequestBody List<Integer> deviceIds) {
        
        DeviceOwnershipValidationService.DeviceOwnershipValidationResult result = 
            deviceOwnershipValidationService.batchValidateDeviceOwnershipConsistency(deviceIds);
        return Result.success(result);
    }

    /**
     * 验证门店是否属于指定合作商
     */
    @GetMapping("/validate/shop-partner")
    @Operation(summary = "验证门店是否属于指定合作商")
    public Result<Boolean> validateShopBelongsToPartner(
            @Parameter(description = "门店ID") @RequestParam Integer shopId,
            @Parameter(description = "合作商ID") @RequestParam Integer partnerId) {
        
        boolean belongs = deviceOwnershipValidationService.validateShopBelongsToPartner(shopId, partnerId);
        return Result.success(belongs);
    }

    /**
     * 验证门店是否属于指定业务主体
     */
    @GetMapping("/validate/shop-entity")
    @Operation(summary = "验证门店是否属于指定业务主体")
    public Result<Boolean> validateShopBelongsToEntity(
            @Parameter(description = "门店ID") @RequestParam Integer shopId,
            @Parameter(description = "业务主体ID") @RequestParam Integer entityId) {
        
        boolean belongs = deviceOwnershipValidationService.validateShopBelongsToEntity(shopId, entityId);
        return Result.success(belongs);
    }

    /**
     * 验证合作商是否属于指定业务主体
     */
    @GetMapping("/validate/partner-entity")
    @Operation(summary = "验证合作商是否属于指定业务主体")
    public Result<Boolean> validatePartnerBelongsToEntity(
            @Parameter(description = "合作商ID") @RequestParam Integer partnerId,
            @Parameter(description = "业务主体ID") @RequestParam Integer entityId) {
        
        boolean belongs = deviceOwnershipValidationService.validatePartnerBelongsToEntity(partnerId, entityId);
        return Result.success(belongs);
    }
}
