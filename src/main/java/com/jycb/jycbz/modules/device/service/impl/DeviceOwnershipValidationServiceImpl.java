package com.jycb.jycbz.modules.device.service.impl;

import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.mapper.DeviceMapper;
import com.jycb.jycbz.modules.device.service.DeviceOwnershipValidationService;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.mapper.ShopMapper;
import com.jycb.jycbz.modules.partner.entity.Partner;
import com.jycb.jycbz.modules.partner.mapper.PartnerMapper;
import com.jycb.jycbz.modules.entity.entity.Entity;
import com.jycb.jycbz.modules.entity.mapper.EntityMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 设备归属验证服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceOwnershipValidationServiceImpl implements DeviceOwnershipValidationService {

    private final DeviceMapper deviceMapper;
    private final ShopMapper shopMapper;
    private final PartnerMapper partnerMapper;
    private final EntityMapper entityMapper;

    @Override
    public boolean validateDeviceBelongsToShop(Integer deviceId, Integer shopId) {
        if (deviceId == null || shopId == null) {
            log.warn("设备归属验证失败：参数不能为空，设备ID: {}, 门店ID: {}", deviceId, shopId);
            return false;
        }

        try {
            Device device = deviceMapper.selectById(deviceId);
            if (device == null) {
                log.warn("设备归属验证失败：设备不存在，设备ID: {}", deviceId);
                return false;
            }

            // 严格检查设备是否已绑定
            if (device.getIsBound() == null || device.getIsBound() != 1) {
                log.warn("设备归属验证失败：设备未绑定，设备ID: {}, 设备编号: {}, 绑定状态: {}",
                        deviceId, device.getDeviceNo(), device.getIsBound());
                return false;
            }

            // 严格检查设备是否属于指定门店
            if (!Objects.equals(device.getShopId(), shopId)) {
                log.warn("设备归属验证失败：设备不属于指定门店，设备ID: {}, 设备编号: {}, 设备门店ID: {}, 指定门店ID: {}",
                        deviceId, device.getDeviceNo(), device.getShopId(), shopId);
                return false;
            }

            // 进一步验证门店的有效性
            Shop shop = shopMapper.selectById(shopId);
            if (shop == null) {
                log.warn("设备归属验证失败：门店不存在，门店ID: {}", shopId);
                return false;
            }

            // 验证设备的合作商和业务主体与门店是否一致（处理数据类型不匹配问题）
            boolean deviceShopPartnerMatches = (device.getPartnerId() != null && shop.getPartnerId() != null) &&
                    Long.valueOf(device.getPartnerId()).equals(Long.valueOf(shop.getPartnerId()));
            if (!deviceShopPartnerMatches) {
                log.warn("设备归属验证失败：设备合作商与门店合作商不一致，设备ID: {}, 设备编号: {}, 设备合作商ID: {}, 门店合作商ID: {}",
                        deviceId, device.getDeviceNo(), device.getPartnerId(), shop.getPartnerId());
                return false;
            }

            // 处理数据类型不匹配问题
            boolean deviceShopEntityMatches = (device.getEntityId() != null && shop.getEntityId() != null) &&
                    Long.valueOf(device.getEntityId()).equals(Long.valueOf(shop.getEntityId()));
            if (!deviceShopEntityMatches) {
                log.warn("设备归属验证失败：设备业务主体与门店业务主体不一致，设备ID: {}, 设备编号: {}, 设备业务主体ID: {}, 门店业务主体ID: {}",
                        deviceId, device.getDeviceNo(), device.getEntityId(), shop.getEntityId());
                return false;
            }

            log.debug("设备归属验证成功：设备 {} 属于门店 {}", device.getDeviceNo(), shop.getShopName());
            return true;

        } catch (Exception e) {
            log.error("设备归属验证异常，设备ID: {}, 门店ID: {}", deviceId, shopId, e);
            return false;
        }
    }

    @Override
    public boolean validateDeviceBelongsToPartner(Integer deviceId, Integer partnerId) {
        if (deviceId == null || partnerId == null) {
            log.warn("设备归属验证失败：参数不能为空，设备ID: {}, 合作商ID: {}", deviceId, partnerId);
            return false;
        }

        try {
            Device device = deviceMapper.selectById(deviceId);
            if (device == null) {
                log.warn("设备归属验证失败：设备不存在，设备ID: {}", deviceId);
                return false;
            }

            // 严格检查设备是否已绑定
            if (device.getIsBound() == null || device.getIsBound() != 1) {
                log.warn("设备归属验证失败：设备未绑定，设备ID: {}, 设备编号: {}, 绑定状态: {}",
                        deviceId, device.getDeviceNo(), device.getIsBound());
                return false;
            }

            // 严格检查设备是否属于指定合作商（处理数据类型不匹配问题）
            boolean devicePartnerMatches = (device.getPartnerId() != null && partnerId != null) &&
                    Long.valueOf(device.getPartnerId()).equals(Long.valueOf(partnerId));
            if (!devicePartnerMatches) {
                log.warn("设备归属验证失败：设备不属于指定合作商，设备ID: {}, 设备编号: {}, 设备合作商ID: {}, 指定合作商ID: {}",
                        deviceId, device.getDeviceNo(), device.getPartnerId(), partnerId);
                return false;
            }

            // 进一步验证合作商的有效性
            Partner partner = partnerMapper.selectById(partnerId);
            if (partner == null) {
                log.warn("设备归属验证失败：合作商不存在，合作商ID: {}", partnerId);
                return false;
            }

            // 验证设备的业务主体与合作商是否一致（处理数据类型不匹配问题）
            boolean entityIdMatches = (device.getEntityId() != null && partner.getEntityId() != null) &&
                    Long.valueOf(device.getEntityId()).equals(Long.valueOf(partner.getEntityId()));
            if (!entityIdMatches) {
                log.warn("设备归属验证失败：设备业务主体与合作商业务主体不一致，设备ID: {}, 设备编号: {}, 设备业务主体ID: {}, 合作商业务主体ID: {}",
                        deviceId, device.getDeviceNo(), device.getEntityId(), partner.getEntityId());
                return false;
            }

            // 如果设备有门店，验证门店是否属于该合作商
            if (device.getShopId() != null) {
                Shop shop = shopMapper.selectById(device.getShopId());
                if (shop == null) {
                    log.warn("设备归属验证失败：设备关联的门店不存在，设备ID: {}, 设备编号: {}, 门店ID: {}",
                            deviceId, device.getDeviceNo(), device.getShopId());
                    return false;
                }

                // 处理数据类型不匹配问题
                boolean shopPartnerMatches = (shop.getPartnerId() != null && partnerId != null) &&
                        Long.valueOf(shop.getPartnerId()).equals(Long.valueOf(partnerId));
                if (!shopPartnerMatches) {
                    log.warn("设备归属验证失败：设备关联的门店不属于指定合作商，设备ID: {}, 设备编号: {}, 门店合作商ID: {}, 指定合作商ID: {}",
                            deviceId, device.getDeviceNo(), shop.getPartnerId(), partnerId);
                    return false;
                }
            }

            log.debug("设备归属验证成功：设备 {} 属于合作商 {}", device.getDeviceNo(), partner.getPartnerName());
            return true;

        } catch (Exception e) {
            log.error("设备归属验证异常，设备ID: {}, 合作商ID: {}", deviceId, partnerId, e);
            return false;
        }
    }

    @Override
    public boolean validateDeviceBelongsToEntity(Integer deviceId, Integer entityId) {
        if (deviceId == null || entityId == null) {
            return false;
        }

        try {
            Device device = deviceMapper.selectById(deviceId);
            if (device == null) {
                log.warn("设备不存在，设备ID: {}", deviceId);
                return false;
            }

            // 检查设备是否已绑定
            if (device.getIsBound() == null || device.getIsBound() != 1) {
                log.warn("设备未绑定，设备ID: {}, 绑定状态: {}", deviceId, device.getIsBound());
                return false;
            }

            // 检查设备是否属于指定业务主体
            boolean belongs = Objects.equals(device.getEntityId(), entityId);
            if (!belongs) {
                log.warn("设备不属于指定业务主体，设备ID: {}, 设备业务主体ID: {}, 指定业务主体ID: {}", 
                        deviceId, device.getEntityId(), entityId);
            }

            return belongs;
        } catch (Exception e) {
            log.error("验证设备归属业务主体失败，设备ID: {}, 业务主体ID: {}", deviceId, entityId, e);
            return false;
        }
    }

    @Override
    public DeviceOwnershipValidationResult validateDevicesBelongToShop(List<Integer> deviceIds, Integer shopId) {
        DeviceOwnershipValidationResult result = new DeviceOwnershipValidationResult();
        List<Integer> validDeviceIds = new ArrayList<>();
        List<Integer> invalidDeviceIds = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        if (deviceIds == null || deviceIds.isEmpty() || shopId == null) {
            result.setValid(false);
            result.setMessage("参数不能为空");
            return result;
        }

        for (Integer deviceId : deviceIds) {
            if (validateDeviceBelongsToShop(deviceId, shopId)) {
                validDeviceIds.add(deviceId);
            } else {
                invalidDeviceIds.add(deviceId);
                errorMessages.add("设备 " + deviceId + " 不属于门店 " + shopId);
            }
        }

        result.setValid(invalidDeviceIds.isEmpty());
        result.setValidDeviceIds(validDeviceIds);
        result.setInvalidDeviceIds(invalidDeviceIds);
        result.setErrorMessages(errorMessages);
        result.setMessage(result.isValid() ? "所有设备都属于指定门店" : 
                "有 " + invalidDeviceIds.size() + " 台设备不属于指定门店");

        return result;
    }

    @Override
    public DeviceOwnershipValidationResult validateDevicesBelongToPartner(List<Integer> deviceIds, Integer partnerId) {
        DeviceOwnershipValidationResult result = new DeviceOwnershipValidationResult();
        List<Integer> validDeviceIds = new ArrayList<>();
        List<Integer> invalidDeviceIds = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        if (deviceIds == null || deviceIds.isEmpty() || partnerId == null) {
            result.setValid(false);
            result.setMessage("参数不能为空");
            return result;
        }

        for (Integer deviceId : deviceIds) {
            if (validateDeviceBelongsToPartner(deviceId, partnerId)) {
                validDeviceIds.add(deviceId);
            } else {
                invalidDeviceIds.add(deviceId);
                errorMessages.add("设备 " + deviceId + " 不属于合作商 " + partnerId);
            }
        }

        result.setValid(invalidDeviceIds.isEmpty());
        result.setValidDeviceIds(validDeviceIds);
        result.setInvalidDeviceIds(invalidDeviceIds);
        result.setErrorMessages(errorMessages);
        result.setMessage(result.isValid() ? "所有设备都属于指定合作商" : 
                "有 " + invalidDeviceIds.size() + " 台设备不属于指定合作商");

        return result;
    }

    @Override
    public boolean validateShopBelongsToPartner(Integer shopId, Integer partnerId) {
        if (shopId == null || partnerId == null) {
            return false;
        }

        try {
            Shop shop = shopMapper.selectById(shopId);
            if (shop == null) {
                log.warn("门店不存在，门店ID: {}", shopId);
                return false;
            }

            boolean belongs = Objects.equals(shop.getPartnerId(), partnerId);
            if (!belongs) {
                log.warn("门店不属于指定合作商，门店ID: {}, 门店合作商ID: {}, 指定合作商ID: {}", 
                        shopId, shop.getPartnerId(), partnerId);
            }

            return belongs;
        } catch (Exception e) {
            log.error("验证门店归属合作商失败，门店ID: {}, 合作商ID: {}", shopId, partnerId, e);
            return false;
        }
    }

    @Override
    public boolean validateShopBelongsToEntity(Integer shopId, Integer entityId) {
        if (shopId == null || entityId == null) {
            return false;
        }

        try {
            Shop shop = shopMapper.selectById(shopId);
            if (shop == null) {
                log.warn("门店不存在，门店ID: {}", shopId);
                return false;
            }

            boolean belongs = Objects.equals(shop.getEntityId(), entityId);
            if (!belongs) {
                log.warn("门店不属于指定业务主体，门店ID: {}, 门店业务主体ID: {}, 指定业务主体ID: {}", 
                        shopId, shop.getEntityId(), entityId);
            }

            return belongs;
        } catch (Exception e) {
            log.error("验证门店归属业务主体失败，门店ID: {}, 业务主体ID: {}", shopId, entityId, e);
            return false;
        }
    }

    @Override
    public boolean validatePartnerBelongsToEntity(Integer partnerId, Integer entityId) {
        if (partnerId == null || entityId == null) {
            return false;
        }

        try {
            Partner partner = partnerMapper.selectById(partnerId);
            if (partner == null) {
                log.warn("合作商不存在，合作商ID: {}", partnerId);
                return false;
            }

            boolean belongs = Objects.equals(partner.getEntityId(), entityId);
            if (!belongs) {
                log.warn("合作商不属于指定业务主体，合作商ID: {}, 合作商业务主体ID: {}, 指定业务主体ID: {}", 
                        partnerId, partner.getEntityId(), entityId);
            }

            return belongs;
        } catch (Exception e) {
            log.error("验证合作商归属业务主体失败，合作商ID: {}, 业务主体ID: {}", partnerId, entityId, e);
            return false;
        }
    }

    @Override
    public DeviceOwnershipChain getDeviceOwnershipChain(Integer deviceId) {
        DeviceOwnershipChain chain = new DeviceOwnershipChain();
        
        if (deviceId == null) {
            chain.setConsistent(false);
            chain.setInconsistencyReason("设备ID不能为空");
            return chain;
        }

        try {
            Device device = deviceMapper.selectById(deviceId);
            if (device == null) {
                chain.setConsistent(false);
                chain.setInconsistencyReason("设备不存在");
                return chain;
            }

            chain.setDeviceId(deviceId);
            chain.setDeviceNo(device.getDeviceNo());
            chain.setShopId(device.getShopId());
            chain.setPartnerId(device.getPartnerId());
            chain.setEntityId(device.getEntityId());

            // 获取门店信息
            if (device.getShopId() != null) {
                Shop shop = shopMapper.selectById(device.getShopId());
                if (shop != null) {
                    chain.setShopName(shop.getShopName());
                    
                    // 验证门店的合作商和业务主体是否与设备一致
                    if (!Objects.equals(shop.getPartnerId(), device.getPartnerId())) {
                        chain.setConsistent(false);
                        chain.setInconsistencyReason("设备的合作商ID与门店的合作商ID不一致");
                        return chain;
                    }
                    
                    if (!Objects.equals(shop.getEntityId(), device.getEntityId())) {
                        chain.setConsistent(false);
                        chain.setInconsistencyReason("设备的业务主体ID与门店的业务主体ID不一致");
                        return chain;
                    }
                }
            }

            // 获取合作商信息
            if (device.getPartnerId() != null) {
                Partner partner = partnerMapper.selectById(device.getPartnerId());
                if (partner != null) {
                    chain.setPartnerName(partner.getPartnerName());
                    
                    // 验证合作商的业务主体是否与设备一致
                    if (!Objects.equals(partner.getEntityId(), device.getEntityId())) {
                        chain.setConsistent(false);
                        chain.setInconsistencyReason("设备的业务主体ID与合作商的业务主体ID不一致");
                        return chain;
                    }
                }
            }

            // 获取业务主体信息
            if (device.getEntityId() != null) {
                Entity entity = entityMapper.selectById(device.getEntityId());
                if (entity != null) {
                    chain.setEntityName(entity.getName());
                }
            }

            chain.setConsistent(true);
            return chain;

        } catch (Exception e) {
            log.error("获取设备归属链失败，设备ID: {}", deviceId, e);
            chain.setConsistent(false);
            chain.setInconsistencyReason("获取设备归属链失败: " + e.getMessage());
            return chain;
        }
    }

    @Override
    public DeviceOwnershipValidationResult validateDeviceOwnershipConsistency(Integer deviceId) {
        DeviceOwnershipChain chain = getDeviceOwnershipChain(deviceId);
        
        DeviceOwnershipValidationResult result = new DeviceOwnershipValidationResult();
        result.setValid(chain.isConsistent());
        result.setMessage(chain.isConsistent() ? "设备归属链一致" : chain.getInconsistencyReason());
        
        if (chain.isConsistent()) {
            result.setValidDeviceIds(List.of(deviceId));
            result.setInvalidDeviceIds(new ArrayList<>());
        } else {
            result.setValidDeviceIds(new ArrayList<>());
            result.setInvalidDeviceIds(List.of(deviceId));
            result.setErrorMessages(List.of(chain.getInconsistencyReason()));
        }
        
        return result;
    }

    @Override
    public DeviceOwnershipValidationResult batchValidateDeviceOwnershipConsistency(List<Integer> deviceIds) {
        DeviceOwnershipValidationResult result = new DeviceOwnershipValidationResult();
        List<Integer> validDeviceIds = new ArrayList<>();
        List<Integer> invalidDeviceIds = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        if (deviceIds == null || deviceIds.isEmpty()) {
            result.setValid(false);
            result.setMessage("设备ID列表不能为空");
            return result;
        }

        for (Integer deviceId : deviceIds) {
            DeviceOwnershipValidationResult singleResult = validateDeviceOwnershipConsistency(deviceId);
            if (singleResult.isValid()) {
                validDeviceIds.add(deviceId);
            } else {
                invalidDeviceIds.add(deviceId);
                errorMessages.add("设备 " + deviceId + ": " + singleResult.getMessage());
            }
        }

        result.setValid(invalidDeviceIds.isEmpty());
        result.setValidDeviceIds(validDeviceIds);
        result.setInvalidDeviceIds(invalidDeviceIds);
        result.setErrorMessages(errorMessages);
        result.setMessage(result.isValid() ? "所有设备归属链都一致" : 
                "有 " + invalidDeviceIds.size() + " 台设备归属链不一致");

        return result;
    }
}
