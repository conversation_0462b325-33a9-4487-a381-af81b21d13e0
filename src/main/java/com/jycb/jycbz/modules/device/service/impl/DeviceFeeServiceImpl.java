package com.jycb.jycbz.modules.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.modules.device.convert.DeviceFeeConvert;
import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.modules.device.mapper.DeviceFeeMapper;
import com.jycb.jycbz.modules.device.service.DeviceFeeService;
import com.jycb.jycbz.modules.device.util.DeviceFeeCalculator;
import com.jycb.jycbz.modules.device.vo.DeviceFeeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * 设备费用服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceFeeServiceImpl extends ServiceImpl<DeviceFeeMapper, DeviceFee> implements DeviceFeeService {

    private final DeviceFeeMapper deviceFeeMapper;
    private final DeviceFeeConvert deviceFeeConvert;

    @Override
    public DeviceFee getDeviceFeeByDeviceId(Integer deviceId) {
        if (deviceId == null) {
            return null;
        }
        return deviceFeeMapper.getDeviceFeeByDeviceId(deviceId);
    }

    @Override
    public DeviceFeeVO getDeviceFeeVOByDeviceId(Integer deviceId) {
        if (deviceId == null) {
            return null;
        }
        DeviceFee deviceFee = getDeviceFeeByDeviceId(deviceId);
        return deviceFeeConvert.convert(deviceFee);
    }

    public DeviceFee getDeviceFeeByDeviceNo(String deviceNo) {
        if (deviceNo == null || deviceNo.trim().isEmpty()) {
            return null;
        }
        return deviceFeeMapper.getDeviceFeeByDeviceNo(deviceNo);
    }

    public boolean deleteByDeviceId(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }
        
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFee::getDeviceId, deviceId);
        
        return remove(queryWrapper);
    }

    @Override
    public PageResult<DeviceFeeVO> getDeviceFeeList(Integer page, Integer size, String feeName, Integer feeType, Integer entityId, Integer partnerId, Integer shopId, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        if (feeName != null && !feeName.trim().isEmpty()) {
            queryWrapper.like(DeviceFee::getFeeName, feeName);
        }
        if (feeType != null) {
            queryWrapper.eq(DeviceFee::getFeeType, feeType);
        }
        if (entityId != null) {
            queryWrapper.eq(DeviceFee::getEntityId, entityId);
        }
        if (partnerId != null) {
            queryWrapper.eq(DeviceFee::getPartnerId, partnerId);
        }
        if (shopId != null) {
            queryWrapper.eq(DeviceFee::getShopId, shopId);
        }
        if (status != null) {
            queryWrapper.eq(DeviceFee::getStatus, status);
        }
        
        // 分页查询，将Integer转为long
        Page<DeviceFee> pageResult = page(new Page<>(Long.valueOf(page), Long.valueOf(size)), queryWrapper);
        
        // 转换为VO
        List<DeviceFeeVO> voList = new ArrayList<>();
        for (DeviceFee deviceFee : pageResult.getRecords()) {
            voList.add(deviceFeeConvert.convert(deviceFee));
        }
        
        // 构建分页结果
        PageResult<DeviceFeeVO> result = new PageResult<>();
        result.setList(voList);
        result.setTotal(pageResult.getTotal());
        result.setPageNum(Long.valueOf(page));
        result.setPageSize(Long.valueOf(size));
        
        return result;
    }

    @Override
    public DeviceFeeVO getDeviceFeeDetail(Integer id) {
        DeviceFee deviceFee = getById(id);
        return deviceFeeConvert.convert(deviceFee);
    }

    @Override
    public boolean addDeviceFee(DeviceFee deviceFee) {
        return save(deviceFee);
    }

    @Override
    public boolean updateDeviceFee(DeviceFee deviceFee) {
        if (deviceFee == null || deviceFee.getId() == null) {
            return false;
        }
        return updateById(deviceFee);
    }

    @Override
    public boolean deleteDeviceFee(Integer id) {
        return removeById(id);
    }

    @Override
    public boolean updateDeviceFeeStatus(Integer id, Integer status) {
        DeviceFee deviceFee = getById(id);
        if (deviceFee == null) {
            return false;
        }
        
        deviceFee.setStatus(status);
        return updateById(deviceFee);
    }

    @Override
    public boolean setDefaultDeviceFee(Integer id) {
        // 先将所有设置为非默认
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFee::getIsDefault, 1);
        
        List<DeviceFee> defaultFees = list(queryWrapper);
        for (DeviceFee fee : defaultFees) {
            fee.setIsDefault(0);
            updateById(fee);
        }
        
        // 设置指定ID为默认
        DeviceFee deviceFee = getById(id);
        if (deviceFee == null) {
            return false;
        }
        
        deviceFee.setIsDefault(1);
        return updateById(deviceFee);
    }

    @Override
    public List<DeviceFeeVO> getDeviceFeesByShopId(Integer shopId) {
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFee::getShopId, shopId);
        
        List<DeviceFee> deviceFees = list(queryWrapper);
        List<DeviceFeeVO> voList = new ArrayList<>();
        
        for (DeviceFee deviceFee : deviceFees) {
            voList.add(deviceFeeConvert.convert(deviceFee));
        }
        
        return voList;
    }

    @Override
    public List<DeviceFeeVO> getDeviceFeesByPartnerId(Integer partnerId) {
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFee::getPartnerId, partnerId);
        
        List<DeviceFee> deviceFees = list(queryWrapper);
        List<DeviceFeeVO> voList = new ArrayList<>();
        
        for (DeviceFee deviceFee : deviceFees) {
            voList.add(deviceFeeConvert.convert(deviceFee));
        }
        
        return voList;
    }

    @Override
    public DeviceFeeVO getDefaultDeviceFee() {
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFee::getIsDefault, 1);
        
        DeviceFee deviceFee = getOne(queryWrapper);
        return deviceFeeConvert.convert(deviceFee);
    }

    @Override
    public BigDecimal calculateUseFee(Integer deviceId, Integer useMinutes) {
        if (deviceId == null || useMinutes == null) {
            log.warn("参数无效，设备ID: {}, 使用分钟数: {}", deviceId, useMinutes);
            return BigDecimal.ZERO;
        }

        // 获取设备费用配置
        DeviceFee deviceFee = deviceFeeMapper.getDeviceFeeByDeviceId(deviceId);
        if (deviceFee == null) {
            // 获取默认费用配置
            LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceFee::getIsDefault, 1)
                       .eq(DeviceFee::getStatus, 1); // 只获取启用的默认费用

            List<DeviceFee> defaultFees = list(queryWrapper);
            if (!defaultFees.isEmpty()) {
                deviceFee = defaultFees.get(0); // 使用第一个默认费用
                log.debug("使用默认费用配置，设备ID: {}, 费用ID: {}", deviceId, deviceFee.getId());
            }
        }

        if (deviceFee == null) {
            log.warn("未找到设备费用配置，设备ID: {}", deviceId);
            return BigDecimal.ZERO;
        }

        // 使用统一的计费计算器计算费用
        BigDecimal calculatedFee = DeviceFeeCalculator.calculateFee(deviceFee, useMinutes);

        log.debug("费用计算完成，设备ID: {}, 使用分钟数: {}, 费用: {}", deviceId, useMinutes, calculatedFee);

        return calculatedFee;
    }

    @Override
    public String exportDeviceFeeList(String feeName, Integer feeType, Integer entityId, Integer partnerId, Integer shopId, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
        if (feeName != null && !feeName.trim().isEmpty()) {
            queryWrapper.like(DeviceFee::getFeeName, feeName);
        }
        if (feeType != null) {
            queryWrapper.eq(DeviceFee::getFeeType, feeType);
        }
        if (entityId != null) {
            queryWrapper.eq(DeviceFee::getEntityId, entityId);
        }
        if (partnerId != null) {
            queryWrapper.eq(DeviceFee::getPartnerId, partnerId);
        }
        if (shopId != null) {
            queryWrapper.eq(DeviceFee::getShopId, shopId);
        }
        if (status != null) {
            queryWrapper.eq(DeviceFee::getStatus, status);
        }
        
        // 查询数据
        List<DeviceFee> deviceFees = list(queryWrapper);

        if (deviceFees.isEmpty()) {
            return "没有符合条件的设备费用数据";
        }

        try {
            // 创建导出目录
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成文件名
            String fileName = "device_fee_" + System.currentTimeMillis() + ".xlsx";
            String filePath = exportDir + "/" + fileName;

            // 创建Excel工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                XSSFSheet sheet = workbook.createSheet("设备费用列表");

                // 创建表头
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("费用ID");
                headerRow.createCell(1).setCellValue("费用名称");
                headerRow.createCell(2).setCellValue("费用类型");
                headerRow.createCell(3).setCellValue("费用金额");
                headerRow.createCell(4).setCellValue("业务主体ID");
                headerRow.createCell(5).setCellValue("合作商ID");
                headerRow.createCell(6).setCellValue("门店ID");
                headerRow.createCell(7).setCellValue("状态");
                headerRow.createCell(8).setCellValue("创建时间");

                // 填充数据
                for (int i = 0; i < deviceFees.size(); i++) {
                    DeviceFee fee = deviceFees.get(i);
                    Row row = sheet.createRow(i + 1);
                    row.createCell(0).setCellValue(fee.getId() != null ? fee.getId().toString() : "");
                    row.createCell(1).setCellValue(fee.getFeeName() != null ? fee.getFeeName() : "");
                    row.createCell(2).setCellValue(getFeeTypeName(fee.getFeeType()));
                    row.createCell(3).setCellValue(fee.getPrice() != null ? fee.getPrice().toString() : "0");
                    row.createCell(4).setCellValue(fee.getEntityId() != null ? fee.getEntityId().toString() : "");
                    row.createCell(5).setCellValue(fee.getPartnerId() != null ? fee.getPartnerId().toString() : "");
                    row.createCell(6).setCellValue(fee.getShopId() != null ? fee.getShopId().toString() : "");
                    row.createCell(7).setCellValue(getStatusName(fee.getStatus()));
                    row.createCell(8).setCellValue(fee.getCreateTime() != null ? fee.getCreateTime().toString() : "");
                }

                // 调整列宽
                for (int i = 0; i < 9; i++) {
                    sheet.autoSizeColumn(i);
                }

                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }

            // 返回文件下载URL
            return "/export/" + fileName;

        } catch (Exception e) {
            log.error("导出设备费用列表失败", e);
            throw new RuntimeException("导出设备费用列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取费用类型名称
     */
    private String getFeeTypeName(Integer feeType) {
        if (feeType == null) return "未知";
        switch (feeType) {
            case 1: return "按时计费";
            case 2: return "按次计费";
            case 3: return "包月计费";
            case 4: return "其他";
            default: return "未知类型";
        }
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "禁用";
            case 1: return "启用";
            default: return "未知状态";
        }
    }
}