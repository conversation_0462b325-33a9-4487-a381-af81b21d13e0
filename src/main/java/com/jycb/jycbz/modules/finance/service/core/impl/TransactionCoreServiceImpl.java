package com.jycb.jycbz.modules.finance.service.core.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.modules.finance.domain.Transaction;
import com.jycb.jycbz.modules.finance.domain.factory.TransactionFactory;
import com.jycb.jycbz.modules.finance.dto.TransactionQueryDTO;
import com.jycb.jycbz.modules.finance.entity.FinanceLog;
import com.jycb.jycbz.modules.finance.mapper.FinanceLogMapper;
import com.jycb.jycbz.modules.finance.service.core.TransactionCoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 交易核心服务实现类
 */
@Service
@Slf4j
public class TransactionCoreServiceImpl extends ServiceImpl<FinanceLogMapper, FinanceLog> implements TransactionCoreService {

    @Override
    public PageResult<FinanceLog> getTransactionPage(TransactionQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<FinanceLog> wrapper = new LambdaQueryWrapper<>();
        
        if (queryDTO.getAccountType() != null) {
            wrapper.eq(FinanceLog::getAccountType, queryDTO.getAccountType());
        }
        
        if (queryDTO.getAccountId() != null) {
            wrapper.eq(FinanceLog::getAccountId, queryDTO.getAccountId().intValue());
        }
        
        if (queryDTO.getEntityId() != null) {
            wrapper.eq(FinanceLog::getEntityId, queryDTO.getEntityId().intValue());
        }
        
        if (queryDTO.getPartnerId() != null) {
            wrapper.eq(FinanceLog::getPartnerId, queryDTO.getPartnerId().intValue());
        }
        
        if (queryDTO.getShopId() != null) {
            wrapper.eq(FinanceLog::getShopId, queryDTO.getShopId().intValue());
        }
        
        if (queryDTO.getType() != null) {
            wrapper.eq(FinanceLog::getType, queryDTO.getType());
        }
        
        if (queryDTO.getStartTime() != null) {
            wrapper.ge(FinanceLog::getCreateTime, queryDTO.getStartTime());
        }
        
        if (queryDTO.getEndTime() != null) {
            wrapper.le(FinanceLog::getCreateTime, queryDTO.getEndTime());
        }
        
        wrapper.orderByDesc(FinanceLog::getCreateTime);
        
        // 分页查询
        Page<FinanceLog> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        IPage<FinanceLog> result = page(page, wrapper);
        
        // 构建返回结果
        return PageResult.build(result);
    }

    @Override
    public FinanceLog getTransactionById(Long id) {
        return getById(id);
    }

    @Override
    public String exportTransactions(TransactionQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<FinanceLog> wrapper = new LambdaQueryWrapper<>();
        
        if (queryDTO.getAccountType() != null) {
            wrapper.eq(FinanceLog::getAccountType, queryDTO.getAccountType());
        }
        
        if (queryDTO.getAccountId() != null) {
            wrapper.eq(FinanceLog::getAccountId, queryDTO.getAccountId().intValue());
        }
        
        if (queryDTO.getEntityId() != null) {
            wrapper.eq(FinanceLog::getEntityId, queryDTO.getEntityId().intValue());
        }
        
        if (queryDTO.getPartnerId() != null) {
            wrapper.eq(FinanceLog::getPartnerId, queryDTO.getPartnerId().intValue());
        }
        
        if (queryDTO.getShopId() != null) {
            wrapper.eq(FinanceLog::getShopId, queryDTO.getShopId().intValue());
        }
        
        if (queryDTO.getType() != null) {
            wrapper.eq(FinanceLog::getType, queryDTO.getType());
        }
        
        if (queryDTO.getStartTime() != null) {
            wrapper.ge(FinanceLog::getCreateTime, queryDTO.getStartTime());
        }
        
        if (queryDTO.getEndTime() != null) {
            wrapper.le(FinanceLog::getCreateTime, queryDTO.getEndTime());
        }
        
        wrapper.orderByDesc(FinanceLog::getCreateTime);
        
        // 查询数据
        List<FinanceLog> logs = list(wrapper);
        
        // 导出文件名
        String fileName = "transactions_" + System.currentTimeMillis() + ".xlsx";
        
        try {
            // 实际项目中这里需要使用POI或EasyExcel导出Excel
            // 这里仅作为示例，实际实现需要根据项目情况调整
            /*
            ExcelWriter writer = EasyExcel.write(fileName)
                    .build();
            WriteSheet sheet = EasyExcel.writerSheet("交易记录")
                    .build();
            writer.write(logs, sheet);
            writer.finish();
            */
            
            // 返回文件路径或下载URL
            return fileName;
        } catch (Exception e) {
            log.error("导出交易记录失败", e);
            throw new RuntimeException("导出交易记录失败", e);
        }
    }

    @Override
    public Object getTransactionStatistics(String accountType, Long accountId, Date startTime, Date endTime) {
        // 构建查询条件
        LambdaQueryWrapper<FinanceLog> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.eq(FinanceLog::getAccountType, accountType)
               .eq(FinanceLog::getAccountId, accountId.intValue());
        
        if (startTime != null) {
            wrapper.ge(FinanceLog::getCreateTime, startTime);
        }
        
        if (endTime != null) {
            wrapper.le(FinanceLog::getCreateTime, endTime);
        }
        
        // 查询所有交易记录
        List<FinanceLog> logs = list(wrapper);
        
        // 统计收入和支出
        BigDecimal income = BigDecimal.ZERO;
        BigDecimal expense = BigDecimal.ZERO;
        
        for (FinanceLog log : logs) {
            Transaction transaction = TransactionFactory.fromFinanceLog(log);
            
            if (transaction.isIncome()) {
                income = income.add(transaction.getAmount());
            } else if (transaction.isExpense() || transaction.isDecreaseFreeze()) {
                expense = expense.add(transaction.getAmount());
            }
        }
        
        // 构建统计结果
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("income", income);
        statistics.put("expense", expense);
        statistics.put("balance", income.subtract(expense));
        statistics.put("transactionCount", logs.size());
        
        return statistics;
    }
} 