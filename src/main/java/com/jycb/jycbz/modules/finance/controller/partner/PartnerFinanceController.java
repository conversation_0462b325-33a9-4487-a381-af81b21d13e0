package com.jycb.jycbz.modules.finance.controller.partner;

import cn.dev33.satoken.stp.StpUtil;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.controller.BaseFinanceController;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.utils.SecurityUtils;
import com.jycb.jycbz.modules.finance.entity.FinanceAccount;
import com.jycb.jycbz.modules.finance.entity.FinanceLog;
import com.jycb.jycbz.modules.finance.entity.OfflineWithdraw;
import com.jycb.jycbz.modules.finance.entity.Withdraw;
import com.jycb.jycbz.modules.finance.dto.BankCardDTO;
import com.jycb.jycbz.modules.finance.service.FinanceAccountService;
import com.jycb.jycbz.modules.finance.vo.BankCardVO;
import com.jycb.jycbz.modules.finance.vo.WithdrawApplyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合作商财务控制器
 */
@RestController
@RequestMapping("/partner/finance")
@Tag(name = "合作商财务接口")
public class PartnerFinanceController extends BaseFinanceController {

    // ==================== 实现BaseFinanceController抽象方法 ====================

    @Override
    protected Map<String, Object> getFilteredStatistics() {
        Long partnerId = getCurrentPartnerId();
        // 调用服务层方法获取合作商财务统计信息
        return (Map<String, Object>) financeAccountService.getPartnerFinanceStatistics(partnerId);
    }

    @Override
    protected FinanceAccount getFilteredAccount() {
        Long partnerId = getCurrentPartnerId();
        // 调用服务层方法获取合作商财务账户信息
        return financeAccountService.getPartnerAccount(partnerId);
    }

    @Override
    protected PageResult<FinanceLog> getFilteredLogs(Integer pageNum, Integer pageSize,
                                                    String type, LocalDateTime startTime, LocalDateTime endTime) {
        Long partnerId = getCurrentPartnerId();
        // 转换LocalDateTime为Date类型
        Date startDate = startTime != null ? java.sql.Timestamp.valueOf(startTime) : null;
        Date endDate = endTime != null ? java.sql.Timestamp.valueOf(endTime) : null;

        // 调用服务层方法获取合作商的财务日志
        // 使用正确的方法签名，避免类型转换警告
        return (PageResult<FinanceLog>) financeAccountService.getPartnerFinanceLogs(partnerId, pageNum, pageSize,
            type != null ? Integer.parseInt(type) : null, startDate, endDate);
    }

    @Override
    protected PageResult<OfflineWithdraw> getFilteredWithdrawRecords(Integer pageNum, Integer pageSize,
                                                                    Integer status, LocalDateTime startTime, LocalDateTime endTime) {
        Long partnerId = getCurrentPartnerId();
        // 合作商提现记录暂时返回空结果，后续可以实现具体逻辑
        PageResult<OfflineWithdraw> emptyResult = new PageResult<>();
        emptyResult.setList(new ArrayList<>());
        emptyResult.setTotal(0L);
        emptyResult.setPageNum(pageNum.longValue());
        emptyResult.setPageSize(pageSize.longValue());
        return emptyResult;
    }

    @Override
    protected void validateWithdrawPermission(WithdrawApplyVO withdrawApplyVO) {
        Long partnerId = getCurrentPartnerId();

        // 合作商提现权限验证：验证账户ID是否属于当前合作商
        if (withdrawApplyVO.getAccountId() != null) {
            FinanceAccount account = financeAccountService.getById(withdrawApplyVO.getAccountId());
            if (account == null) {
                throw new BusinessException("财务账户不存在");
            }

            // 类型安全的比较：将Integer转换为Long进行比较
            Long accountPartnerId = account.getPartnerId() != null ? account.getPartnerId().longValue() : null;
            if (!partnerId.equals(accountPartnerId)) {
                throw new BusinessException("无权操作其他合作商的财务账户");
            }
        }
    }

    @Override
    protected void setApplicantInfo(WithdrawApplyVO withdrawApplyVO) {
        // WithdrawApplyVO没有申请人信息字段，这里只做基本验证
        // 合作商的提现申请已通过validateWithdrawPermission验证权限
    }

    @Override
    protected Long getCurrentUserId() {
        return SecurityUtils.getCurrentUserId();
    }

    @Override
    protected String getCurrentUsername() {
        return SecurityUtils.getCurrentUsername();
    }

    @Override
    protected Long getCurrentPartnerId() {
        // 从Session中获取合作商ID
        Object partnerId = StpUtil.getSession().get("partnerId");
        if (partnerId == null) {
            throw new BusinessException("未找到合作商信息，请重新登录");
        }
        return Long.valueOf(partnerId.toString());
    }

    /**
     * 获取合作商账户信息
     */
    @Operation(summary = "获取合作商账户信息")
    @GetMapping("/account-info")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取合作商账户信息",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<Map<String, Object>> getPartnerAccountInfo() {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        // 直接调用服务方法，避免类型转换
        Object accountInfoObj = financeAccountService.getPartnerAccountInfo(partnerId);
        if (accountInfoObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> accountInfo = (Map<String, Object>) accountInfoObj;
            return CommonResult.success(accountInfo);
        } else {
            // 如果返回的不是Map类型，创建一个默认的Map
            Map<String, Object> defaultInfo = new HashMap<>();
            defaultInfo.put("partnerId", partnerId);
            defaultInfo.put("accountInfo", accountInfoObj);
            return CommonResult.success(defaultInfo);
        }
    }

    // 注意：getFinanceStatistics() 方法已经从 BaseFinanceController 继承
    // 通过重写 getFilteredStatistics() 方法来提供合作商特定的统计数据

    /**
     * 获取合作商财务统计信息（详细版）
     */
    @Operation(summary = "获取合作商财务统计信息（详细版）")
    @GetMapping("/statistics/detailed")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取合作商财务统计信息",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<Map<String, Object>> getDetailedPartnerFinanceStatistics() {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        Map<String, Object> statistics = (Map<String, Object>) financeAccountService.getPartnerFinanceStatistics(partnerId);
        return CommonResult.success(statistics);
    }

    /**
     * 获取合作商可用余额
     */
    @Operation(summary = "获取合作商可用余额")
    @GetMapping("/available-balance")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取合作商可用余额",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<BigDecimal> getPartnerAvailableBalance() {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        BigDecimal availableBalance = financeAccountService.getPartnerAvailableBalance(partnerId);
        return CommonResult.success(availableBalance);
    }

    // 注意：getFinanceLogs() 方法已经从 BaseFinanceController 继承
    // 通过重写 getFilteredLogs() 方法来提供合作商特定的财务日志数据

    /**
     * 获取合作商财务流水列表（详细版）
     */
    @Operation(summary = "获取合作商财务流水列表（详细版）")
    @GetMapping("/logs/detailed")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.QUERY,
        description = "获取合作商财务流水列表",
        targetType = AuditConstants.TargetType.FINANCE_LOG
    )
    public CommonResult<PageResult<FinanceLog>> getDetailedPartnerFinanceLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Date startTime,
            @RequestParam(required = false) Date endTime) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        PageResult<FinanceLog> result = (PageResult<FinanceLog>) financeAccountService.getPartnerFinanceLogs(partnerId, page, size, type, startTime, endTime);
        return CommonResult.success(result);
    }

    /**
     * 获取合作商财务流水详情
     */
    @Operation(summary = "获取合作商财务流水详情")
    @GetMapping("/log/{id}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取合作商财务流水详情",
        targetType = AuditConstants.TargetType.FINANCE_LOG,
        targetIdParam = "id"
    )
    public CommonResult<FinanceLog> getPartnerFinanceLogDetail(@PathVariable Long id) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        FinanceLog financeLog = (FinanceLog) financeAccountService.getPartnerFinanceLogDetail(partnerId, id);
        return CommonResult.success(financeLog);
    }

    /**
     * 获取合作商下的门店账户列表
     */
    @Operation(summary = "获取合作商下的门店账户列表")
    @GetMapping("/shop-accounts")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.QUERY,
        description = "获取合作商下的门店账户列表",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<List<FinanceAccount>> getPartnerShopAccounts() {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        List<FinanceAccount> accounts = (List<FinanceAccount>) financeAccountService.getPartnerShopAccounts(partnerId);
        return CommonResult.success(accounts);
    }

    /**
     * 获取合作商下的门店账户详情
     */
    @Operation(summary = "获取合作商下的门店账户详情")
    @GetMapping("/shop-account/{shopId}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取合作商下的门店账户详情",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT,
        targetIdParam = "shopId"
    )
    public CommonResult<FinanceAccount> getPartnerShopAccount(@PathVariable Long shopId) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        FinanceAccount account = (FinanceAccount) financeAccountService.getPartnerShopAccount(partnerId, shopId);
        return CommonResult.success(account);
    }

    /**
     * 获取合作商下门店财务流水列表
     */
    @Operation(summary = "获取合作商下门店财务流水列表")
    @GetMapping("/shop-logs/{shopId}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.QUERY,
        description = "获取合作商下门店财务流水列表",
        targetType = AuditConstants.TargetType.FINANCE_LOG,
        targetIdParam = "shopId"
    )
    public CommonResult<PageResult<FinanceLog>> getPartnerShopFinanceLogs(
            @PathVariable Long shopId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Date startTime,
            @RequestParam(required = false) Date endTime) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        PageResult<FinanceLog> result = (PageResult<FinanceLog>) financeAccountService.getPartnerShopFinanceLogs(partnerId, shopId, page, size, type, startTime, endTime);
        return CommonResult.success(result);
    }

    /**
     * 合作商申请提现
     */
    @Operation(summary = "合作商申请提现")
    @PostMapping("/apply-withdraw")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.WITHDRAW,
        description = "合作商申请提现",
        targetType = AuditConstants.TargetType.WITHDRAW,
        logRequestParams = true
    )
    public CommonResult<Boolean> applyPartnerWithdraw(@RequestBody WithdrawApplyVO withdrawApplyVO) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        boolean result = financeAccountService.applyPartnerWithdraw(partnerId, withdrawApplyVO);
        return CommonResult.success(result);
    }

    /**
     * 获取合作商提现记录
     */
    @Operation(summary = "获取合作商提现记录")
    @GetMapping("/withdraw-records")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.QUERY,
        description = "获取合作商提现记录",
        targetType = AuditConstants.TargetType.WITHDRAW
    )
    public CommonResult<PageResult<Withdraw>> getPartnerWithdrawRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        PageResult<Withdraw> result = (PageResult<Withdraw>) financeAccountService.getPartnerWithdrawRecords(partnerId, page, size, status);
        return CommonResult.success(result);
    }

    /**
     * 获取合作商提现详情
     */
    @Operation(summary = "获取合作商提现详情")
    @GetMapping("/withdraw/{id}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取合作商提现详情",
        targetType = AuditConstants.TargetType.WITHDRAW,
        targetIdParam = "id"
    )
    public CommonResult<Withdraw> getPartnerWithdrawDetail(@PathVariable Long id) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        Withdraw withdraw = (Withdraw) financeAccountService.getPartnerWithdrawDetail(partnerId, id);
        return CommonResult.success(withdraw);
    }

    /**
     * 取消合作商提现申请
     */
    @Operation(summary = "取消合作商提现申请")
    @PostMapping("/withdraw/cancel/{id}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.UPDATE,
        description = "取消合作商提现申请",
        targetType = AuditConstants.TargetType.WITHDRAW,
        targetIdParam = "id"
    )
    public CommonResult<Boolean> cancelPartnerWithdraw(@PathVariable Long id) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        boolean result = financeAccountService.cancelPartnerWithdraw(partnerId, id);
        return CommonResult.success(result);
    }

    /**
     * 获取合作商下门店的提现记录
     */
    @Operation(summary = "获取合作商下门店的提现记录")
    @GetMapping("/shop-withdraw-records/{shopId}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.QUERY,
        description = "获取合作商下门店的提现记录",
        targetType = AuditConstants.TargetType.WITHDRAW,
        targetIdParam = "shopId"
    )
    public CommonResult<PageResult<Withdraw>> getShopWithdrawRecordsByPartner(
            @PathVariable Long shopId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status) {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        PageResult<Withdraw> result = (PageResult<Withdraw>) financeAccountService.getShopWithdrawRecordsByPartner(partnerId, shopId, status, page, size);
        return CommonResult.success(result);
    }

    /**
     * 获取合作商银行卡列表
     */
    @Operation(summary = "获取合作商银行卡列表")
    @GetMapping("/bank-cards")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.QUERY,
        description = "获取合作商银行卡列表",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<List<BankCardVO>> getPartnerBankCards() {
        Long partnerId = getCurrentPartnerId();
        @SuppressWarnings("unchecked")
        List<BankCardVO> bankCards = (List<BankCardVO>) financeAccountService.getPartnerBankCards(partnerId);
        return CommonResult.success(bankCards);
    }

    /**
     * 添加合作商银行卡
     */
    @Operation(summary = "添加合作商银行卡")
    @PostMapping("/bank-card")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.CREATE,
        description = "添加合作商银行卡",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT,
        logRequestParams = true
    )
    public CommonResult<Boolean> addPartnerBankCard(@Valid @RequestBody BankCardDTO bankCardDTO) {
        Long partnerId = getCurrentPartnerId();

        // 转换DTO为VO
        BankCardVO bankCardVO = new BankCardVO();
        bankCardVO.setBankName(bankCardDTO.getBankName());
        bankCardVO.setCardNo(bankCardDTO.getCardNumber());
        bankCardVO.setHolderName(bankCardDTO.getCardHolderName());
        bankCardVO.setIdCardNo(bankCardDTO.getIdCard());
        bankCardVO.setReservedPhone(bankCardDTO.getMobile());
        bankCardVO.setIsDefault(bankCardDTO.getIsDefault());
        bankCardVO.setRemark(bankCardDTO.getRemark());

        boolean result = financeAccountService.addPartnerBankCard(partnerId, bankCardVO);
        return CommonResult.success(result);
    }

    /**
     * 更新合作商银行卡
     */
    @Operation(summary = "更新合作商银行卡")
    @PutMapping("/bank-card/{id}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.UPDATE,
        description = "更新合作商银行卡",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT,
        targetIdParam = "id",
        logRequestParams = true
    )
    public CommonResult<Boolean> updatePartnerBankCard(
            @PathVariable Long id,
            @RequestBody BankCardVO bankCardVO) {
        Long partnerId = getCurrentPartnerId();
        boolean result = financeAccountService.updatePartnerBankCard(partnerId, id, bankCardVO);
        return CommonResult.success(result);
    }

    /**
     * 删除合作商银行卡
     */
    @Operation(summary = "删除合作商银行卡")
    @DeleteMapping("/bank-card/{id}")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.DELETE,
        description = "删除合作商银行卡",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT,
        targetIdParam = "id"
    )
    public CommonResult<Boolean> deletePartnerBankCard(@PathVariable Long id) {
        Long partnerId = getCurrentPartnerId();
        boolean result = financeAccountService.deletePartnerBankCard(partnerId, id);
        return CommonResult.success(result);
    }

    /**
     * 获取最小提现金额
     */
    @Operation(summary = "获取最小提现金额")
    @GetMapping("/min-withdraw-amount")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "获取最小提现金额",
        targetType = AuditConstants.TargetType.SYSTEM_CONFIG
    )
    public CommonResult<BigDecimal> getMinWithdrawAmount() {
        BigDecimal minAmount = financeAccountService.getMinWithdrawAmount();
        return CommonResult.success(minAmount);
    }

    // ==================== 数据安全验证接口 ====================

    /**
     * 验证合作商账户余额
     */
    @Operation(summary = "验证合作商账户余额")
    @PostMapping("/validate-balance")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "验证合作商账户余额",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<Map<String, Object>> validatePartnerBalance() {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        FinanceAccount account = financeAccountService.getByTypeAndId("PARTNER", partnerId.intValue());
        if (account == null) {
            return CommonResult.failed("账户不存在");
        }

        Map<String, Object> result = financeAccountService.validateAccountBalance(account.getId());
        return CommonResult.success(result);
    }

    /**
     * 计算合作商可提现余额
     */
    @Operation(summary = "计算合作商可提现余额")
    @GetMapping("/withdrawable-balance")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "计算合作商可提现余额",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<BigDecimal> getWithdrawableBalance() {
        Long partnerId = getCurrentPartnerId();
        FinanceAccount account = financeAccountService.getByTypeAndId("PARTNER", partnerId.intValue());
        if (account == null) {
            return CommonResult.failed("账户不存在");
        }

        BigDecimal withdrawableBalance = financeAccountService.calculateWithdrawableBalance(account.getId());
        return CommonResult.success(withdrawableBalance);
    }

    /**
     * 检查合作商账户安全状态
     */
    @Operation(summary = "检查合作商账户安全状态")
    @GetMapping("/security-status")
    @Auditable(
        module = AuditConstants.Module.FINANCE,
        operation = AuditConstants.Operation.READ,
        description = "检查合作商账户安全状态",
        targetType = AuditConstants.TargetType.FINANCE_ACCOUNT
    )
    public CommonResult<Map<String, Object>> getAccountSecurityStatus() {
        Long partnerId = SecurityUtils.getCurrentPartnerId();
        FinanceAccount account = financeAccountService.getByTypeAndId("PARTNER", partnerId.intValue());
        if (account == null) {
            return CommonResult.failed("账户不存在");
        }

        Map<String, Object> securityStatus = financeAccountService.getAccountSecurityStatus(account.getId());
        return CommonResult.success(securityStatus);
    }
}