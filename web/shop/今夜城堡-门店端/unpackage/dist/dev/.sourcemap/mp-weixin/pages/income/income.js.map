{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/income/income.vue?18c8", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/income/income.vue?3bcc", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/income/income.vue?ebcb", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/income/income.vue?6d65", "uni-app:///pages/income/income.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/income/income.vue?69dc", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/income/income.vue?8c4b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "TabBar", "data", "balanceInfo", "balance", "todayIncome", "<PERSON><PERSON><PERSON><PERSON>", "totalIncome", "todayIncomeChange", "monthIncomeChange", "withdrawAmount", "withdrawPassword", "selectedBankCard", "bankCards", "withdrawRecords", "loading", "loadingBankCards", "submitting", "showModal", "pagination", "page", "size", "total", "newBankCard", "bankName", "bankAccount", "accountName", "idCard", "mobile", "isDefault", "computed", "formatAmount", "onLoad", "methods", "loadData", "Promise", "refreshData", "uni", "title", "setTimeout", "icon", "loadAccountInfo", "then", "catch", "console", "loadWithdrawRecords", "loadBankCards", "finally", "showWithdrawModal", "showBankCardModal", "showTransactionHistory", "url", "showBankCardManagement", "hideModal", "selectBankCard", "confirmBankCard", "validateWithdrawAmount", "submitWithdraw", "amount", "bankCardId", "password", "remark", "errorMessage", "duration", "formatDateTime", "getStatusText", "getStatusClass", "showAddBankCardModal", "resetNewBankCard", "toggleDefaultCard", "addBankCard", "validateBankCardForm", "setDefaultBankCard", "content", "success", "editBankCard", "deleteBankCard"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAovB,CAAgB,kvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC0ZxwB;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACAC,aACA,wBACA,4BACA,qBACA;QACA;MACA;IACA;IAEA;IACAC;MACAC;QAAAC;MAAA;MACA;MACAC;QACAF;QACAA;UACAC;UACAE;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA,sCACAC;QACA;UACAtC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA,GACAkC;QACAC;QACA;QACA;UACAxC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAoC;MAAA;MACA;QACAzB;QACAC;MACA,GACAqB;QACA;QACA;QACA;MACA,GACAC;QACAC;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACA;MACA,wCACAJ;QACA;QACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA,GACAC;QACAC;QACA;MACA,GACAG;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAb;QACAc;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAlB;UACAC;UACAE;QACA;QACA;MACA;MACA;IACA;IAEA;IACAgB;MACA;MACA;MAEA;QACA;MACA;MAEA;QACAnB;UACAC;UACAE;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAiB;MAAA;MACA;;MAEA;MACA;QACApB;UACAC;UACAE;QACA;QACA;MACA;;MAEA;MACA;QACAH;UACAC;UACAE;QACA;QACA;MACA;;MAEA;MACA;QACAH;UACAC;UACAE;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACAkB;QACAC;QACAC;QACAC;MACA,GACAnB;QACAL;UACAC;UACAE;QACA;QAEA;QACA;MACA,GACAG;QACAC;QAEA;QACA;UACAkB;QACA;UACAA;QACA;QAEAzB;UACAC;UACAE;UACAuB;QACA;MACA,GACAhB;QACA;MACA;IACA;IAEA;IACAiB;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA5C;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAwC;MACA;IACA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACA;MACA;MAEA;;MAEA;MACA,gDACA5B;QACAL;UACAC;UACAE;QACA;QAEA;QACA;MACA,GACAG;QACAC;QAEA;QACA;UACAkB;QACA;UACAA;QACA;QAEAzB;UACAC;UACAE;UACAuB;QACA;MACA,GACAhB;QACA;MACA;IACA;IAEA;IACAwB;MACA;QACAlC;UACAC;UACAE;QACA;QACA;MACA;MAEA;QACAH;UACAC;UACAE;QACA;QACA;MACA;MAEA;QACAH;UACAC;UACAE;QACA;QACA;MACA;MAEA;QACAH;UACAC;UACAE;QACA;QACA;MACA;MAEA;QACAH;UACAC;UACAE;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAH;UACAC;UACAE;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAH;UACAC;UACAE;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAgC;MAAA;MACAnC;QACAC;QACAmC;QACAC;UACA;YACA,gDACAhC;cACAL;gBACAC;gBACAE;cACA;cACA;YACA,GACAG;cACAC;cACAP;gBACAC;gBACAE;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAmC;MACAtC;QACAC;QACAE;MACA;IACA;IAEA;IACAoC;MAAA;MACAvC;QACAC;QACAmC;QACAC;UACA;YACA,4CACAhC;cACAL;gBACAC;gBACAE;cACA;cACA;YACA,GACAG;cACAC;cACAP;gBACAC;gBACAE;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACj6BA;AAAA;AAAA;AAAA;AAAylC,CAAgB,mjCAAG,EAAC,C;;;;;;;;;;;ACA7mC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/income/income.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/income/income.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./income.vue?vue&type=template&id=4a11fda6&scoped=true&\"\nvar renderjs\nimport script from \"./income.vue?vue&type=script&lang=js&\"\nexport * from \"./income.vue?vue&type=script&lang=js&\"\nimport style0 from \"./income.vue?vue&type=style&index=0&id=4a11fda6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a11fda6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/income/income.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./income.vue?vue&type=template&id=4a11fda6&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./income.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./income.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 头部信息 -->\n    <view class=\"top-spacing\"></view>\n    <view class=\"page-header\">\n      <view class=\"header-content\">\n        <view class=\"page-title purple-title\">财务管理</view>\n        <view class=\"page-subtitle\">查看您的收益和提现管理</view>\n      </view>\n      <view>\n        <view class=\"refresh-btn\" @tap=\"refreshData\">\n          <text class=\"material-icons\" style=\"font-size: 28rpx; margin-right: 8rpx;\">refresh</text>\n          <text>刷新</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 财务统计卡片 -->\n    <view class=\"stats-grid\">\n      <view class=\"stat-card\">\n        <view class=\"stat-title\">账户余额（元）</view>\n        <view class=\"stat-value\">{{ formatAmount(balanceInfo.balance) }}</view>\n        <view class=\"stat-change positive\">\n          <text class=\"material-icons neon-green\" style=\"font-size: 28rpx;\">account_balance_wallet</text>\n          <text>可提现余额</text>\n        </view>\n      </view>\n      \n      <view class=\"stat-card\">\n        <view class=\"stat-title\">今日收益（元）</view>\n        <view class=\"stat-value\">{{ formatAmount(balanceInfo.todayIncome) }}</view>\n        <view class=\"stat-change\" :class=\"balanceInfo.todayIncomeChange >= 0 ? 'positive' : 'negative'\">\n          <text class=\"material-icons\" :class=\"balanceInfo.todayIncomeChange >= 0 ? 'neon-green' : 'neon-pink'\" style=\"font-size: 28rpx;\">{{ balanceInfo.todayIncomeChange >= 0 ? 'arrow_upward' : 'arrow_downward' }}</text>\n          <text>{{ Math.abs(balanceInfo.todayIncomeChange || 0) }}% 较昨日</text>\n        </view>\n      </view>\n      \n      <view class=\"stat-card\">\n        <view class=\"stat-title\">本月收益（元）</view>\n        <view class=\"stat-value\">{{ formatAmount(balanceInfo.monthIncome) }}</view>\n        <view class=\"stat-change\" :class=\"balanceInfo.monthIncomeChange >= 0 ? 'positive' : 'negative'\">\n          <text class=\"material-icons\" :class=\"balanceInfo.monthIncomeChange >= 0 ? 'neon-green' : 'neon-pink'\" style=\"font-size: 28rpx;\">{{ balanceInfo.monthIncomeChange >= 0 ? 'arrow_upward' : 'arrow_downward' }}</text>\n          <text>{{ Math.abs(balanceInfo.monthIncomeChange || 0) }}% 较上月</text>\n        </view>\n      </view>\n      \n      <view class=\"stat-card\">\n        <view class=\"stat-title\">累计收益（元）</view>\n        <view class=\"stat-value\">{{ formatAmount(balanceInfo.totalIncome) }}</view>\n        <view class=\"stat-change positive\">\n          <text class=\"material-icons neon-blue\" style=\"font-size: 28rpx;\">trending_up</text>\n          <text>历史总收益</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <view class=\"action-btn primary\" @tap=\"showWithdrawModal\">\n        <text class=\"material-icons\">monetization_on</text>\n        <text>申请提现</text>\n      </view>\n      <view class=\"action-btn secondary\" @tap=\"showTransactionHistory\">\n        <text class=\"material-icons\">history</text>\n        <text>交易记录</text>\n      </view>\n      <view class=\"action-btn tertiary\" @tap=\"showBankCardManagement\">\n        <text class=\"material-icons\">credit_card</text>\n        <text>银行卡管理</text>\n      </view>\n    </view>\n    \n    <!-- 提现记录 -->\n    <view class=\"section\">\n      <view class=\"section-header\">\n        <view class=\"section-title\">提现记录</view>\n        <view class=\"section-action\" @tap=\"loadWithdrawRecords\">\n          <text class=\"material-icons\">refresh</text>\n        </view>\n      </view>\n      \n      <view class=\"withdraw-list\">\n        <!-- 加载中状态 -->\n        <view class=\"loading-state\" v-if=\"loading && withdrawRecords.length === 0\">\n          <text class=\"material-icons rotating\">sync</text>\n          <text>加载中...</text>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"!loading && withdrawRecords.length === 0\">\n          <text class=\"material-icons\">receipt_long</text>\n          <text>暂无提现记录</text>\n        </view>\n        \n        <!-- 提现记录列表 -->\n        <view \n          class=\"withdraw-item\" \n          v-for=\"(record, index) in withdrawRecords\" \n          :key=\"record.id || index\"\n        >\n          <view class=\"withdraw-info\">\n            <view class=\"withdraw-amount\">¥{{ formatAmount(record.amount) }}</view>\n            <view class=\"withdraw-time\">{{ formatDateTime(record.createTime) }}</view>\n          </view>\n          <view class=\"withdraw-status\" :class=\"getStatusClass(record.status)\">\n            {{ getStatusText(record.status) }}\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部导航栏 -->\n    <tab-bar current-tab=\"/pages/income/income\"></tab-bar>\n    \n    <!-- 提现模态框 -->\n    <view class=\"modal\" v-if=\"showModal === 'withdraw'\">\n      <view class=\"modal-backdrop\" @tap=\"hideModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <view class=\"modal-title\">申请提现</view>\n          <view class=\"modal-close\" @tap=\"hideModal\">\n            <text class=\"material-icons\">close</text>\n          </view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"form-group\">\n            <view class=\"form-label\">提现金额</view>\n            <view class=\"form-input-container\">\n              <input \n                class=\"form-input\" \n                type=\"digit\" \n                placeholder=\"请输入提现金额\" \n                v-model=\"withdrawAmount\"\n                @input=\"validateWithdrawAmount\"\n              />\n              <text class=\"input-unit\">元</text>\n            </view>\n            <view class=\"form-hint\">可提现余额：¥{{ formatAmount(balanceInfo.balance) }}</view>\n          </view>\n          \n          <view class=\"form-group\">\n            <view class=\"form-label\">银行卡</view>\n            <view class=\"bank-card-selector\" @tap=\"showBankCardModal\">\n              <view class=\"bank-card-info\" v-if=\"selectedBankCard\">\n                <view class=\"bank-name\">{{ selectedBankCard.bankName }}</view>\n                <view class=\"bank-account\">**** **** **** {{ selectedBankCard.bankAccount.slice(-4) }}</view>\n              </view>\n              <view class=\"no-bank-card\" v-else>\n                <text class=\"material-icons\">credit_card</text>\n                <text>请选择银行卡</text>\n              </view>\n              <text class=\"material-icons\">chevron_right</text>\n            </view>\n          </view>\n          \n          <view class=\"form-group\">\n            <view class=\"form-label\">提现密码</view>\n            <view class=\"form-input-container\">\n              <input \n                class=\"form-input\" \n                type=\"password\" \n                placeholder=\"请输入6位提现密码\" \n                v-model=\"withdrawPassword\"\n                maxlength=\"6\"\n              />\n            </view>\n            <view class=\"form-hint\">请输入6位数字提现密码</view>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <view class=\"btn btn-secondary\" @tap=\"hideModal\">取消</view>\n          <view class=\"btn btn-primary\" @tap=\"submitWithdraw\" :class=\"{ disabled: submitting }\">\n            <text v-if=\"submitting\">提交中...</text>\n            <text v-else>确认提现</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 银行卡选择模态框 -->\n    <view class=\"modal\" v-if=\"showModal === 'bankCard'\">\n      <view class=\"modal-backdrop\" @tap=\"hideModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <view class=\"modal-title\">选择银行卡</view>\n          <view class=\"modal-close\" @tap=\"hideModal\">\n            <text class=\"material-icons\">close</text>\n          </view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"bank-card-list\">\n            <!-- 加载中 -->\n            <view class=\"loading-state\" v-if=\"loadingBankCards\">\n              <text class=\"material-icons rotating\">sync</text>\n              <text>加载银行卡...</text>\n            </view>\n            \n            <!-- 空状态 -->\n            <view class=\"empty-state\" v-if=\"!loadingBankCards && bankCards.length === 0\">\n              <text class=\"material-icons\">credit_card</text>\n              <text>暂无银行卡</text>\n              <view class=\"add-bank-card-btn\" @tap=\"showAddBankCardModal\">\n                <text class=\"material-icons\">add</text>\n                <text>添加银行卡</text>\n              </view>\n            </view>\n            \n            <!-- 银行卡列表 -->\n            <view \n              class=\"bank-card-item\" \n              v-for=\"card in bankCards\" \n              :key=\"card.id\"\n              :class=\"{ selected: selectedBankCard && selectedBankCard.id === card.id }\"\n              @tap=\"selectBankCard(card)\"\n            >\n              <view class=\"bank-card-info\">\n                <view class=\"bank-name\">{{ card.bankName }}</view>\n                <view class=\"bank-account\">**** **** **** {{ card.bankAccount.slice(-4) }}</view>\n                <view class=\"account-name\">{{ card.accountName }}</view>\n              </view>\n              <view class=\"bank-card-action\">\n                <text class=\"material-icons\" v-if=\"selectedBankCard && selectedBankCard.id === card.id\">check_circle</text>\n                <text class=\"material-icons\" v-else>radio_button_unchecked</text>\n              </view>\n            </view>\n            \n            <!-- 添加银行卡按钮 -->\n            <view class=\"add-bank-card-btn\" @tap=\"showAddBankCardModal\" v-if=\"bankCards.length > 0\">\n              <text class=\"material-icons\">add</text>\n              <text>添加新银行卡</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <view class=\"btn btn-primary\" @tap=\"confirmBankCard\" :class=\"{ disabled: !selectedBankCard }\">\n            确认选择\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 银行卡管理模态框 -->\n    <view class=\"modal\" v-if=\"showModal === 'bankCardManagement'\">\n      <view class=\"modal-backdrop\" @tap=\"hideModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <view class=\"modal-title\">银行卡管理</view>\n          <view class=\"modal-close\" @tap=\"hideModal\">\n            <text class=\"material-icons\">close</text>\n          </view>\n        </view>\n\n        <view class=\"modal-body\">\n          <view class=\"bank-card-management\">\n            <!-- 银行卡列表 -->\n            <view class=\"bank-card-list\">\n              <!-- 加载中 -->\n              <view class=\"loading-state\" v-if=\"loadingBankCards\">\n                <text class=\"material-icons rotating\">sync</text>\n                <text>加载银行卡...</text>\n              </view>\n\n              <!-- 空状态 -->\n              <view class=\"empty-state\" v-if=\"!loadingBankCards && bankCards.length === 0\">\n                <text class=\"material-icons\">credit_card</text>\n                <text>暂无银行卡</text>\n                <text style=\"font-size: 24rpx; margin-top: 8rpx;\">请添加银行卡以便提现</text>\n              </view>\n\n              <!-- 银行卡列表 -->\n              <view\n                class=\"bank-card-management-item\"\n                v-for=\"card in bankCards\"\n                :key=\"card.id\"\n              >\n                <view class=\"bank-card-info\">\n                  <view class=\"bank-card-header\">\n                    <view class=\"bank-name\">{{ card.bankName }}</view>\n                    <view class=\"bank-default\" v-if=\"card.isDefault\">默认</view>\n                  </view>\n                  <view class=\"bank-account\">**** **** **** {{ card.bankAccount.slice(-4) }}</view>\n                  <view class=\"account-name\">{{ card.accountName }}</view>\n                </view>\n                <view class=\"bank-card-actions\">\n                  <view class=\"action-icon\" @tap=\"setDefaultBankCard(card)\" v-if=\"!card.isDefault\">\n                    <text class=\"material-icons\">star_border</text>\n                  </view>\n                  <view class=\"action-icon\" @tap=\"editBankCard(card)\">\n                    <text class=\"material-icons\">edit</text>\n                  </view>\n                  <view class=\"action-icon danger\" @tap=\"deleteBankCard(card)\">\n                    <text class=\"material-icons\">delete</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n\n            <!-- 添加银行卡按钮 -->\n            <view class=\"add-bank-card-btn\" @tap=\"showAddBankCardModal\">\n              <text class=\"material-icons\">add</text>\n              <text>添加新银行卡</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 添加银行卡模态框 -->\n    <view class=\"modal\" v-if=\"showModal === 'addBankCard'\">\n      <view class=\"modal-backdrop\" @tap=\"hideModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <view class=\"modal-title\">添加银行卡</view>\n          <view class=\"modal-close\" @tap=\"hideModal\">\n            <text class=\"material-icons\">close</text>\n          </view>\n        </view>\n\n        <view class=\"modal-body\">\n          <view class=\"form-group\">\n            <view class=\"form-label\">银行名称</view>\n            <view class=\"form-input-container\">\n              <input\n                class=\"form-input\"\n                type=\"text\"\n                placeholder=\"请输入银行名称\"\n                v-model=\"newBankCard.bankName\"\n              />\n            </view>\n          </view>\n\n          <view class=\"form-group\">\n            <view class=\"form-label\">银行卡号</view>\n            <view class=\"form-input-container\">\n              <input\n                class=\"form-input\"\n                type=\"text\"\n                placeholder=\"请输入银行卡号\"\n                v-model=\"newBankCard.bankAccount\"\n              />\n            </view>\n          </view>\n\n          <view class=\"form-group\">\n            <view class=\"form-label\">持卡人姓名</view>\n            <view class=\"form-input-container\">\n              <input\n                class=\"form-input\"\n                type=\"text\"\n                placeholder=\"请输入持卡人姓名\"\n                v-model=\"newBankCard.accountName\"\n              />\n            </view>\n          </view>\n\n          <view class=\"form-group\">\n            <view class=\"form-label\">身份证号</view>\n            <view class=\"form-input-container\">\n              <input\n                class=\"form-input\"\n                type=\"text\"\n                placeholder=\"请输入身份证号\"\n                v-model=\"newBankCard.idCard\"\n              />\n            </view>\n          </view>\n\n          <view class=\"form-group\">\n            <view class=\"form-label\">手机号</view>\n            <view class=\"form-input-container\">\n              <input\n                class=\"form-input\"\n                type=\"text\"\n                placeholder=\"请输入手机号\"\n                v-model=\"newBankCard.mobile\"\n              />\n            </view>\n          </view>\n\n          <view class=\"form-group\">\n            <view class=\"form-checkbox\">\n              <checkbox\n                :checked=\"newBankCard.isDefault\"\n                @change=\"toggleDefaultCard\"\n                color=\"#A875FF\"\n              />\n              <view class=\"checkbox-label\">设为默认银行卡</view>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"modal-footer\">\n          <view class=\"btn btn-secondary\" @tap=\"hideModal\">取消</view>\n          <view class=\"btn btn-primary\" @tap=\"addBankCard\" :class=\"{ disabled: submitting }\">\n            <text v-if=\"submitting\">添加中...</text>\n            <text v-else>确认添加</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport TabBar from '../../components/tab-bar.vue'\n\nexport default {\n  components: {\n    TabBar\n  },\n  data() {\n    return {\n      // 余额信息\n      balanceInfo: {\n        balance: 0,\n        todayIncome: 0,\n        monthIncome: 0,\n        totalIncome: 0,\n        todayIncomeChange: 0,\n        monthIncomeChange: 0\n      },\n      \n      // 提现相关\n      withdrawAmount: '',\n      withdrawPassword: '',\n      selectedBankCard: null,\n      bankCards: [],\n      withdrawRecords: [],\n      \n      // 状态控制\n      loading: false,\n      loadingBankCards: false,\n      submitting: false,\n      showModal: '',\n      \n      // 分页信息\n      pagination: {\n        page: 1,\n        size: 10,\n        total: 0\n      },\n\n      // 新银行卡信息\n      newBankCard: {\n        bankName: '',\n        bankAccount: '',\n        accountName: '',\n        idCard: '',\n        mobile: '',\n        isDefault: false\n      }\n    }\n  },\n  computed: {\n    // 格式化金额\n    formatAmount() {\n      return (amount) => {\n        if (!amount && amount !== 0) return '0.00';\n        return parseFloat(amount).toFixed(2);\n      }\n    }\n  },\n  onLoad() {\n    this.loadData();\n  },\n  methods: {\n    // 加载数据\n    loadData() {\n      this.loading = true;\n      Promise.all([\n        this.loadAccountInfo(),\n        this.loadWithdrawRecords(),\n        this.loadBankCards()\n      ]).finally(() => {\n        this.loading = false;\n      });\n    },\n\n    // 刷新数据\n    refreshData() {\n      uni.showLoading({ title: '刷新中...' });\n      this.loadData();\n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '数据已更新',\n          icon: 'success'\n        });\n      }, 1000);\n    },\n\n    // 加载账户信息\n    loadAccountInfo() {\n      return this.$api.finance.getAccount()\n        .then(res => {\n          this.balanceInfo = {\n            balance: res.data.balance || 0,\n            todayIncome: res.data.todayIncome || 0,\n            monthIncome: res.data.monthIncome || 0,\n            totalIncome: res.data.totalIncome || 0,\n            todayIncomeChange: res.data.todayIncomeChange || 0,\n            monthIncomeChange: res.data.monthIncomeChange || 0\n          };\n        })\n        .catch(err => {\n          console.error('获取账户信息失败', err);\n          // 使用模拟数据\n          this.balanceInfo = {\n            balance: 1234.56,\n            todayIncome: 89.50,\n            monthIncome: 2456.78,\n            totalIncome: 15678.90,\n            todayIncomeChange: 12.5,\n            monthIncomeChange: 8.3\n          };\n        });\n    },\n\n    // 加载提现记录\n    loadWithdrawRecords() {\n      return this.$api.finance.getWithdrawRecords({\n        page: this.pagination.page,\n        size: this.pagination.size\n      })\n        .then(res => {\n          const data = res.data || {};\n          this.withdrawRecords = data.records || [];\n          this.pagination.total = data.total || 0;\n        })\n        .catch(err => {\n          console.error('获取提现记录失败', err);\n          this.withdrawRecords = [];\n        });\n    },\n\n    // 加载银行卡列表\n    loadBankCards() {\n      this.loadingBankCards = true;\n      return this.$api.finance.getBankCards()\n        .then(res => {\n          this.bankCards = res.data || [];\n          // 自动选择默认银行卡\n          const defaultCard = this.bankCards.find(card => card.isDefault);\n          if (defaultCard) {\n            this.selectedBankCard = defaultCard;\n          }\n        })\n        .catch(err => {\n          console.error('获取银行卡列表失败', err);\n          this.bankCards = [];\n        })\n        .finally(() => {\n          this.loadingBankCards = false;\n        });\n    },\n\n    // 显示提现模态框\n    showWithdrawModal() {\n      this.showModal = 'withdraw';\n      this.withdrawAmount = '';\n      this.withdrawPassword = '';\n    },\n\n    // 显示银行卡选择模态框\n    showBankCardModal() {\n      this.showModal = 'bankCard';\n    },\n\n    // 显示交易记录\n    showTransactionHistory() {\n      uni.navigateTo({\n        url: '/pages/income/transaction-history'\n      });\n    },\n\n    // 显示银行卡管理\n    showBankCardManagement() {\n      this.showModal = 'bankCardManagement';\n    },\n\n    // 隐藏模态框\n    hideModal() {\n      this.showModal = '';\n    },\n\n    // 选择银行卡\n    selectBankCard(card) {\n      this.selectedBankCard = card;\n    },\n\n    // 确认银行卡选择\n    confirmBankCard() {\n      if (!this.selectedBankCard) {\n        uni.showToast({\n          title: '请选择银行卡',\n          icon: 'none'\n        });\n        return;\n      }\n      this.showModal = 'withdraw';\n    },\n\n    // 验证提现金额\n    validateWithdrawAmount() {\n      const amount = parseFloat(this.withdrawAmount);\n      const balance = parseFloat(this.balanceInfo.balance);\n\n      if (isNaN(amount) || amount <= 0) {\n        return false;\n      }\n\n      if (amount > balance) {\n        uni.showToast({\n          title: '提现金额不能超过余额',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      return true;\n    },\n\n    // 提交提现申请\n    submitWithdraw() {\n      if (this.submitting) return;\n\n      // 验证提现金额\n      if (!this.validateWithdrawAmount()) {\n        uni.showToast({\n          title: '请输入有效的提现金额',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 验证银行卡\n      if (!this.selectedBankCard) {\n        uni.showToast({\n          title: '请选择银行卡',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 验证提现密码\n      if (!this.withdrawPassword || this.withdrawPassword.length !== 6) {\n        uni.showToast({\n          title: '请输入6位提现密码',\n          icon: 'none'\n        });\n        return;\n      }\n\n      this.submitting = true;\n\n      // 调用提现API\n      this.$api.finance.withdraw({\n        amount: parseFloat(this.withdrawAmount),\n        bankCardId: this.selectedBankCard.id,\n        password: this.withdrawPassword,\n        remark: '门店提现'\n      })\n        .then(res => {\n          uni.showToast({\n            title: '提现申请已提交',\n            icon: 'success'\n          });\n\n          this.hideModal();\n          this.loadData(); // 重新加载数据\n        })\n        .catch(err => {\n          console.error('提现申请失败', err);\n\n          let errorMessage = '提现申请失败';\n          if (err.message) {\n            errorMessage = err.message;\n          } else if (err.data && err.data.message) {\n            errorMessage = err.data.message;\n          }\n\n          uni.showToast({\n            title: errorMessage,\n            icon: 'none',\n            duration: 3000\n          });\n        })\n        .finally(() => {\n          this.submitting = false;\n        });\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return '暂无记录';\n\n      try {\n        const date = new Date(dateTimeStr);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n\n        return `${year}-${month}-${day} ${hours}:${minutes}`;\n      } catch (error) {\n        return '时间格式错误';\n      }\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待审核',\n        'processing': '处理中',\n        'success': '成功',\n        'failed': '失败'\n      };\n      return statusMap[status] || '未知状态';\n    },\n\n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        'pending': 'status-pending',\n        'processing': 'status-processing',\n        'success': 'status-success',\n        'failed': 'status-failed'\n      };\n      return classMap[status] || '';\n    },\n\n    // 显示添加银行卡模态框\n    showAddBankCardModal() {\n      this.showModal = 'addBankCard';\n      this.resetNewBankCard();\n    },\n\n    // 重置新银行卡表单\n    resetNewBankCard() {\n      this.newBankCard = {\n        bankName: '',\n        bankAccount: '',\n        accountName: '',\n        idCard: '',\n        mobile: '',\n        isDefault: false\n      };\n    },\n\n    // 切换默认卡设置\n    toggleDefaultCard(e) {\n      this.newBankCard.isDefault = e.detail.value;\n    },\n\n    // 添加银行卡\n    addBankCard() {\n      if (this.submitting) return;\n\n      // 验证表单\n      if (!this.validateBankCardForm()) {\n        return;\n      }\n\n      this.submitting = true;\n\n      // 调用API添加银行卡\n      this.$api.finance.addBankCard(this.newBankCard)\n        .then(res => {\n          uni.showToast({\n            title: '银行卡添加成功',\n            icon: 'success'\n          });\n\n          this.hideModal();\n          this.loadBankCards(); // 重新加载银行卡列表\n        })\n        .catch(err => {\n          console.error('添加银行卡失败', err);\n\n          let errorMessage = '添加银行卡失败';\n          if (err.message) {\n            errorMessage = err.message;\n          } else if (err.data && err.data.message) {\n            errorMessage = err.data.message;\n          }\n\n          uni.showToast({\n            title: errorMessage,\n            icon: 'none',\n            duration: 3000\n          });\n        })\n        .finally(() => {\n          this.submitting = false;\n        });\n    },\n\n    // 验证银行卡表单\n    validateBankCardForm() {\n      if (!this.newBankCard.bankName.trim()) {\n        uni.showToast({\n          title: '请输入银行名称',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      if (!this.newBankCard.bankAccount.trim()) {\n        uni.showToast({\n          title: '请输入银行卡号',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      if (!this.newBankCard.accountName.trim()) {\n        uni.showToast({\n          title: '请输入持卡人姓名',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      if (!this.newBankCard.idCard.trim()) {\n        uni.showToast({\n          title: '请输入身份证号',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      if (!this.newBankCard.mobile.trim()) {\n        uni.showToast({\n          title: '请输入手机号',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      // 验证身份证号格式\n      const idCardRegex = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\n      if (!idCardRegex.test(this.newBankCard.idCard)) {\n        uni.showToast({\n          title: '身份证号格式不正确',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      // 验证手机号格式\n      const mobileRegex = /^1[3-9]\\d{9}$/;\n      if (!mobileRegex.test(this.newBankCard.mobile)) {\n        uni.showToast({\n          title: '手机号格式不正确',\n          icon: 'none'\n        });\n        return false;\n      }\n\n      return true;\n    },\n\n    // 设置默认银行卡\n    setDefaultBankCard(card) {\n      uni.showModal({\n        title: '确认操作',\n        content: `确定要将${card.bankName}(${card.bankAccount.slice(-4)})设为默认银行卡吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            this.$api.finance.setDefaultBankCard(card.id)\n              .then(() => {\n                uni.showToast({\n                  title: '设置成功',\n                  icon: 'success'\n                });\n                this.loadBankCards();\n              })\n              .catch(err => {\n                console.error('设置默认银行卡失败', err);\n                uni.showToast({\n                  title: '设置失败',\n                  icon: 'none'\n                });\n              });\n          }\n        }\n      });\n    },\n\n    // 编辑银行卡\n    editBankCard(card) {\n      uni.showToast({\n        title: '编辑功能开发中',\n        icon: 'none'\n      });\n    },\n\n    // 删除银行卡\n    deleteBankCard(card) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除${card.bankName}(${card.bankAccount.slice(-4)})吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            this.$api.finance.deleteBankCard(card.id)\n              .then(() => {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                });\n                this.loadBankCards();\n              })\n              .catch(err => {\n                console.error('删除银行卡失败', err);\n                uni.showToast({\n                  title: '删除失败',\n                  icon: 'none'\n                });\n              });\n          }\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* CSS变量定义 */\n:root {\n  --bg-dark: #121212;\n  --bg-card: rgba(255, 255, 255, 0.05);\n  --text-primary: #FFFFFF;\n  --text-secondary: rgba(255, 255, 255, 0.7);\n  --text-tertiary: rgba(255, 255, 255, 0.5);\n  --primary: #9527C6;\n  --primary-dark: #6E3AD9;\n  --primary-light: #A875FF;\n  --border-light: rgba(255, 255, 255, 0.1);\n  --radius-sm: 8rpx;\n  --radius-md: 12rpx;\n  --radius-lg: 16rpx;\n  --space-xs: 8rpx;\n  --space-sm: 16rpx;\n  --space-md: 24rpx;\n  --space-lg: 32rpx;\n}\n\n/* 基础容器样式 */\n.container {\n  min-height: 100vh;\n  padding: 30rpx 30rpx 150rpx 30rpx;\n  background-color: var(--bg-dark);\n  color: var(--text-primary);\n  background-image:\n    radial-gradient(circle at top right, rgba(167, 86, 229, 0.15) 0%, transparent 35%),\n    radial-gradient(circle at center left, rgba(0, 210, 255, 0.08) 0%, transparent 30%),\n    radial-gradient(circle at bottom center, rgba(255, 44, 146, 0.1) 0%, transparent 40%);\n}\n\n.top-spacing {\n  height: 60rpx;\n}\n\n/* 页面头部样式 */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: var(--space-lg);\n}\n\n.header-content {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 48rpx;\n  font-weight: 700;\n  margin-bottom: var(--space-xs);\n  background: linear-gradient(to right, #FFFFFF, var(--primary-light));\n  -webkit-background-clip: text;\n  background-clip: text;\n  -webkit-text-fill-color: transparent;\n  text-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);\n}\n\n.page-subtitle {\n  font-size: 28rpx;\n  color: var(--text-secondary);\n  line-height: 1.4;\n}\n\n.refresh-btn {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 24rpx;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: var(--radius-md);\n  border: 1px solid var(--border-light);\n  color: var(--text-secondary);\n  font-size: 24rpx;\n  transition: all 0.3s ease;\n}\n\n.refresh-btn:active {\n  transform: scale(0.95);\n  background: rgba(168, 117, 255, 0.1);\n}\n\n/* 统计卡片网格 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20rpx;\n  margin-bottom: var(--space-lg);\n}\n\n.stat-card {\n  background: var(--bg-card);\n  border-radius: var(--radius-lg);\n  padding: 24rpx;\n  border: 1px solid var(--border-light);\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-4rpx);\n  box-shadow: 0 8rpx 32rpx rgba(168, 117, 255, 0.2);\n}\n\n.stat-title {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n  margin-bottom: 12rpx;\n}\n\n.stat-value {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: 8rpx;\n}\n\n.stat-change {\n  display: flex;\n  align-items: center;\n  font-size: 20rpx;\n  gap: 4rpx;\n}\n\n.stat-change.positive {\n  color: #4CAF50;\n}\n\n.stat-change.negative {\n  color: #F44336;\n}\n\n/* 操作按钮 */\n.action-buttons {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 16rpx;\n  margin-bottom: 40rpx;\n  padding: 0 8rpx;\n}\n\n.action-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  padding: 20rpx 16rpx;\n  border-radius: var(--radius-md);\n  font-size: 24rpx;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  min-height: 120rpx;\n}\n\n.action-btn.primary {\n  background: linear-gradient(135deg, var(--primary-light), var(--primary));\n  color: #FFFFFF;\n  box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.3);\n}\n\n.action-btn.primary:active {\n  transform: scale(0.95);\n  box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.4);\n}\n\n.action-btn.secondary {\n  background: rgba(168, 117, 255, 0.1);\n  color: var(--primary-light);\n  border: 1px solid rgba(168, 117, 255, 0.3);\n}\n\n.action-btn.secondary:active {\n  transform: scale(0.95);\n  background: rgba(168, 117, 255, 0.2);\n}\n\n.action-btn.tertiary {\n  background: rgba(255, 255, 255, 0.05);\n  color: var(--text-primary);\n  border: 1px solid var(--border-light);\n}\n\n.action-btn.tertiary:active {\n  transform: scale(0.95);\n  background: rgba(255, 255, 255, 0.1);\n}\n\n/* 区块样式 */\n.section {\n  margin-bottom: var(--space-lg);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--space-md);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.section-action {\n  padding: 12rpx;\n  color: var(--primary-light);\n  font-size: 32rpx;\n}\n\n/* 提现记录列表 */\n.withdraw-list {\n  background: var(--bg-card);\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--border-light);\n  overflow: hidden;\n}\n\n.withdraw-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx;\n  border-bottom: 1px solid var(--border-light);\n}\n\n.withdraw-item:last-child {\n  border-bottom: none;\n}\n\n.withdraw-info {\n  flex: 1;\n}\n\n.withdraw-amount {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 8rpx;\n}\n\n.withdraw-time {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n}\n\n.withdraw-status {\n  padding: 8rpx 16rpx;\n  border-radius: var(--radius-sm);\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.status-pending {\n  background: rgba(255, 193, 7, 0.2);\n  color: #FFC107;\n}\n\n.status-processing {\n  background: rgba(33, 150, 243, 0.2);\n  color: #2196F3;\n}\n\n.status-success {\n  background: rgba(76, 175, 80, 0.2);\n  color: #4CAF50;\n}\n\n.status-failed {\n  background: rgba(244, 67, 54, 0.2);\n  color: #F44336;\n}\n\n/* 加载和空状态 */\n.loading-state,\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60rpx 20rpx;\n  color: var(--text-secondary);\n  text-align: center;\n}\n\n.loading-state .material-icons,\n.empty-state .material-icons {\n  font-size: 80rpx;\n  color: var(--text-tertiary);\n  margin-bottom: 16rpx;\n}\n\n.rotating {\n  animation: rotate 2s linear infinite;\n}\n\n@keyframes rotate {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 霓虹灯效果 */\n.neon-green {\n  color: #4CAF50;\n  text-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);\n}\n\n.neon-pink {\n  color: #E91E63;\n  text-shadow: 0 0 10rpx rgba(233, 30, 99, 0.5);\n}\n\n.neon-blue {\n  color: #2196F3;\n  text-shadow: 0 0 10rpx rgba(33, 150, 243, 0.5);\n}\n\n/* 模态框样式 */\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.modal-backdrop {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n}\n\n.modal-content {\n  position: relative;\n  width: 90%;\n  max-width: 580rpx;\n  max-height: 85vh;\n  background: var(--bg-dark);\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--border-light);\n  overflow: hidden;\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.6);\n  backdrop-filter: blur(20px);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 28rpx;\n  border-bottom: 1px solid var(--border-light);\n}\n\n.modal-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.modal-close {\n  padding: 8rpx;\n  color: var(--text-secondary);\n  font-size: 28rpx;\n  transition: all 0.3s ease;\n}\n\n.modal-close:active {\n  transform: scale(0.9);\n  color: var(--text-primary);\n}\n\n.modal-body {\n  padding: 24rpx 28rpx;\n  max-height: 65vh;\n  overflow-y: auto;\n}\n\n.modal-footer {\n  display: flex;\n  gap: 16rpx;\n  padding: 20rpx 28rpx 24rpx;\n  border-top: 1px solid var(--border-light);\n}\n\n/* 表单样式 */\n.form-group {\n  margin-bottom: 24rpx;\n}\n\n.form-group:last-child {\n  margin-bottom: 0;\n}\n\n.form-label {\n  font-size: 26rpx;\n  color: var(--text-primary);\n  margin-bottom: 10rpx;\n  font-weight: 500;\n  display: block;\n}\n\n.form-input-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n}\n\n.form-input-container:focus-within {\n  transform: translateY(-1rpx);\n}\n\n.form-input {\n  flex: 1;\n  padding: 16rpx 20rpx;\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid var(--border-light);\n  border-radius: var(--radius-md);\n  color: var(--text-primary);\n  font-size: 26rpx;\n  line-height: 1.4;\n  transition: all 0.3s ease;\n  min-height: 44rpx;\n}\n\n.form-input:focus {\n  border-color: var(--primary-light);\n  background: rgba(255, 255, 255, 0.12);\n  box-shadow: 0 0 0 3rpx rgba(168, 117, 255, 0.15);\n  outline: none;\n}\n\n.form-input::placeholder {\n  color: var(--text-tertiary);\n  font-size: 24rpx;\n}\n\n.input-unit {\n  position: absolute;\n  right: 20rpx;\n  color: var(--text-secondary);\n  font-size: 24rpx;\n}\n\n.form-hint {\n  margin-top: 6rpx;\n  font-size: 22rpx;\n  color: var(--text-tertiary);\n  line-height: 1.3;\n}\n\n/* 银行卡选择器 */\n.bank-card-selector {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid var(--border-light);\n  border-radius: var(--radius-md);\n  transition: all 0.3s ease;\n}\n\n.bank-card-selector:active {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.bank-card-info {\n  flex: 1;\n}\n\n.bank-name {\n  font-size: 28rpx;\n  color: var(--text-primary);\n  margin-bottom: 4rpx;\n}\n\n.bank-account {\n  font-size: 24rpx;\n  color: var(--text-secondary);\n}\n\n.no-bank-card {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n  color: var(--text-secondary);\n  font-size: 28rpx;\n}\n\n/* 银行卡列表 */\n.bank-card-list {\n  max-height: 400rpx;\n  overflow-y: auto;\n}\n\n.bank-card-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 24rpx;\n  margin-bottom: 16rpx;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid var(--border-light);\n  border-radius: var(--radius-md);\n  transition: all 0.3s ease;\n}\n\n.bank-card-item.selected {\n  background: rgba(168, 117, 255, 0.1);\n  border-color: var(--primary-light);\n}\n\n.bank-card-item:active {\n  transform: scale(0.98);\n}\n\n.account-name {\n  font-size: 24rpx;\n  color: var(--text-tertiary);\n  margin-top: 4rpx;\n}\n\n.bank-card-action .material-icons {\n  font-size: 32rpx;\n  color: var(--primary-light);\n}\n\n.add-bank-card-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12rpx;\n  padding: 24rpx;\n  margin-top: 20rpx;\n  background: rgba(168, 117, 255, 0.1);\n  border: 1px dashed var(--primary-light);\n  border-radius: var(--radius-md);\n  color: var(--primary-light);\n  font-size: 28rpx;\n  transition: all 0.3s ease;\n}\n\n.add-bank-card-btn:active {\n  transform: scale(0.95);\n  background: rgba(168, 117, 255, 0.2);\n}\n\n/* 按钮样式 */\n.btn {\n  flex: 1;\n  padding: 16rpx 24rpx;\n  border-radius: var(--radius-md);\n  text-align: center;\n  font-size: 26rpx;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 80rpx;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, var(--primary-light), var(--primary));\n  color: #FFFFFF;\n  box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.3);\n}\n\n.btn-primary:active {\n  transform: scale(0.95);\n  box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.4);\n}\n\n.btn-primary.disabled {\n  opacity: 0.6;\n  pointer-events: none;\n}\n\n.btn-secondary {\n  background: rgba(255, 255, 255, 0.05);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-light);\n}\n\n.btn-secondary:active {\n  transform: scale(0.95);\n  background: rgba(255, 255, 255, 0.1);\n}\n\n/* 银行卡管理样式 */\n.bank-card-management {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.bank-card-management-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 24rpx;\n  margin-bottom: 16rpx;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid var(--border-light);\n  border-radius: var(--radius-md);\n  transition: all 0.3s ease;\n}\n\n.bank-card-management-item:hover {\n  background: rgba(255, 255, 255, 0.08);\n}\n\n.bank-card-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.bank-default {\n  font-size: 20rpx;\n  color: var(--primary-light);\n  background: rgba(168, 117, 255, 0.2);\n  padding: 4rpx 8rpx;\n  border-radius: 4rpx;\n  margin-left: 12rpx;\n}\n\n.bank-card-actions {\n  display: flex;\n  gap: 16rpx;\n}\n\n.action-icon {\n  padding: 12rpx;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--text-secondary);\n  font-size: 32rpx;\n  transition: all 0.3s ease;\n}\n\n.action-icon:active {\n  transform: scale(0.9);\n}\n\n.action-icon.danger {\n  color: #F44336;\n}\n\n.action-icon.danger:active {\n  background: rgba(244, 67, 54, 0.2);\n}\n\n/* 表单复选框样式 */\n.form-checkbox {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 0;\n  background: rgba(255, 255, 255, 0.03);\n  border-radius: var(--radius-md);\n  padding: 16rpx 20rpx;\n  border: 1px solid var(--border-light);\n  transition: all 0.3s ease;\n}\n\n.form-checkbox:active {\n  background: rgba(255, 255, 255, 0.06);\n}\n\n.checkbox-label {\n  margin-left: 12rpx;\n  font-size: 26rpx;\n  color: var(--text-primary);\n  flex: 1;\n}\n\n/* 响应式调整 */\n@media (max-width: 750rpx) {\n  .action-buttons {\n    grid-template-columns: 1fr;\n    gap: 12rpx;\n  }\n\n  .action-btn {\n    flex-direction: row;\n    min-height: auto;\n    padding: 16rpx 20rpx;\n  }\n\n  .action-btn .material-icons {\n    margin-right: 8rpx;\n    margin-bottom: 0;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./income.vue?vue&type=style&index=0&id=4a11fda6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./income.vue?vue&type=style&index=0&id=4a11fda6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753035246040\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}