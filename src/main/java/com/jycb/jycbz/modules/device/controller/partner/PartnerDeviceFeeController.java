package com.jycb.jycbz.modules.device.controller.partner;

import cn.dev33.satoken.stp.StpUtil;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.modules.device.service.DeviceFeeService;
import com.jycb.jycbz.modules.device.vo.DeviceFeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合作商端-设备费用控制器
 */
@RestController
@RequestMapping("/api/partner/device/fee")
@RequiredArgsConstructor
@Tag(name = "合作商端-设备费用API")
public class PartnerDeviceFeeController {

    private final DeviceFeeService deviceFeeService;

    /**
     * 获取设备费用配置列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取设备费用配置列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备费用配置列表"
    )
    public Result<PageResult<DeviceFeeVO>> getDeviceFeeList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页条数") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "费用名称") @RequestParam(required = false) String feeName,
            @Parameter(description = "费用类型：1-按时间 2-按次数 3-包天") @RequestParam(required = false) Integer feeType,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "状态：1-启用 0-禁用") @RequestParam(required = false) Integer status
    ) {
        // 获取当前登录的合作商ID
        Integer partnerId = getCurrentPartnerId();
        return Result.success(deviceFeeService.getDeviceFeeList(page, size, feeName, feeType, null, partnerId, shopId, status));
    }

    /**
     * 获取设备费用配置详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取设备费用配置详情")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备费用配置详情",
            targetId = "#id"
    )
    public Result<DeviceFeeVO> getDeviceFeeDetail(@PathVariable Integer id) {
        return Result.success(deviceFeeService.getDeviceFeeDetail(id));
    }

    /**
     * 添加设备费用配置
     */
    @PostMapping
    @Operation(summary = "添加设备费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.CREATE,
            description = "添加设备费用配置"
    )
    public Result<Void> addDeviceFee(@RequestBody @Valid DeviceFee deviceFee) {
        // 设置当前合作商ID
        deviceFee.setPartnerId(getCurrentPartnerId());
        deviceFeeService.addDeviceFee(deviceFee);
        return Result.success();
    }

    /**
     * 更新设备费用配置
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新设备费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备费用配置",
            targetId = "#id"
    )
    public Result<Void> updateDeviceFee(@PathVariable Integer id, @RequestBody @Valid DeviceFee deviceFee) {
        deviceFee.setId(id);
        // 设置当前合作商ID
        deviceFee.setPartnerId(getCurrentPartnerId());
        deviceFeeService.updateDeviceFee(deviceFee);
        return Result.success();
    }

    /**
     * 删除设备费用配置
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.DELETE,
            description = "删除设备费用配置",
            targetId = "#id"
    )
    public Result<Void> deleteDeviceFee(@PathVariable Integer id) {
        deviceFeeService.deleteDeviceFee(id);
        return Result.success();
    }

    /**
     * 更新设备费用配置状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新设备费用配置状态")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备费用配置状态",
            targetId = "#id"
    )
    public Result<Void> updateDeviceFeeStatus(
            @PathVariable Integer id,
            @Parameter(description = "状态：1-启用 0-禁用") @RequestParam Integer status
    ) {
        deviceFeeService.updateDeviceFeeStatus(id, status);
        return Result.success();
    }

    /**
     * 设置默认费用配置
     */
    @PutMapping("/{id}/default")
    @Operation(summary = "设置默认费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "设置默认费用配置",
            targetId = "#id"
    )
    public Result<Void> setDefaultDeviceFee(@PathVariable Integer id) {
        deviceFeeService.setDefaultDeviceFee(id);
        return Result.success();
    }

    /**
     * 获取设备的费用配置
     */
    @GetMapping("/device/{deviceId}")
    @Operation(summary = "获取设备的费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备的费用配置",
            targetId = "#deviceId"
    )
    public Result<DeviceFeeVO> getDeviceFeeByDeviceId(@PathVariable Integer deviceId) {
        return Result.success(deviceFeeService.getDeviceFeeVOByDeviceId(deviceId));
    }

    /**
     * 获取门店的费用配置列表
     */
    @GetMapping("/shop/{shopId}")
    @Operation(summary = "获取门店的费用配置列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询门店的费用配置列表",
            targetId = "#shopId"
    )
    public Result<List<DeviceFeeVO>> getDeviceFeesByShopId(@PathVariable Integer shopId) {
        return Result.success(deviceFeeService.getDeviceFeesByShopId(shopId));
    }

    /**
     * 获取当前登录的合作商ID
     */
    private Integer getCurrentPartnerId() {
        // 从当前登录用户的Session中获取合作商ID
        if (!StpUtil.isLogin()) {
            throw new BusinessException("用户未登录");
        }
        
        Object partnerIdObj = StpUtil.getSession().get("partnerId");
        if (partnerIdObj == null) {
            throw new BusinessException("当前用户未关联合作商");
        }
        
        return Integer.valueOf(partnerIdObj.toString());
    }
} 