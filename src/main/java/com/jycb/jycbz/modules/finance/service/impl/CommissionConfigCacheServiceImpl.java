package com.jycb.jycbz.modules.finance.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jycb.jycbz.modules.finance.entity.CommissionConfig;
import com.jycb.jycbz.modules.finance.service.CommissionConfigCacheService;
import com.jycb.jycbz.modules.finance.service.CommissionConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 佣金配置缓存服务实现类
 */
@Service
@Slf4j
public class CommissionConfigCacheServiceImpl implements CommissionConfigCacheService {

    @Autowired
    @Qualifier("commissionConfigServiceImplBasic")
    private CommissionConfigService commissionConfigService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_KEY_PREFIX = "commission:config:";
    private static final long CACHE_EXPIRE_TIME = 1; // 缓存过期时间（小时）
    
    @Override
    public CommissionConfig getShopConfigWithCache(Integer shopId) {
        if (shopId == null) {
            return null;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "shop:" + shopId;
        
        // 尝试从缓存获取
        CommissionConfig config = getFromCache(cacheKey);
        if (config != null) {
            return config;
        }
        
        // 缓存未命中，从数据库查询
        config = commissionConfigService.getByShop(shopId);
        
        // 存入缓存
        if (config != null) {
            saveToCache(cacheKey, config);
        }
        
        return config;
    }

    @Override
    public CommissionConfig getPartnerConfigWithCache(Integer partnerId) {
        if (partnerId == null) {
            return null;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "partner:" + partnerId;
        
        // 尝试从缓存获取
        CommissionConfig config = getFromCache(cacheKey);
        if (config != null) {
            return config;
        }
        
        // 缓存未命中，从数据库查询
        config = commissionConfigService.getByPartner(partnerId);
        
        // 存入缓存
        if (config != null) {
            saveToCache(cacheKey, config);
        }
        
        return config;
    }

    @Override
    public CommissionConfig getEntityConfigWithCache(Integer entityId) {
        if (entityId == null) {
            return null;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "entity:" + entityId;
        
        // 尝试从缓存获取
        CommissionConfig config = getFromCache(cacheKey);
        if (config != null) {
            return config;
        }
        
        // 缓存未命中，从数据库查询
        config = commissionConfigService.getByEntity(entityId);
        
        // 存入缓存
        if (config != null) {
            saveToCache(cacheKey, config);
        }
        
        return config;
    }

    @Override
    public CommissionConfig getSystemConfigWithCache() {
        String cacheKey = CACHE_KEY_PREFIX + "system";
        
        // 尝试从缓存获取
        CommissionConfig config = getFromCache(cacheKey);
        if (config != null) {
            return config;
        }
        
        // 缓存未命中，从数据库查询
        config = commissionConfigService.getSystemConfig();
        
        // 存入缓存
        if (config != null) {
            saveToCache(cacheKey, config);
        }
        
        return config;
    }

    @Override
    public void clearShopConfigCache(Integer shopId) {
        if (shopId == null) {
            return;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "shop:" + shopId;
        redisTemplate.delete(cacheKey);
        log.info("已清除门店配置缓存，门店ID：{}", shopId);
    }

    @Override
    public void clearPartnerConfigCache(Integer partnerId) {
        if (partnerId == null) {
            return;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "partner:" + partnerId;
        redisTemplate.delete(cacheKey);
        log.info("已清除合作商配置缓存，合作商ID：{}", partnerId);
    }

    @Override
    public void clearEntityConfigCache(Integer entityId) {
        if (entityId == null) {
            return;
        }
        
        String cacheKey = CACHE_KEY_PREFIX + "entity:" + entityId;
        redisTemplate.delete(cacheKey);
        log.info("已清除业务主体配置缓存，业务主体ID：{}", entityId);
    }

    @Override
    public void clearSystemConfigCache() {
        String cacheKey = CACHE_KEY_PREFIX + "system";
        redisTemplate.delete(cacheKey);
        log.info("已清除系统配置缓存");
    }

    @Override
    public void clearAllConfigCache() {
        Set<String> keys = redisTemplate.keys(CACHE_KEY_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
            log.info("已清除所有配置缓存，共 {} 条", keys.size());
        }
    }
    
    /**
     * 从缓存中获取配置
     */
    @SuppressWarnings("unchecked")
    private CommissionConfig getFromCache(String cacheKey) {
        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                if (cachedValue instanceof CommissionConfig) {
                    return (CommissionConfig) cachedValue;
                } else {
                    // 如果是Map，转换为对象
                    ObjectMapper objectMapper = new ObjectMapper();
                    return objectMapper.convertValue(cachedValue, CommissionConfig.class);
                }
            }
        } catch (Exception e) {
            log.warn("从缓存获取分成配置失败，缓存键：{}，错误：{}，将直接查询数据库", cacheKey, e.getMessage());
        }
        return null;
    }
    
    /**
     * 保存配置到缓存
     */
    private void saveToCache(String cacheKey, CommissionConfig config) {
        try {
            redisTemplate.opsForValue().set(cacheKey, config, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("存储分成配置到缓存失败，缓存键：{}，错误：{}，数据库查询结果仍然有效", cacheKey, e.getMessage());
        }
    }
} 