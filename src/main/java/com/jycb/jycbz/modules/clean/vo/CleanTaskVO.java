package com.jycb.jycbz.modules.clean.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 清洁任务视图对象
 */
@Data
@Schema(description = "清洁任务信息")
public class CleanTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "任务ID")
    private Integer id;

    @Schema(description = "设备ID")
    private Integer deviceId;

    @Schema(description = "设备编号")
    private String deviceNo;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "门店名称")
    private String shopName;

    @Schema(description = "卫生员ID")
    private Integer cleanerId;

    @Schema(description = "卫生员姓名")
    private String cleanerName;

    @Schema(description = "任务类型：1-常规清洁 2-紧急清洁 3-投诉处理")
    private Integer taskType;

    @Schema(description = "任务类型名称")
    private String taskTypeName;

    @Schema(description = "任务状态：0-待处理 1-处理中 2-已完成 3-已取消")
    private Integer taskStatus;

    @Schema(description = "任务状态名称")
    private String taskStatusName;

    @Schema(description = "计划清洁时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planTime;

    @Schema(description = "开始清洁时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "完成清洁时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "清洁耗时（分钟）")
    private Integer duration;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "清洁前图片，多张以逗号分隔")
    private String imagesBefore;

    @Schema(description = "清洁后图片，多张以逗号分隔")
    private String imagesAfter;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否超时")
    private Boolean isOverdue;

    @Schema(description = "完成率（百分比）")
    private Double completionRate;
}
