package com.jycb.jycbz.modules.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.modules.device.entity.DeviceFeeRule;
import com.jycb.jycbz.modules.device.mapper.DeviceFeeRuleMapper;
import com.jycb.jycbz.modules.device.service.DeviceFeeRuleService;
import com.jycb.jycbz.modules.device.vo.DeviceFeeRuleVO;
import com.jycb.jycbz.modules.device.convert.DeviceFeeRuleConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备费用规则服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceFeeRuleServiceImpl extends ServiceImpl<DeviceFeeRuleMapper, DeviceFeeRule> implements DeviceFeeRuleService {

    private final DeviceFeeRuleMapper deviceFeeRuleMapper;

    @Override
    public List<DeviceFeeRuleVO> getRulesByFeeId(Integer feeId) {
        if (feeId == null) {
            return List.of();
        }

        LambdaQueryWrapper<DeviceFeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFeeRule::getFeeId, feeId)
                   .orderByAsc(DeviceFeeRule::getTimeRange);

        List<DeviceFeeRule> rules = list(queryWrapper);
        return rules.stream()
                   .map(DeviceFeeRuleConvert.INSTANCE::convert)
                   .collect(Collectors.toList());
    }

    @Override
    public boolean addFeeRule(DeviceFeeRule feeRule) {
        if (feeRule == null) {
            return false;
        }
        return save(feeRule);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddFeeRules(Integer feeId, List<DeviceFeeRule> feeRules) {
        if (feeId == null || feeRules == null || feeRules.isEmpty()) {
            return false;
        }

        // 设置费用配置ID
        feeRules.forEach(rule -> rule.setFeeId(feeId));

        return saveBatch(feeRules);
    }

    @Override
    public boolean updateFeeRule(DeviceFeeRule feeRule) {
        if (feeRule == null || feeRule.getId() == null) {
            return false;
        }
        return updateById(feeRule);
    }

    @Override
    public boolean deleteFeeRule(Integer id) {
        if (id == null) {
            return false;
        }
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRulesByFeeId(Integer feeId) {
        if (feeId == null) {
            return false;
        }

        LambdaQueryWrapper<DeviceFeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFeeRule::getFeeId, feeId);

        return remove(queryWrapper);
    }
}
