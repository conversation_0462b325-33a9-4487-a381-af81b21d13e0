package com.jycb.jycbz.modules.order.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.constant.RoleConstants;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 合作商订单控制器
 * 用于合作商管理旗下订单
 */
@Slf4j
@RestController
@RequestMapping("/partner/order")
@Tag(name = "合作商订单管理", description = "合作商管理旗下订单相关接口")
@RequiredArgsConstructor
@SaCheckRole(RoleConstants.PARTNER_ADMIN)
@Validated
public class PartnerOrderController {

    private final OrderService orderService;
    private final ShopService shopService;

    /**
     * 获取订单列表
     *
     * @param orderNo 订单编号
     * @param deviceNo 设备编号
     * @param orderStatus 订单状态
     * @param payStatus 支付状态
     * @param shopId 门店ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 订单列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取订单列表", description = "分页获取合作商旗下订单列表")
    @Auditable(
        module = AuditConstants.Module.ORDER,
        operation = AuditConstants.Operation.QUERY,
        description = "合作商查询订单列表",
        targetType = AuditConstants.TargetType.ORDER
    )
    public Result<PageResult<Order>> getOrderList(
            @Parameter(description = "订单编号") @RequestParam(required = false) String orderNo,
            @Parameter(description = "设备编号") @RequestParam(required = false) String deviceNo,
            @Parameter(description = "订单状态：1-进行中 2-已完成 3-已取消 4-已退款") @RequestParam(required = false) Integer orderStatus,
            @Parameter(description = "支付状态：0-未支付 1-已支付") @RequestParam(required = false) Integer payStatus,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int pageSize) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 构建分页对象
        Page<Order> page = new Page<>(pageNum, pageSize);
        
        // 查询订单列表
        var resultPage = orderService.pageOrders(page, orderNo, deviceNo, null, 
                                          orderStatus, payStatus, null, 
                                          partnerId, shopId, startTime, endTime);
        
        // 返回结果
        return Result.success(PageResult.build(resultPage));
    }

    /**
     * 获取订单详情
     *
     * @param orderNo 订单编号
     * @return 订单详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取订单详情", description = "根据订单编号获取订单详情")
    @Auditable(
        module = AuditConstants.Module.ORDER,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询订单详情",
        targetType = AuditConstants.TargetType.ORDER
    )
    public Result<Order> getOrderDetail(
            @Parameter(description = "订单编号") @RequestParam String orderNo) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 获取订单详情
        Order order = orderService.getOrderByOrderNo(orderNo);
        
        if (order == null) {
            return Result.failed("订单不存在");
        }
        
        // 验证订单是否属于当前合作商
        if (!partnerId.equals(order.getPartnerId())) {
            return Result.failed("您无权查看该订单");
        }
        
        return Result.success(order);
    }

    /**
     * 获取订单统计数据
     *
     * @param shopId 门店ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取订单统计数据", description = "获取合作商旗下订单统计数据")
    @Auditable(
        module = AuditConstants.Module.ORDER,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询订单统计数据",
        targetType = AuditConstants.TargetType.ORDER
    )
    public Result<Map<String, Object>> getOrderStatistics(
            @Parameter(description = "门店ID") @RequestParam(required = false) Long shopId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        // 获取当前登录的合作商ID
        Long partnerId = Long.valueOf(getPartnerIdFromToken());
        
        // 验证门店是否属于当前合作商
        if (shopId != null) {
            Shop shop = shopService.getById(shopId);
            if (shop == null) {
                return Result.failed("门店不存在");
            }
            
            if (!partnerId.equals(shop.getPartnerId())) {
                return Result.failed("无权访问非本合作商的门店数据");
            }
        }
        
        // 处理时间参数，如果为空则使用默认值（最近30天）
        if (startTime == null || endTime == null) {
            endTime = LocalDateTime.now();
            startTime = endTime.minusDays(30);
        }

        // 格式化时间为字符串
        String startTimeStr = startTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = endTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 调用服务获取订单统计数据
        Map<String, Object> statistics = orderService.getPartnerOrderStatistics(partnerId, shopId, startTimeStr, endTimeStr);
        
        return Result.success(statistics);
    }
    
    /**
     * 获取门店订单统计
     *
     * @return 门店订单统计
     */
    @GetMapping("/shop-statistics")
    @Operation(summary = "获取门店订单统计", description = "获取合作商旗下各门店订单统计")
    @Auditable(
        module = AuditConstants.Module.ORDER,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询门店订单统计",
        targetType = AuditConstants.TargetType.ORDER
    )
    public Result<Map<String, Object>> getShopOrderStatistics(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        // 获取当前登录的合作商ID
        Long partnerId = Long.valueOf(getPartnerIdFromToken());
        
        // 实现门店订单统计
        Map<String, Object> statistics = orderService.getPartnerShopOrderStatistics(partnerId, startTime.toString(), endTime.toString());
        
        return Result.success(statistics);
    }

    /**
     * 获取每日订单统计
     *
     * @param shopId 门店ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 每日订单统计
     */
    @GetMapping("/daily-statistics")
    @Operation(summary = "获取每日订单统计", description = "获取合作商旗下每日订单统计")
    @Auditable(
        module = AuditConstants.Module.ORDER,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询每日订单统计",
        targetType = AuditConstants.TargetType.ORDER
    )
    public Result<Map<String, Object>> getDailyOrderStatistics(
            @Parameter(description = "门店ID") @RequestParam(required = false) Long shopId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDateTime endDate) {
        
        // 获取当前登录的合作商ID
        Long partnerId = Long.valueOf(getPartnerIdFromToken());
        
        // 验证门店是否属于当前合作商
        if (shopId != null) {
            Shop shop = shopService.getById(shopId);
            if (shop == null) {
                return Result.failed("门店不存在");
            }
            
            if (!partnerId.equals(shop.getPartnerId())) {
                return Result.failed("无权访问非本合作商的门店数据");
            }
        }
        
        // 实现每日订单统计
        Map<String, Object> statistics = orderService.getPartnerDailyOrderStatistics(partnerId, shopId, startDate.toString(), endDate.toString());
        
        return Result.success(statistics);
    }

    /**
     * 导出订单数据
     */
    @GetMapping("/export")
    @Operation(summary = "导出订单数据", description = "导出合作商旗下订单数据")
    @Auditable(
        module = AuditConstants.Module.ORDER,
        operation = AuditConstants.Operation.EXPORT,
        description = "合作商导出订单数据",
        targetType = AuditConstants.TargetType.ORDER
    )
    public Result<String> exportOrders(
            @Parameter(description = "订单编号") @RequestParam(required = false) String orderNo,
            @Parameter(description = "设备编号") @RequestParam(required = false) String deviceNo,
            @Parameter(description = "订单状态：1-进行中 2-已完成 3-已取消 4-已退款") @RequestParam(required = false) Integer orderStatus,
            @Parameter(description = "支付状态：0-未支付 1-已支付") @RequestParam(required = false) Integer payStatus,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 验证门店是否属于当前合作商
        if (shopId != null) {
            Shop shop = shopService.getById(shopId);
            if (shop == null) {
                return Result.failed("门店不存在");
            }
            
            if (!partnerId.equals(shop.getPartnerId())) {
                return Result.failed("无权访问非本合作商的门店数据");
            }
        }
        
        // 实现订单导出
        String downloadUrl = orderService.exportPartnerOrders(orderNo, deviceNo, orderStatus, payStatus, partnerId, shopId, 
                                                          startTime != null ? startTime.toString() : null, 
                                                          endTime != null ? endTime.toString() : null);
        
        return Result.success(downloadUrl);
    }
    
    /**
     * 获取当前登录的合作商ID
     *
     * @return 合作商ID
     */
    private Integer getPartnerIdFromToken() {
        // 从Session中获取合作商ID
        Object partnerId = StpUtil.getSession().get("partnerId");
        if (partnerId == null) {
            throw new BusinessException("未找到合作商信息，请重新登录");
        }
        return Integer.valueOf(partnerId.toString());
    }
} 