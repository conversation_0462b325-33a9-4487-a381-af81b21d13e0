package com.jycb.jycbz.modules.shop.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.constant.RoleConstants;
import com.jycb.jycbz.common.enums.AdminTypeEnum;
import com.jycb.jycbz.modules.partner.entity.Partner;
import com.jycb.jycbz.modules.partner.service.PartnerService;
import com.jycb.jycbz.modules.shop.dto.ShopDTO;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopService;
import com.jycb.jycbz.modules.shop.vo.ShopDetailVO;
import com.jycb.jycbz.modules.system.entity.Admin;
import com.jycb.jycbz.modules.system.service.AdminService;

import java.util.HashMap;
import java.util.Map;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 合作商门店管理控制器
 * 用于合作商管理旗下门店
 */
@Slf4j
@RestController
@RequestMapping("/partner/shop")
@Tag(name = "合作商门店管理", description = "合作商管理旗下门店相关接口")
@RequiredArgsConstructor
@SaCheckRole(RoleConstants.PARTNER_ADMIN)
@Validated
public class PartnerShopController {

    private final ShopService shopService;
    private final AdminService adminService;
    private final PartnerService partnerService;

    /**
     * 获取合作商门店分页列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param shopName 门店名称
     * @param status   状态
     * @return 分页列表
     */
    @GetMapping("/page")
    @Operation(summary = "获取合作商门店分页列表", description = "分页获取当前合作商下的门店列表")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.QUERY,
        description = "查询合作商门店列表",
        targetType = AuditConstants.TargetType.SHOP
    )
    public Result<PageResult<ShopDetailVO>> getPartnerShopPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "门店名称") @RequestParam(required = false) String shopName,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取合作商门店分页列表
        Page<ShopDetailVO> page = shopService.getPartnerShopPage(pageNum, pageSize, partnerId, shopName, status);
        return Result.success(PageResult.build(page));
    }

    /**
     * 获取合作商门店列表（不分页）
     *
     * @param shopName 门店名称
     * @param status   状态
     * @return 门店列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取合作商门店列表", description = "获取当前合作商下的门店列表（不分页）")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.QUERY,
        description = "查询合作商门店列表",
        targetType = AuditConstants.TargetType.SHOP
    )
    public Result<PageResult<ShopDetailVO>> getPartnerShopList(
            @Parameter(description = "门店名称") @RequestParam(required = false) String shopName,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取合作商门店列表（使用分页但设置大页面大小）
        Page<ShopDetailVO> page = shopService.getPartnerShopPage(1, 1000, partnerId, shopName, status);
        return Result.success(PageResult.build(page));
    }

    /**
     * 获取门店详情
     *
     * @param id 门店ID
     * @return 门店详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取门店详情", description = "根据门店ID获取门店详细信息")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.READ,
        description = "查询门店详情",
        targetType = AuditConstants.TargetType.SHOP,
        targetIdParam = "id"
    )
    public Result<ShopDetailVO> getShopDetail(@Parameter(description = "门店ID") @PathVariable Long id) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取门店详情
        ShopDetailVO shopDetail = shopService.getShopDetail(id);
        // 验证门店是否属于当前合作商
        if (!partnerId.equals(shopDetail.getPartnerId())) {
            return Result.failed("无权访问非本合作商门店信息");
        }
        return Result.success(shopDetail);
    }

    /**
     * 获取门店详情（详细路径）
     *
     * @param id 门店ID
     * @return 门店详情
     */
    @GetMapping("/{id}/detail")
    @Operation(summary = "获取门店详情", description = "根据门店ID获取门店详细信息")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.READ,
        description = "查询门店详情",
        targetType = AuditConstants.TargetType.SHOP,
        targetIdParam = "id"
    )
    public Result<ShopDetailVO> getShopDetailWithPath(@Parameter(description = "门店ID") @PathVariable Long id) {
        return getShopDetail(id);
    }

    /**
     * 获取门店营业概况
     *
     * @return 门店营业概况
     */
    @GetMapping("/overview")
    @Operation(summary = "获取门店营业概况", description = "获取当前合作商下门店的营业概况")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.QUERY,
        description = "查询门店营业概况",
        targetType = AuditConstants.TargetType.SHOP
    )
    public Result<Object> getShopOverview() {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();

        // 构建营业概况数据
        Map<String, Object> overview = new HashMap<>();

        // 获取门店统计数据
        Page<ShopDetailVO> shopPage = shopService.getPartnerShopPage(1, 1000, partnerId, null, null);
        long totalShops = shopPage.getTotal();
        long activeShops = shopPage.getRecords().stream()
                .filter(shop -> shop.getStatus() != null && shop.getStatus() == 1)
                .count();

        overview.put("totalShops", totalShops);
        overview.put("activeShops", activeShops);
        overview.put("todayRevenue", 0.0); // 今日营收，需要根据实际业务逻辑实现
        overview.put("monthRevenue", 0.0); // 本月营收，需要根据实际业务逻辑实现
        overview.put("totalRevenue", 0.0); // 总营收，需要根据实际业务逻辑实现

        return Result.success(overview);
    }

    /**
     * 创建门店
     *
     * @param shopDTO 门店信息
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建门店", description = "合作商创建旗下门店")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.CREATE,
        description = "创建门店",
        targetType = AuditConstants.TargetType.SHOP,
        logRequestParams = true
    )
    public Result<Shop> createShop(@Parameter(description = "门店信息") @Valid @RequestBody ShopDTO shopDTO) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();

        // 获取合作商信息以获取entityId
        Partner partner = partnerService.getById(partnerId);
        if (partner == null) {
            return Result.failed("合作商信息不存在");
        }

        // 自动设置门店所属合作商ID和业务主体ID，确保安全性
        shopDTO.setPartnerId(partnerId);
        shopDTO.setEntityId(partner.getEntityId());

        // 创建门店
        Shop shop = shopService.createShop(shopDTO);
        
        // 创建门店管理员账号
        if (shopDTO.getAdminInfo() != null) {
            try {
                Admin admin = new Admin();
                admin.setUsername(shopDTO.getAdminInfo().getUsername());
                admin.setPassword(shopDTO.getAdminInfo().getPassword());
                admin.setRealName(shopDTO.getAdminInfo().getRealName());
                admin.setMobile(shopDTO.getAdminInfo().getMobile());
                admin.setEmail(shopDTO.getAdminInfo().getEmail());
                admin.setAdminType(AdminTypeEnum.SHOP.getValue());
                admin.setShopId(shop.getId());
                admin.setPartnerId(partnerId);
                admin.setEntityId(shop.getEntityId());
                admin.setStatus(1); // 默认启用
                
                // 保存管理员信息
                boolean success = adminService.saveAdmin(admin);
                if (success) {
                    // 更新门店关联的管理员ID
                    shop.setAdminId(admin.getId());
                    shopService.updateById(shop);
                }
            } catch (Exception e) {
                log.error("创建门店管理员账号失败", e);
                // 不影响门店创建的主流程，只记录日志
            }
        }
        
        return Result.success(shop);
    }

    /**
     * 更新门店
     *
     * @param id      门店ID
     * @param shopDTO 门店信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新门店", description = "更新门店基本信息")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.UPDATE,
        description = "更新门店信息",
        targetType = AuditConstants.TargetType.SHOP,
        targetIdParam = "id",
        logRequestParams = true
    )
    public Result<Shop> updateShop(
            @Parameter(description = "门店ID") @PathVariable Long id,
            @Parameter(description = "门店信息") @Valid @RequestBody ShopDTO shopDTO) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取门店详情，验证是否属于当前合作商
        ShopDetailVO existingShop = shopService.getShopDetail(id);
        if (!partnerId.equals(existingShop.getPartnerId())) {
            return Result.failed("无权修改非本合作商门店信息");
        }
        // 自动设置门店所属合作商ID和业务主体ID，确保安全性
        shopDTO.setPartnerId(partnerId);
        shopDTO.setEntityId(existingShop.getEntityId());
        // 更新门店
        Shop shop = shopService.updateShop(id, shopDTO);
        return Result.success(shop);
    }

    /**
     * 更新门店状态
     *
     * @param id     门店ID
     * @param status 状态
     * @return 更新结果
     */
    @PutMapping("/{id}/shop-status")
    @Operation(summary = "更新门店状态", description = "启用或禁用门店")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.UPDATE,
        description = "更新门店状态",
        targetType = AuditConstants.TargetType.SHOP,
        targetIdParam = "id"
    )
    public Result<Boolean> updateStatus(
            @Parameter(description = "门店ID") @PathVariable Long id,
            @Parameter(description = "状态：1-启用 0-禁用") @RequestParam Integer status) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取门店详情，验证是否属于当前合作商
        ShopDetailVO existingShop = shopService.getShopDetail(id);
        if (!partnerId.equals(existingShop.getPartnerId())) {
            return Result.failed("无权修改非本合作商门店状态");
        }
        // 更新门店状态
        boolean result = shopService.updateStatus((long) id.intValue(), status);
        return Result.success(result);
    }

    /**
     * 更新门店分成比例
     *
     * @param id           门店ID
     * @param revenueRatio 分成比例
     * @return 更新结果
     */
    @PutMapping("/{id}/revenue-ratio")
    @Operation(summary = "更新门店分成比例", description = "更新门店的分成比例")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.UPDATE,
        description = "更新门店分成比例",
        targetType = AuditConstants.TargetType.SHOP,
        targetIdParam = "id"
    )
    public Result<Boolean> updateRevenueRatio(
            @Parameter(description = "门店ID") @PathVariable Long id,
            @Parameter(description = "分成比例") @RequestParam BigDecimal revenueRatio) {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取门店详情，验证是否属于当前合作商
        ShopDetailVO existingShop = shopService.getShopDetail(id);
        if (!partnerId.equals(existingShop.getPartnerId())) {
            return Result.failed("无权修改非本合作商门店分成比例");
        }
        // 更新门店分成比例
        boolean result = shopService.updateRevenueRatio(id, revenueRatio);
        return Result.success(result);
    }

    /**
     * 获取门店统计数据
     *
     * @return 统计数据
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取门店统计数据", description = "获取当前合作商下门店统计数据")
    @Auditable(
        module = AuditConstants.Module.SHOP,
        operation = AuditConstants.Operation.READ,
        description = "查看门店统计数据",
        targetType = AuditConstants.TargetType.SHOP
    )
    public Result<Map<String, Object>> getShopStatistics() {
        // 获取当前登录的合作商ID
        Long partnerId = getPartnerIdFromToken();
        // 获取合作商下门店统计数据
        Map<String, Object> statistics = shopService.getPartnerShopStatistics(partnerId);
        return Result.success(statistics);
    }

    /**
     * 获取当前登录的合作商ID
     *
     * @return 合作商ID
     */
    private Long getPartnerIdFromToken() {
        // 从Session中获取合作商ID
        Object partnerId = StpUtil.getSession().get("partnerId");
        if (partnerId == null) {
            throw new BusinessException("未找到合作商信息，请重新登录");
        }
        return Long.valueOf(partnerId.toString());
    }
} 