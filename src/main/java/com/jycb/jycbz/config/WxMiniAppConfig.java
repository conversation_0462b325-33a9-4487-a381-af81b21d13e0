package com.jycb.jycbz.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.jycb.jycbz.modules.system.service.SystemConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 微信小程序配置
 */
@Slf4j
@Data
@Configuration
public class WxMiniAppConfig {

    // 修改为wechat配置类型，与SQL文件保持一致
    private static final String CONFIG_TYPE = "wechat";
    // 兼容旧的配置类型
    private static final String COMPAT_CONFIG_TYPE = "wx_miniapp";
    
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 小程序appid
     */
    private String appid;

    /**
     * 小程序密钥
     */
    private String secret;

    /**
     * 小程序token
     */
    private String token;

    /**
     * 小程序aesKey
     */
    private String aesKey;

    /**
     * 消息格式
     */
    private String msgDataFormat;

    /**
     * 从数据库加载配置
     */
    private void loadConfigFromDB() {
        log.info("开始从数据库加载微信小程序配置...");
        try {
            log.debug("尝试从配置类型 {} 加载配置", CONFIG_TYPE);
            Map<String, String> configs = systemConfigService.getConfigsByType(CONFIG_TYPE);
            
            if (configs == null || configs.isEmpty()) {
                log.debug("未找到 {} 类型的配置，尝试从兼容配置类型 {} 加载", CONFIG_TYPE, COMPAT_CONFIG_TYPE);
                // 兼容旧配置
                configs = systemConfigService.getConfigsByType(COMPAT_CONFIG_TYPE);
            }
            
            if (configs != null && !configs.isEmpty()) {
                log.debug("成功加载配置，配置项数量: {}", configs.size());
                log.debug("配置内容: {}", configs);
                
                this.appid = configs.getOrDefault("appid", "");
                this.secret = configs.getOrDefault("secret", "");
                this.token = configs.getOrDefault("token", "");
                this.aesKey = configs.getOrDefault("aesKey", "");
                this.msgDataFormat = configs.getOrDefault("msgDataFormat", "JSON");
                
                log.info("从数据库加载微信小程序配置成功");
                log.info("appid: {}", maskString(appid));
                log.info("secret: {}", maskString(secret));
                log.info("token是否设置: {}", StringUtils.hasText(token));
                log.info("aesKey是否设置: {}", StringUtils.hasText(aesKey));
                log.info("msgDataFormat: {}", msgDataFormat);
            } else {
                // 使用默认配置
                log.warn("未找到微信小程序配置，使用默认配置");
                this.appid = "wx23b7c6a78842ee76";
                this.secret = "63374942cd14aa34112354b2999a6ed6";
                this.token = "";
                this.aesKey = "";
                this.msgDataFormat = "JSON";
                
                log.info("使用默认配置");
                log.info("默认appid: {}", maskString(appid));
                log.info("默认secret: {}", maskString(secret));
            }
            
            // 验证配置有效性
            if (!StringUtils.hasText(appid)) {
                log.error("微信小程序appid为空，这将导致登录失败");
            }
            if (!StringUtils.hasText(secret)) {
                log.error("微信小程序secret为空，这将导致登录失败");
            }
        } catch (Exception e) {
            log.error("从数据库加载微信小程序配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 对敏感信息进行脱敏处理
     */
    private String maskString(String value) {
        if (value == null || value.length() <= 8) {
            return value != null ? "***" : null;
        }
        return value.substring(0, 3) + "****" + value.substring(value.length() - 3);
    }

    /**
     * 微信小程序服务
     */
    @Bean
    public WxMaService wxMaService() {
        log.info("初始化微信小程序服务...");
        
        // 从数据库加载配置
        loadConfigFromDB();
        
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(appid);
        config.setSecret(secret);
        config.setToken(token);
        config.setAesKey(aesKey);
        config.setMsgDataFormat(msgDataFormat);

        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(config);
        
        log.info("微信小程序配置初始化完成");
        return service;
    }
    
    /**
     * 刷新配置
     */
    public void refreshConfig() {
        log.info("刷新微信小程序配置...");
        loadConfigFromDB();
        log.info("微信小程序配置刷新完成");
    }
} 