项目概述
今夜城堡(JYCB)是一个综合性设备管理与财务结算系统，主要用于管理设备全生命周期、处理订单和财务结算。系统支持多种角色：系统管理员、业务主体、合作商和门店。
技术栈
核心框架：Spring Boot
ORM框架：MyBatis-Plus
数据库：MySQL 8.0
缓存：Redis
认证授权：Sa-Token
API文档：Knife4j (基于Swagger)
存储服务：腾讯云COS
微信集成：微信小程序API
项目结构
项目采用模块化设计：
common模块：通用工具类、常量、枚举、异常处理
config模块：各种配置类（MyBatis-Plus、Redis、Sa-Token等）
业务模块：
admin：管理员模块
user：用户模块
entity：业务主体模块
partner：合作商模块
shop：门店模块
device：设备模块
order：订单模块
system：系统模块
数据库设计
数据库采用分表设计，主要包含以下表组：
财务相关表：
财务账户表(jy_finance_account)
财务流水表(jy_finance_log)
提现表(jy_withdraw)
线下提现表(jy_offline_withdraw)
佣金配置表(jy_commission_config)
佣金明细表(jy_commission_detail)
结算配置表(jy_settlement_config)
用户与角色表：
用户表(jy_user)
管理员表(jy_admin)
权限表(jy_permission)
业务实体表：
业务主体表(jy_entity)
合作商表(jy_partner)
门店表(jy_shop)
招商管理表(jy_partner_application)
设备相关表：
设备表(jy_device)
设备日志表(jy_device_log)
设备费用表(jy_device_fee)
其他功能表：
地理位置表(jy_geo)
清洁人员表(jy_cleaner)
帮助支持表(jy_faq)
订单表(jy_order)
反馈表(jy_feedback)
消息表(jy_message)
审计日志表(jy_audit_log)
操作日志表(jy_operation_log)
系统配置表(jy_system)
核心业务逻辑
1. 用户体系
系统支持多种用户角色：
普通用户：通过微信小程序登录
门店管理员：负责门店日常运营
合作商/直签门店：负责区域内设备和门店管理
业务主体：整体业务管理者
系统管理员：系统最高权限管理者
2. 权限控制
采用多级权限控制机制：
系统级(超级管理员)：管理所有用户和账户，拥有全局权限
业务主体级：只能管理下级账户
合作商级：只能管理下级门店账户
门店级：只能管理自己门店内账户
3. 设备管理
设备管理特点：
设备本身不联网，通过小程序上传状态
通过设备UUID进行校验、绑定和归属管理
同一设备可被多用户同时付款开锁使用
4. 订单流程
用户扫描设备二维码
系统校验设备状态和绑定情况
用户创建使用订单
用户支付订单
系统发送开锁指令（通过小程序）
用户使用设备
用户结束使用，关闭订单
5. 财务与分成系统
采用多级分成模型：
平台方：获取平台分成
业务主体：获取业务主体分成
合作商：获取合作商分成
门店：获取门店分成
分成配置体系：
层级配置：支持系统级、业务主体级、合作商级、门店级配置
继承机制：下级可继承上级配置
独立配置：可为每级单独设置分成比例
财务账户体系：
支持系统账户、业务主体账户、合作商账户、门店账户和用户账户
包括可用余额和冻结余额
业务主体账户仅用于收入统计，不支持提现
6. 审计日志系统
用于记录系统操作行为，包括：
用户登录
数据增删改查
财务操作
设备操作
通过@Auditable注解实现，包含模块、操作类型、描述等信息。
系统特点
多级业务架构：总后台-业务主体-合作商-门店
完善的权限管理：基于Sa-Token实现
离线设备管理：设备本身不联网，通过小程序中转
多用户并发使用：同一设备多用户同时使用
灵活的分成机制：支持多级分成
完整的财务体系：从订单创建到最终结算的闭环
详细的审计日志：记录系统操作，便于追溯
这个项目是一个企业级应用后端系统，具有完善的架构设计和丰富的功能模块，特别在财务分成、权限控制和设备管理方面有较为复杂的业务逻辑实现。










@src @jycb_optimized @功能文档.md @逻辑 @财务 @架构 @实现 仔细理解项目阅读sql；理解架构


chellcode 



 

