# 使用OpenJDK 17作为基础镜像
FROM openjdk:17-jdk-slim

# 设置工作目录
WORKDIR /jycb

# 创建目录结构
RUN mkdir -p /jycb/target /jycb/logs

# 复制JAR文件（使用通配符确保能找到文件）
COPY target/*.jar /jycb/target/jycb-z-0.0.1-SNAPSHOT.jar

# 验证文件是否复制成功
RUN ls -la /jycb/target/

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["java", "-Xms512m", "-Xmx2g", "-XX:+UseG1GC", "-Dspring.profiles.active=docker", "-Dserver.port=8080", "-jar", "/jycb/target/jycb-z-0.0.1-SNAPSHOT.jar"]
