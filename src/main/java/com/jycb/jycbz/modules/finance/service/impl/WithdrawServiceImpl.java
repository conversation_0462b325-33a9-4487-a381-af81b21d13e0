package com.jycb.jycbz.modules.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.finance.domain.factory.WithdrawFactory;
import com.jycb.jycbz.modules.finance.dto.BalanceOperationDTO;
import com.jycb.jycbz.modules.finance.entity.Withdraw;
import com.jycb.jycbz.modules.finance.mapper.WithdrawMapper;
import com.jycb.jycbz.modules.finance.service.WithdrawService;
import com.jycb.jycbz.modules.finance.service.core.AccountCoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 提现服务实现类
 */
@Service("basicWithdrawService")
public class WithdrawServiceImpl extends ServiceImpl<WithdrawMapper, Withdraw> implements WithdrawService {

    @Autowired
    private AccountCoreService accountCoreService;
    
    @Autowired
    private WithdrawFactory withdrawFactory;

    @Override
    @Transactional
    public boolean updateStatus(Long withdrawId, int status, Integer operatorId, String operatorName) {
        // 获取提现记录
        Withdraw entity = getById(withdrawId);
        if (entity == null) {
            throw new BusinessException("提现记录不存在");
        }
        
        // 转换为领域对象
        com.jycb.jycbz.modules.finance.domain.Withdraw withdraw = withdrawFactory.fromEntity(entity);
        
        // 根据状态执行不同操作
        boolean success = false;
        switch (status) {
            case 1: // 审核通过
                success = withdraw.audit(true, operatorId.longValue(), operatorName, "审核通过");
                break;
            case 2: // 审核拒绝
                success = withdraw.audit(false, operatorId.longValue(), operatorName, "审核拒绝");
                // 如果拒绝，需要解冻资金
                if (success) {
                    BalanceOperationDTO operationDTO = new BalanceOperationDTO();
                    // 根据提现类型确定账户类型和ID
                    if (entity.getShopId() != null) {
                        operationDTO.setAccountType("shop");
                        operationDTO.setAccountId(entity.getShopId().longValue());
                    } else if (entity.getPartnerId() != null) {
                        operationDTO.setAccountType("partner");
                        operationDTO.setAccountId(entity.getPartnerId().longValue());
                    } else if (entity.getEntityId() != null) {
                        operationDTO.setAccountType("entity");
                        operationDTO.setAccountId(entity.getEntityId().longValue());
                    } else {
                        throw new BusinessException("无法确定提现账户类型");
                    }
                    operationDTO.setAmount(entity.getAmount());
                    operationDTO.setOrderId(entity.getWithdrawNo());
                    operationDTO.setDescription("提现拒绝解冻资金");
                    operationDTO.setOperatorId(operatorId.longValue());
                    operationDTO.setOperatorName(operatorName);
                    accountCoreService.unfreezeBalance(operationDTO);
                }
                break;
            case 3: // 已打款
                success = withdraw.confirmPayment(operatorId.longValue(), operatorName, "PAYMENT_" + System.currentTimeMillis(), "提现打款成功");
                break;
            case 4: // 已取消
                success = withdraw.cancel();
                // 如果取消，需要解冻资金
                if (success) {
                    BalanceOperationDTO operationDTO = new BalanceOperationDTO();
                    // 根据提现类型确定账户类型和ID
                    if (entity.getShopId() != null) {
                        operationDTO.setAccountType("shop");
                        operationDTO.setAccountId(entity.getShopId().longValue());
                    } else if (entity.getPartnerId() != null) {
                        operationDTO.setAccountType("partner");
                        operationDTO.setAccountId(entity.getPartnerId().longValue());
                    } else if (entity.getEntityId() != null) {
                        operationDTO.setAccountType("entity");
                        operationDTO.setAccountId(entity.getEntityId().longValue());
                    } else {
                        throw new BusinessException("无法确定提现账户类型");
                    }
                    operationDTO.setAmount(entity.getAmount());
                    operationDTO.setOrderId(entity.getWithdrawNo());
                    operationDTO.setDescription("提现取消解冻资金");
                    operationDTO.setOperatorId(operatorId.longValue());
                    operationDTO.setOperatorName(operatorName);
                    accountCoreService.unfreezeBalance(operationDTO);
                }
                break;
            default:
                throw new BusinessException("不支持的状态变更");
        }
        
        if (!success) {
            throw new BusinessException("状态变更失败");
        }
        
        // 转换回实体并更新
        entity = withdrawFactory.toEntity(withdraw);
        entity.setUpdateTime(LocalDateTime.now());
        
        return updateById(entity);
    }

    @Override
    public Withdraw getByWithdrawNo(String withdrawNo) {
        LambdaQueryWrapper<Withdraw> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Withdraw::getWithdrawNo, withdrawNo);
        return getOne(wrapper);
    }
} 