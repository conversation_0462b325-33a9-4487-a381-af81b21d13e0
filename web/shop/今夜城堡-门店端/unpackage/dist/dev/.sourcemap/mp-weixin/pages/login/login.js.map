{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/login.vue?5805", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/login.vue?aecd", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/login.vue?69fd", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/login.vue?bf81", "uni-app:///pages/login/login.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/login.vue?2a94", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/login.vue?f192"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "username", "password", "remember", "originalScrollTop", "loading", "computed", "onLoad", "console", "onShow", "onUnload", "methods", "disableScroll", "uni", "pageStyle", "overflow", "currentWebview", "bounce", "e", "document", "passive", "enableScroll", "toggleRemember", "forgotPassword", "url", "success", "fail", "title", "icon", "openTerms", "openPrivacy", "submitForm", "userType", "mask", "then", "token", "userInfo", "userId", "role", "realName", "avatar", "shopId", "adminType", "entityId", "partnerId", "animation", "setTimeout", "duration", "checkLoginStatus"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,ivBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkEvwB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC,4BACA,oEACA;EACAC;IACA;IACAC;;IAEA;IACA;;IAEA;IACA;MACA;MACA;QACA;QACA;MACA;IACA;MACAA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC,yCACA;IACAC;MACA;MACA;QACA;QACA;UACAC;YACAC;cACAC;YACA;UACA;QACA;QACA;QAAA,KACA;UACA;UACAC;YACAC;UACA;QACA;QACA;QAAA,KACA;UACA;YACAC;UACA;;UAEA;UACA;;UAEA;UACAC;YAAAC;UAAA;QACA;MACA;QACAZ;MACA;IACA;IACAa;MACA;MACA;QACA;QACA;UACAR;YACAC;cACAC;YACA;UACA;QACA;QACA;QAAA,KACA;UACA;UACAC;YACAC;UACA;QACA;QACA;QAAA,KACA;UACAE;YAAAC;UAAA;UACA;QACA;MACA;QACAZ;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;MACAV;QACAW;QACAC;UACAjB;QACA;QACAkB;UACAlB;UACAK;YACAc;YACAC;UACA;QACA;MACA;IACA;IACAC;MACAhB;QACAc;QACAC;MACA;IACA;IACAE;MACAjB;QACAc;QACAC;MACA;IACA;IACAG;MAAA;MACA;MACA;QACAlB;UACAc;UACAC;QACA;QACA;MACA;MAEA;QACAf;UACAc;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;UACAf;YACAZ;YACA+B;UACA;QACA;UACAxB;QACA;MACA;QACA;UACAK;QACA;UACAL;QACA;MACA;;MAEA;MACA;;MAEA;MACA;MACAK;QACAc;QACAM;MACA;;MAEA;MACA,qEACAC;QACA;QACArB;QACA;QAEAL;;QAEA;QACA;UACAA;;UAEA;UACA;UACA;YACAA;YACAK;UACA;;UAEA;UACA;YACAsB;YACAH;YACAI;cACAC;cACApC;cACAqC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;UACA;UAEApC;;UAEA;UACAK;YACAgC;UACA;;UAEA;UACAC;YACAtC;YACAK;cACAW;cACAC;gBACAjB;cACA;cACAkB;gBACAlB;gBACA;gBACAK;kBACAW;gBACA;cACA;YACA;UACA;QACA;UACAhB;UACAK;YACAc;YACAC;YACAmB;UACA;QACA;MACA;QACA;QACAlC;QACA;QAEAA;UACAc;UACAC;UACAmB;QACA;MACA;IACA;IACA;IACAC;MACA;QACAxC;;QAEA;QACA;UACAA;UACAK;YACAW;UACA;QACA;MACA;QACAhB;QACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnWA;AAAA;AAAA;AAAA;AAAgkC,CAAgB,0hCAAG,EAAC,C;;;;;;;;;;;ACAplC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container safe-area-bottom page-login\">\n    <view class=\"login-background\"></view>\n    <view class=\"login-container\">\n      <view class=\"scrollable-content\">\n        <view class=\"login-header\">\n          <view class=\"login-logo\">\n            <image src=\"/static/logo2.svg\" mode=\"aspectFit\"></image>\n          </view>\n        </view>\n        \n        <view class=\"login-title\">\n          <text>门店管理系统</text>\n        </view>\n        \n        <view class=\"form-container\">\n          <view class=\"form-group\">\n            <text class=\"form-label\">账号</text>\n            <view class=\"input-icon\">\n              <text class=\"material-icons icon-input\">person</text>\n              <input type=\"text\" class=\"form-input\" placeholder=\"请输入账号\" v-model=\"formData.username\" placeholder-style=\"color: rgba(255, 255, 255, 0.3);\" />\n            </view>\n          </view>\n          \n          <view class=\"form-group\">\n            <text class=\"form-label\">密码</text>\n            <view class=\"input-icon\">\n              <text class=\"material-icons icon-input\">lock</text>\n              <input type=\"password\" class=\"form-input\" placeholder=\"请输入密码\" v-model=\"formData.password\" placeholder-style=\"color: rgba(255, 255, 255, 0.3);\" />\n            </view>\n          </view>\n          \n          <view class=\"form-footer\">\n            <label class=\"remember-me\" @tap.stop=\"toggleRemember\">\n              <view class=\"checkbox-wrapper\">\n                <view class=\"checkbox-custom\" :class=\"{'checked': formData.remember}\"></view>\n              </view>\n              <text>记住我</text>\n            </label>\n            <text class=\"forgot-password\" @tap=\"forgotPassword\">忘记密码？</text>\n          </view>\n          \n          <button class=\"button primary\" @tap=\"submitForm\" :disabled=\"loading\">\n            {{loading ? '登录中...' : '登录'}}\n          </button>\n        </view>\n        \n        <view class=\"login-footer\">\n          <text>登录即表示您同意我们的</text>\n          <text class=\"link\" @tap=\"openTerms\">服务条款</text>\n          <text>和</text>\n          <text class=\"link\" @tap=\"openPrivacy\">隐私政策</text>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"version-info version-safe-bottom\">\n      <text>版本号: v1.0.0</text>\n    </view>\n    \n    <!-- iOS安全区域 - 透明背景 -->\n    <view class=\"login-safe-area\"></view>\n  </view>\n</template>\n\n<script>\nimport { mapActions, mapGetters } from 'vuex';\n\nexport default {\n  data() {\n    return {\n      formData: {\n        username: '',\n        password: '',\n        remember: false\n      },\n      originalScrollTop: 0,\n      loading: false\n    }\n  },\n  computed: {\n    ...mapGetters(['isLoggedIn', 'getUserType', 'getUserInfo'])\n  },\n  onLoad() {\n    // 页面加载完成\n    console.log('登录页面加载完成');\n    \n    // 检查登录状态\n    this.checkLoginStatus();\n    \n    // 尝试获取保存的账号信息\n    try {\n      const savedUser = uni.getStorageSync('savedUser');\n      if (savedUser) {\n        this.formData.username = savedUser.username;\n        this.formData.remember = true;\n      }\n    } catch (e) {\n      console.error('获取保存的账号信息失败', e);\n    }\n    \n    // 禁止页面滚动\n    this.disableScroll();\n  },\n  onShow() {\n    // 禁止页面滚动\n    this.disableScroll();\n    \n    // 检查登录状态\n    this.checkLoginStatus();\n  },\n  onUnload() {\n    // 页面卸载时移除事件监听\n    this.enableScroll();\n  },\n  methods: {\n    ...mapActions(['changeUserType', 'logout']),\n    disableScroll() {\n      // 禁止页面滚动 - 使用uni-app API\n      try {\n        // 小程序环境\n        if (uni.canIUse('setPageMeta')) {\n          uni.setPageMeta({\n            pageStyle: {\n              overflow: 'hidden'\n            }\n          });\n        } \n        // App环境\n        else if (plus && plus.webview) {\n          const currentWebview = plus.webview.currentWebview();\n          currentWebview.setStyle({\n            bounce: 'none'\n          });\n        }\n        // H5环境\n        else if (typeof document !== 'undefined') {\n          const preventDefault = (e) => {\n            e.preventDefault();\n          };\n          \n          // 保存函数引用以便移除\n          this.preventDefault = preventDefault;\n          \n          // 添加事件监听\n          document.addEventListener('touchmove', preventDefault, { passive: false });\n        }\n      } catch (e) {\n        console.error('禁止滚动失败', e);\n      }\n    },\n    enableScroll() {\n      // 移除滚动限制 - 使用uni-app API\n      try {\n        // 小程序环境\n        if (uni.canIUse('setPageMeta')) {\n          uni.setPageMeta({\n            pageStyle: {\n              overflow: 'auto'\n            }\n          });\n        } \n        // App环境\n        else if (plus && plus.webview) {\n          const currentWebview = plus.webview.currentWebview();\n          currentWebview.setStyle({\n            bounce: 'vertical'\n          });\n        }\n        // H5环境\n        else if (typeof document !== 'undefined' && this.preventDefault) {\n          document.removeEventListener('touchmove', this.preventDefault, { passive: false });\n          this.preventDefault = null;\n        }\n      } catch (e) {\n        console.error('启用滚动失败', e);\n      }\n    },\n    toggleRemember() {\n      this.formData.remember = !this.formData.remember;\n    },\n    forgotPassword() {\n      // 跳转到忘记密码页面\n      uni.navigateTo({\n        url: '/pages/login/reset-password',\n        success: () => {\n          console.log('跳转到忘记密码页面成功');\n        },\n        fail: (err) => {\n          console.error('跳转到忘记密码页面失败', err);\n          uni.showToast({\n            title: '跳转失败，请稍后再试',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    openTerms() {\n      uni.showToast({\n        title: '服务条款页面开发中',\n        icon: 'none'\n      });\n    },\n    openPrivacy() {\n      uni.showToast({\n        title: '隐私政策页面开发中',\n        icon: 'none'\n      });\n    },\n    submitForm() {\n      // 表单验证\n      if (!this.formData.username.trim()) {\n        uni.showToast({\n          title: '请输入账号',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.formData.password) {\n        uni.showToast({\n          title: '请输入密码',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 保存账号信息\n      if (this.formData.remember) {\n        try {\n          uni.setStorageSync('savedUser', {\n            username: this.formData.username,\n            userType: 'store'\n          });\n        } catch (e) {\n          console.error('保存账号信息失败', e);\n        }\n      } else {\n        try {\n          uni.removeStorageSync('savedUser');\n        } catch (e) {\n          console.error('移除账号信息失败', e);\n        }\n      }\n      \n      // 设置用户类型\n      this.changeUserType('store');\n      \n      // 显示加载中\n      this.loading = true;\n      uni.showLoading({\n        title: '登录中...',\n        mask: true\n      });\n      \n      // 调用API进行登录\n      this.$api.auth.login(this.formData.username, this.formData.password)\n        .then(res => {\n          // 登录成功\n          uni.hideLoading();\n          this.loading = false;\n          \n          console.log('登录响应:', res);\n          \n          // 检查响应格式\n          if (res && res.code === 200 && res.data && res.data.token) {\n            console.log('登录成功，保存token和用户信息');\n            \n            // 确保token已保存到本地存储\n            const savedToken = uni.getStorageSync('token');\n            if (!savedToken) {\n              console.log('本地存储中没有token，手动保存');\n              uni.setStorageSync('token', res.data.token);\n            }\n            \n            // 使用Vuex保存登录信息\n            this.$store.dispatch('saveLoginInfo', {\n              token: res.data.token,\n              userType: 'store',\n              userInfo: {\n                userId: res.data.userId,\n                username: res.data.username,\n                role: res.data.role,\n                realName: res.data.realName,\n                avatar: res.data.avatar,\n                shopId: res.data.shopId,\n                adminType: res.data.adminType,\n                entityId: res.data.entityId,\n                partnerId: res.data.partnerId\n              }\n            });\n            \n            console.log('登录成功，准备跳转到首页');\n            \n            // 确保隐藏原生TabBar，避免闪烁\n            uni.hideTabBar({\n              animation: false\n            });\n            \n            // 添加延迟确保TabBar隐藏后再跳转\n            setTimeout(() => {\n              console.log('开始跳转到首页');\n              uni.reLaunch({\n                url: '/pages/dashboard/dashboard',\n                success: () => {\n                  console.log('跳转到首页成功');\n                },\n                fail: (err) => {\n                  console.error('跳转到首页失败', err);\n                  // 尝试使用switchTab作为备选方案\n                  uni.switchTab({\n                    url: '/pages/dashboard/dashboard'\n                  });\n                }\n              });\n            }, 500); // 延长延迟时间，确保token保存完成\n          } else {\n            console.error('登录返回数据格式错误:', res);\n            uni.showToast({\n              title: '登录返回数据格式错误',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        }).catch(err => {\n          // 登录失败\n          uni.hideLoading();\n          this.loading = false;\n          \n          uni.showToast({\n            title: err.message || '登录失败，请检查账号密码',\n            icon: 'none',\n            duration: 2000\n          });\n        });\n    },\n    // 检查登录状态\n    checkLoginStatus() {\n      try {\n        console.log('登录页检查登录状态:', this.isLoggedIn ? '已登录' : '未登录');\n        \n        // 如果已登录，直接跳转到首页\n        if (this.isLoggedIn && this.getUserType === 'store') {\n          console.log('已登录，跳转到首页');\n          uni.reLaunch({\n            url: '/pages/dashboard/dashboard'\n          });\n        }\n      } catch (e) {\n        console.error('检查登录状态失败', e);\n        // 出错时也使用Vuex的logout action清除登录状态\n        this.$store.dispatch('logout');\n      }\n    }\n  }\n}\n</script>\n\n<style>\n/* 引入Material Icons图标库 */\n@font-face {\n  font-family: 'Material Icons';\n  font-style: normal;\n  font-weight: 400;\n  src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n}\n\n.material-icons {\n  font-family: 'Material Icons';\n  font-weight: normal;\n  font-style: normal;\n  font-size: 48rpx;\n  line-height: 1;\n  letter-spacing: normal;\n  text-transform: none;\n  display: inline-block;\n  white-space: nowrap;\n  word-wrap: normal;\n  direction: ltr;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: rgba(255, 255, 255, 0.7);\n}\n\npage {\n  background-color: #121212;\n  color: #FFFFFF;\n  overflow: hidden; /* 禁止页面滚动 */\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n  position: fixed; /* 固定页面位置 */\n}\n\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n  max-width: 100%;\n}\n\n.page-container {\n  min-height: 100vh;\n  height: 100vh; /* 限制高度为视口高度 */\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  position: relative;\n  overflow: hidden; /* 禁止容器滚动 */\n  box-sizing: border-box; /* 确保padding不会增加宽度 */\n  width: 100%;\n  position: fixed; /* 固定容器位置 */\n  top: 0;\n  left: 0;\n  /* 确保底部内容不被安全区域遮挡 */\n  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */\n  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */\n}\n\n.login-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #121212;\n  background-image: radial-gradient(circle at top right, rgba(149, 39, 198, 0.15) 0%, transparent 40%), \n                    radial-gradient(circle at bottom left, rgba(255, 46, 147, 0.15) 0%, transparent 40%);\n  z-index: 0;\n}\n\n.login-container {\n  width: 100%;\n  max-width: 750rpx;\n  padding: 0rpx 30rpx 80rpx 30rpx; /* 减少顶部内边距 */\n  position: relative;\n  z-index: 1;\n  display: flex;\n  flex-direction: column;\n  overflow-y: hidden;\n  max-height: 94vh;\n  box-sizing: border-box;\n  -webkit-overflow-scrolling: none;\n  touch-action: none;\n}\n\n/* 创建一个内部滚动容器 */\n.scrollable-content {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  max-height: calc(100vh - 100rpx - constant(safe-area-inset-bottom)); /* 调整高度，考虑安全区域 */\n  max-height: calc(100vh - 100rpx - env(safe-area-inset-bottom)); /* 调整高度，考虑安全区域 */\n  overflow-y: auto;\n  padding-right: 10rpx;\n  -webkit-overflow-scrolling: touch;\n  transition: all 0.3s ease;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 0rpx; /* 减少底部间距 */\n  margin-top: 0rpx; /* 减少顶部间距 */\n  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);\n  cursor: pointer;\n}\n\n.login-logo {\n  margin-bottom: 10rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);\n}\n\n.login-logo image {\n  height: 400rpx;\n  width: 580rpx;\n  max-width: 100%;\n  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);\n}\n\n.form-container {\n  width: 100%;\n  margin-bottom: 20rpx; /* 减少底部间距 */\n  padding: 0 20rpx;\n  max-width: 650rpx;\n  margin-left: auto;\n  margin-right: auto;\n  overflow-y: hidden;\n  touch-action: none;\n  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);\n}\n\n.form-group {\n  margin-bottom: 30rpx; /* 减少间距 */\n  opacity: 1;\n}\n\n.form-group:nth-child(2) {\n  opacity: 1;\n}\n\n.form-footer {\n  opacity: 1;\n  margin-bottom: 30rpx; /* 减少间距 */\n}\n\n.button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100rpx;\n  border-radius: 9999rpx;\n  font-weight: 600;\n  border: none;\n  width: 70%;\n  margin: 0 auto;\n  font-size: 36rpx;\n  margin-top: 30rpx; /* 减少间距 */\n  margin-bottom: 30rpx; /* 减少间距 */\n  font-family: \"幼圆\", \"YouYuan\", \"Microsoft YaHei\", sans-serif;\n}\n\n.button.primary {\n  background: linear-gradient(to right, #9527C6, #A875FF);\n  color: white;\n  box-shadow: 0 8rpx 20rpx rgba(149, 39, 198, 0.3);\n  position: relative;\n  letter-spacing: 4rpx;\n  opacity: 1;\n}\n\n/* 添加伪元素增强阴影效果 */\n.button.primary::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border-radius: 9999rpx;\n  background: linear-gradient(to right, #9527C6, #A875FF);\n  opacity: 0.6;\n  filter: blur(15rpx);\n  z-index: -1;\n  transform: translateY(10rpx) scale(0.95);\n}\n\n/* 按钮点击效果 */\n.button.primary:active {\n  transform: translateY(2rpx);\n}\n\n.button.primary:active::after {\n  transform: translateY(5rpx) scale(0.95);\n  opacity: 0.4;\n}\n\n.login-footer {\n  text-align: center;\n  margin-top: 30rpx; /* 减少间距 */\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 24rpx;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  flex-wrap: wrap;\n  padding-bottom: 40rpx;\n  background-color: transparent;\n  opacity: 1;\n}\n\n.login-footer text {\n  margin: 0 4rpx;\n}\n\n.link {\n  color: #9527C6;\n}\n\n.version-info {\n  position: fixed;\n  bottom: 30rpx;\n  left: 0;\n  width: 100%;\n  text-align: center;\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.5);\n  z-index: 1;\n  padding: 10rpx;\n  background-color: transparent;\n}\n\n/* 小屏幕适配 */\n@media screen and (max-width: 375px) {\n  .login-logo image {\n    height: 300rpx;\n    width: 430rpx;\n  }\n  \n  .form-input {\n    height: 80rpx;\n    font-size: 28rpx;\n  }\n  \n  .button {\n    height: 80rpx;\n    font-size: 30rpx;\n  }\n}\n\n/* 新增样式 */\n.login-title {\n  text-align: center;\n  margin-bottom: 20rpx; /* 减少间距 */\n  margin-top: 0rpx;\n}\n\n.login-title text {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #8223BE;\n  font-family: \"幼圆\", \"YouYuan\", \"Microsoft YaHei\", sans-serif;\n  text-shadow: 0 0 10rpx rgba(130, 35, 190, 0.3);\n  letter-spacing: 2rpx;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 16rpx;\n  color: rgba(255, 255, 255, 0.7);\n  font-weight: 500;\n  font-size: 32rpx;\n}\n\n.form-input {\n  width: 100%;\n  height: 90rpx;\n  padding: 0 30rpx 0 90rpx;\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 24rpx;\n  color: #FFFFFF;\n  font-size: 34rpx;\n  box-sizing: border-box;\n}\n\n.input-icon {\n  position: relative;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.icon-input {\n  position: absolute;\n  left: 30rpx;\n  top: 50%;\n  transform: translateY(-50%);\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 40rpx;\n  z-index: 1;\n}\n\n.form-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.remember-me {\n  display: flex;\n  align-items: center;\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 26rpx;\n}\n\n.checkbox-wrapper {\n  position: relative;\n  display: inline-block;\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 16rpx;\n}\n\n.checkbox-custom {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 32rpx;\n  width: 32rpx;\n  background-color: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 16rpx;\n  transition: all 0.2s ease;\n}\n\n.checkbox-custom.checked {\n  background-color: #9527C6;\n  border-color: #9527C6;\n}\n\n.checkbox-custom.checked:after {\n  content: '';\n  position: absolute;\n  left: 10rpx;\n  top: 4rpx;\n  width: 6rpx;\n  height: 16rpx;\n  border: solid white;\n  border-width: 0 2px 2px 0;\n  transform: rotate(45deg);\n}\n\n.forgot-password {\n  color: #9527C6;\n  font-size: 26rpx;\n}\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753035246027\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}