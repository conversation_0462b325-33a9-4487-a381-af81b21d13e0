package com.jycb.jycbz.modules.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.utils.QrcodeUtils;
import com.jycb.jycbz.modules.clean.service.CleanTaskService;
import com.jycb.jycbz.modules.device.convert.DeviceConvert;
import com.jycb.jycbz.modules.device.entity.*;
import com.jycb.jycbz.modules.device.dto.*;
import com.jycb.jycbz.modules.device.mapper.DeviceLogMapper;
import com.jycb.jycbz.modules.device.mapper.DeviceMapper;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.device.service.DeviceFeeService;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.device.service.DeviceMaintenanceService;
import com.jycb.jycbz.modules.device.service.DeviceFaultReportService;
import com.jycb.jycbz.modules.device.service.DeviceCacheService;
import com.jycb.jycbz.modules.device.service.DeviceQueryValidationService;
import com.jycb.jycbz.modules.device.service.DeviceOwnershipValidationService;
import com.jycb.jycbz.modules.device.vo.DeviceBindVO;
import com.jycb.jycbz.modules.device.vo.DeviceFeeVO;
import com.jycb.jycbz.modules.device.vo.DeviceVO;
import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.modules.device.dto.DeviceTransferDTO;
import com.jycb.jycbz.modules.device.service.DeviceTransferService;
import com.jycb.jycbz.modules.finance.entity.CommissionDetail;
import com.jycb.jycbz.modules.finance.service.CommissionDetailService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备服务实现类
 */
@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    @Autowired
    private DeviceMapper deviceMapper;
    
    @Autowired
    private DeviceFeeService deviceFeeService;
    
    @Autowired
    private DeviceLogMapper deviceLogMapper;

    @Autowired
    private DeviceConvert deviceConvert;

    @Autowired
    private OrderService orderService;
    
    @Autowired
    private QrcodeUtils qrcodeUtils;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private CommissionDetailService commissionDetailService;

    @Autowired
    @Lazy
    private DeviceTransferService deviceTransferService;

    @Autowired
    private DeviceMaintenanceService deviceMaintenanceService;

    @Autowired
    private DeviceFaultReportService deviceFaultReportService;

    @Autowired
    @Lazy
    private DeviceCacheService deviceCacheService;

    @Autowired
    private DeviceQueryValidationService deviceQueryValidationService;

    @Autowired
    private DeviceOwnershipValidationService deviceOwnershipValidationService;

    private CleanTaskService cleanTaskService;
    
    @PostConstruct
    public void init() {
        // 懒加载依赖，避免循环依赖
        cleanTaskService = applicationContext.getBean("cleanModuleCleanTaskServiceImpl", CleanTaskService.class);
    }

    @Override
    public IPage<Device> pageDevices(Page<Device> page, String deviceNo, String deviceName, Integer deviceType, 
                                   Integer status, Integer entityId, Integer partnerId, Integer shopId) {
        return deviceMapper.pageDevices(page, deviceNo, deviceName, deviceType, status, entityId, partnerId, shopId);
    }

    @Override
    public boolean checkDeviceNoExists(String deviceNo) {
        if (!StringUtils.isNotBlank(deviceNo)) {
            return false;
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getDeviceNo, deviceNo)
                   .select(Device::getId); // 只查询ID字段即可

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean checkDeviceExists(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }
        
        return getById(deviceId) != null;
    }
    
    @Override
    public long countNewDevicesByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(Device::getCreateTime, startTime)
                   .le(Device::getCreateTime, endTime);
        return count(queryWrapper);
    }

    @Override
    public boolean checkDeviceAvailable(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }
        
        // 检查设备状态是否为在线
        if (device.getStatus() == null || device.getStatus() != 1) {
            return false;
        }
        
        // 检查设备是否已绑定
        if (device.getIsBound() == null || device.getIsBound() != 1) {
            return false;
        }
        
        // 检查设备是否未被使用
        return device.getInUse() == null || device.getInUse() != 1;
    }

    @Override
    public Map<String, Object> checkDeviceStatus(String deviceNo) {
        Map<String, Object> result = new HashMap<>();
        
        if (!StringUtils.isNotBlank(deviceNo)) {
            result.put("success", false);
            result.put("message", "设备编号不能为空");
            return result;
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getDeviceNo, deviceNo);
        
        Device device = getOne(queryWrapper);
        if (device == null) {
            result.put("success", false);
            result.put("message", "设备不存在");
            return result;
        }
        
        result.put("success", true);
        result.put("deviceId", device.getId());
        result.put("deviceNo", device.getDeviceNo());
        result.put("deviceName", device.getDeviceName());
        result.put("status", device.getStatus());
        result.put("inUse", device.getInUse());
        result.put("isBound", device.getIsBound());
        result.put("onlineStatus", device.getOnlineStatus());
        
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean bindDevice(String macAddress, String bindCode, Integer shopId) {
        if (!StringUtils.isNotBlank(macAddress) || !StringUtils.isNotBlank(bindCode) || shopId == null) {
            return false;
        }
        
        // 根据MAC地址查询设备
        Device device = getDeviceByMacAddress(macAddress);
        if (device == null) {
            return false;
        }
        
        // 检查绑定码是否匹配
        if (!bindCode.equals(device.getBindCode())) {
            return false;
        }
        
        // 更新设备绑定信息
        device.setIsBound(1);
        device.setShopId(shopId);
        device.setBindTime(LocalDateTime.now());
        
        return updateById(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindDevice(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }

        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }

        // 检查设备是否正在使用中
        if (device.getInUse() != null && device.getInUse() == 1) {
            return false;
        }

        // 记录旧的门店ID，用于清理缓存
        Integer oldShopId = device.getShopId();

        // 更新设备解绑信息
        device.setIsBound(0);
        device.setShopId(null);
        device.setPartnerId(null);
        device.setEntityId(null);
        device.setBindTime(null);
        device.setActivateTime(null);

        boolean result = updateById(device);

        if (result) {
            // 清理相关缓存
            if (deviceCacheService != null) {
                deviceCacheService.clearDeviceAllRelatedCache(device, oldShopId);
            }

            // 记录设备日志
            addDeviceLog(device.getId(), "UNBIND",
                String.format("解绑设备，原门店ID：%s", oldShopId));

            log.info("成功解绑设备，设备ID：{}，设备编号：{}，原门店ID：{}",
                    deviceId, device.getDeviceNo(), oldShopId);
        }

        return result;
    }

    @Override
    public boolean updateDeviceStatus(Integer deviceId, Integer status) {
        if (deviceId == null || status == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }
        
        device.setStatus(status);
        
        // 如果状态为离线，则更新在线状态
        if (status == 0) {
            device.setOnlineStatus(0);
        } else if (status == 1) {
            device.setOnlineStatus(1);
            device.setLastOnlineTime(LocalDateTime.now());
        }
        
        return updateById(device);
    }

    @Override
    public boolean updateDeviceUseStatus(String macAddress, Integer inUse) {
        if (StringUtils.isBlank(macAddress)) {
            return false;
        }
        
        Device device = getDeviceByMacAddress(macAddress);
        if (device == null) {
            return false;
        }
        
        return updateDeviceUseStatus(device.getId(), inUse == 1);
    }

    @Override
    public boolean updateDeviceUseStatus(Integer deviceId, boolean inUse) {
        if (deviceId == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }
        
        device.setInUse(inUse ? 1 : 0);
        
        return updateById(device);
    }

    @Override
    public boolean updateDeviceLocation(String macAddress, String latitude, String longitude, String address) {
        if (StringUtils.isBlank(macAddress)) {
            return false;
        }
        
        Device device = getDeviceByMacAddress(macAddress);
        if (device == null) {
            return false;
        }
        
        Double lat = null;
        Double lng = null;
        
        try {
            if (StringUtils.isNotBlank(latitude)) {
                lat = Double.parseDouble(latitude);
            }
            
            if (StringUtils.isNotBlank(longitude)) {
                lng = Double.parseDouble(longitude);
            }
        } catch (NumberFormatException e) {
            return false;
        }
        
        return updateDeviceLocation(device.getId(), lat, lng, address);
    }

    @Override
    public boolean updateDeviceLocation(Integer deviceId, Double latitude, Double longitude, String address) {
        if (deviceId == null || latitude == null || longitude == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }
        
        device.setLatitude(BigDecimal.valueOf(latitude));
        device.setLongitude(BigDecimal.valueOf(longitude));
        device.setAddress(address);
        device.setLastLocationTime(LocalDateTime.now());
        
        return updateById(device);
    }

    @Override
    public BigDecimal getDevicePrice(Integer deviceId) {
        if (deviceId == null) {
            return BigDecimal.ZERO;
        }
        
        DeviceFee deviceFee = deviceFeeService.getDeviceFeeByDeviceId(deviceId);
        if (deviceFee == null || deviceFee.getUsePrice() == null) {
            return BigDecimal.ZERO;
        }
        
        return deviceFee.getUsePrice();
    }

    @Override
    public List<Device> getDevicesByShop(Integer shopId) {
        if (shopId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getShopId, shopId);
        return list(queryWrapper);
    }

    @Override
    public List<Device> getDevicesByPartner(Integer partnerId) {
        if (partnerId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getPartnerId, partnerId);
        return list(queryWrapper);
    }

    @Override
    public List<Device> getDevicesByEntity(Integer entityId) {
        if (entityId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getEntityId, entityId);
        return list(queryWrapper);
    }

    @Override
    public Map<String, Object> getDeviceStatistics() {
        Map<String, Object> result = new HashMap<>();

        // 设备总数（只统计已绑定设备）
        LambdaQueryWrapper<Device> totalQuery = new LambdaQueryWrapper<>();
        totalQuery.eq(Device::getIsBound, 1);
        long totalCount = count(totalQuery);
        result.put("totalCount", totalCount);

        // 已绑定设备数（使用is_bound字段）
        LambdaQueryWrapper<Device> boundQuery = new LambdaQueryWrapper<>();
        boundQuery.eq(Device::getIsBound, 1);
        long boundCount = count(boundQuery);
        result.put("boundCount", boundCount);

        // 未绑定设备数（管理员可以看到，但不计入总数）
        LambdaQueryWrapper<Device> unboundQuery = new LambdaQueryWrapper<>();
        unboundQuery.eq(Device::getIsBound, 0);
        long unboundCount = count(unboundQuery);
        result.put("unboundCount", unboundCount);
        
        // 正常设备数
        LambdaQueryWrapper<Device> normalQuery = new LambdaQueryWrapper<>();
        normalQuery.eq(Device::getStatus, 1);
        long normalCount = count(normalQuery);
        result.put("normalCount", normalCount);
        
        // 维护中设备数
        LambdaQueryWrapper<Device> maintenanceQuery = new LambdaQueryWrapper<>();
        maintenanceQuery.eq(Device::getStatus, 2);
        long maintenanceCount = count(maintenanceQuery);
        result.put("maintenanceCount", maintenanceCount);
        
        // 故障设备数
        LambdaQueryWrapper<Device> faultQuery = new LambdaQueryWrapper<>();
        faultQuery.eq(Device::getStatus, 3);
        long faultCount = count(faultQuery);
        result.put("faultCount", faultCount);
        
        return result;
    }

    @Override
    public Map<String, Object> getDeviceStatistics(Integer entityId, Integer partnerId, Integer shopId) {
        // 如果deviceMapper.getDeviceStatistics方法能够处理这个查询，则使用它
        // 否则，我们可以手动实现
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getIsBound, 1); // 只统计已绑定设备
        if (entityId != null) {
            queryWrapper.eq(Device::getEntityId, entityId);
        }
        if (partnerId != null) {
            queryWrapper.eq(Device::getPartnerId, partnerId);
        }
        if (shopId != null) {
            queryWrapper.eq(Device::getShopId, shopId);
        }

        // 设备总数（只统计已绑定设备）
        long totalCount = count(queryWrapper);
        result.put("totalCount", totalCount);

        // 添加更多统计信息
        // ... (根据实际需求添加)

        return result;
    }

    @Override
    public Map<String, Object> getDeviceUsageStatistics(int shopId) {
        Map<String, Object> result = new HashMap<>();

        // 获取门店下的已绑定设备统计
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getShopId, shopId)
                   .eq(Device::getIsBound, 1); // 只统计已绑定设备

        long totalDevices = count(queryWrapper);
        result.put("totalDevices", totalDevices);

        // 在线设备数（只统计已绑定设备）
        LambdaQueryWrapper<Device> onlineQuery = new LambdaQueryWrapper<>();
        onlineQuery.eq(Device::getShopId, shopId)
                   .eq(Device::getIsBound, 1) // 只统计已绑定设备
                   .eq(Device::getOnlineStatus, 1);
        long onlineDevices = count(onlineQuery);
        result.put("onlineDevices", onlineDevices);

        // 使用中设备数
        LambdaQueryWrapper<Device> inUseQuery = new LambdaQueryWrapper<>();
        inUseQuery.eq(Device::getShopId, shopId).eq(Device::getInUse, 1);
        long inUseDevices = count(inUseQuery);
        result.put("inUseDevices", inUseDevices);

        return result;
    }

    @Override
    public Map<String, Object> getDeviceOwnership(Integer deviceId) {
        if (deviceId == null) {
            return null;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return null;
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("entityId", device.getEntityId());
        result.put("entityName", device.getEntityName());
        result.put("partnerId", device.getPartnerId());
        result.put("partnerName", device.getPartnerName());
        result.put("shopId", device.getShopId());
        result.put("shopName", device.getShopName());
        
        return result;
    }

    @Override
    public List<Device> getShopDevices(Long shopId, Integer status, Integer batteryLevel) {
        // 类型转换
        Integer shopIdInt = shopId != null ? shopId.intValue() : null;
        
        if (shopIdInt == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getShopId, shopIdInt)
                   .eq(Device::getIsBound, 1) // 确保设备已绑定
                   .select(Device::getId, Device::getDeviceNo, Device::getDeviceName, Device::getDeviceType,
                          Device::getEntityId, Device::getPartnerId, Device::getShopId, Device::getBindCode,
                          Device::getMacAddress, Device::getQrcodeUrl, Device::getIsBound, Device::getStatus,
                          Device::getOnlineStatus, Device::getInUse, Device::getCurrentUsers, Device::getMaxUsers,
                          Device::getLatitude, Device::getLongitude, Device::getBatteryLevel, Device::getAddress,
                          Device::getProvince, Device::getCity, Device::getDistrict, Device::getRegionId,
                          Device::getBindTime, Device::getLastOnlineTime, Device::getLastLocationTime,
                          Device::getActivateTime, Device::getRemark, Device::getSalesId, Device::getSalesName,
                          Device::getCreateTime, Device::getUpdateTime); // 明确指定查询字段

        if (status != null) {
            queryWrapper.eq(Device::getStatus, status);
        }

        if (batteryLevel != null) {
            queryWrapper.ge(Device::getBatteryLevel, batteryLevel);
        }

        List<Device> devices = list(queryWrapper);

        // 验证查询结果，确保只返回已绑定到该门店的设备
        return deviceQueryValidationService.validateShopDevices(shopIdInt, devices);
    }

    @Override
    public Device getDeviceDetail(Long deviceId) {
        // 类型转换
        Integer deviceIdInt = deviceId != null ? deviceId.intValue() : null;
        
        if (deviceIdInt == null) {
            return null;
        }
        
        return getById(deviceIdInt);
    }

    @Override
    public boolean reportDeviceFault(Long deviceId, Long shopId, String faultType, String description, List<String> imageUrls) {
        // 类型转换
        Integer deviceIdInt = deviceId != null ? deviceId.intValue() : null;
        Integer shopIdInt = shopId != null ? shopId.intValue() : null;
        
        if (deviceIdInt == null || shopIdInt == null) {
            return false;
        }
        
        try {
            // 查询设备信息
            Device device = getById(deviceIdInt);
            if (device == null) {
                log.error("设备不存在，deviceId={}", deviceIdInt);
                return false;
            }
            
            // 验证设备归属
            if (!shopIdInt.equals(device.getShopId())) {
                log.error("设备不属于该门店，deviceId={}, shopId={}", deviceIdInt, shopIdInt);
                return false;
            }
            
            // 更新设备状态为故障
            device.setStatus(3); // 3-故障
            updateById(device);
            
            // 记录设备故障日志
            String imageUrlsStr = imageUrls != null ? String.join(",", imageUrls) : "";
            String logDesc = String.format("故障类型：%s，描述：%s，图片：%s", faultType, description, imageUrlsStr);
            addDeviceLog(device.getId(), "报告故障", logDesc);
            
            // 这里可以添加故障工单创建逻辑
            // ...
            
            return true;
        } catch (Exception e) {
            log.error("报告设备故障失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getDeviceFaultProgress(Long deviceId, Long shopId) {
        // 类型转换
        Integer deviceIdInt = deviceId != null ? deviceId.intValue() : null;
        Integer shopIdInt = shopId != null ? shopId.intValue() : null;
        
        Map<String, Object> result = new HashMap<>();
        
        if (deviceIdInt == null || shopIdInt == null) {
            return result;
        }
        
        try {
            // 查询设备信息
            Device device = getById(deviceIdInt);
            if (device == null) {
                result.put("success", false);
                result.put("message", "设备不存在");
                return result;
            }
            
            // 验证设备归属
            if (!shopIdInt.equals(device.getShopId())) {
                result.put("success", false);
                result.put("message", "设备不属于该门店");
                return result;
            }
            
            // 查询最新的设备故障报告
            DeviceFaultReport latestFaultReport = deviceFaultReportService.lambdaQuery()
                .eq(DeviceFaultReport::getDeviceId, deviceIdInt)
                .orderByDesc(DeviceFaultReport::getReportTime)
                .last("LIMIT 1")
                .one();

            result.put("success", true);
            result.put("deviceId", deviceIdInt);
            result.put("deviceNo", device.getDeviceNo());
            result.put("deviceName", device.getDeviceName());
            result.put("status", device.getStatus());

            // 如果有故障报告，返回真实的故障进度数据
            if (latestFaultReport != null) {
                result.put("faultType", getFaultTypeName(latestFaultReport.getFaultType()));
                result.put("reportTime", latestFaultReport.getReportTime().toString());
                result.put("processStatus", getFaultStatusName(latestFaultReport.getStatus()));
                result.put("processPerson", latestFaultReport.getProcessorName() != null ?
                    latestFaultReport.getProcessorName() : "待分配");
                result.put("reportNo", latestFaultReport.getReportNo());
                result.put("faultDesc", latestFaultReport.getFaultDesc());
                result.put("processResult", latestFaultReport.getProcessResult());
                result.put("processTime", latestFaultReport.getProcessTime());

                // 根据状态计算预计完成时间
                if (latestFaultReport.getStatus() == 0) { // 待处理
                    result.put("estimatedCompletionTime", LocalDateTime.now().plusDays(1).toString());
                } else if (latestFaultReport.getStatus() == 1) { // 处理中
                    result.put("estimatedCompletionTime", LocalDateTime.now().plusHours(12).toString());
                } else { // 已处理或已关闭
                    result.put("estimatedCompletionTime", latestFaultReport.getProcessTime());
                }
            } else {
                // 没有故障报告时的默认值
                result.put("faultType", "无故障记录");
                result.put("reportTime", null);
                result.put("processStatus", "正常");
                result.put("processPerson", null);
                result.put("estimatedCompletionTime", null);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取设备故障进度失败", e);
            result.put("success", false);
            result.put("message", "获取设备故障进度失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public boolean processDeviceFault(Long deviceId, Long partnerId, String processResult, String remark) {
        // 类型转换
        Integer deviceIdInt = deviceId != null ? deviceId.intValue() : null;
        Integer partnerIdInt = partnerId != null ? partnerId.intValue() : null;
        
        if (deviceIdInt == null || partnerIdInt == null) {
            return false;
        }
        
        try {
            // 查询设备信息
            Device device = getById(deviceIdInt);
            if (device == null) {
                log.error("设备不存在，deviceId={}", deviceIdInt);
                return false;
            }
            
            // 验证设备归属
            if (!partnerIdInt.equals(device.getPartnerId())) {
                log.error("设备不属于该合作商，deviceId={}, partnerId={}", deviceIdInt, partnerIdInt);
                return false;
            }
            
            // 根据处理结果更新设备状态
            if ("已修复".equals(processResult)) {
                device.setStatus(1); // 1-正常
            } else if ("需要维修".equals(processResult)) {
                device.setStatus(2); // 2-维护中
            } else {
                device.setStatus(3); // 3-故障
            }
            
            updateById(device);
            
            // 记录设备故障处理日志
            String logDesc = String.format("处理结果：%s，备注：%s", processResult, remark);
            addDeviceLog(device.getId(), "处理故障", logDesc);
            
            // 这里可以添加故障工单更新逻辑
            // ...
            
            return true;
        } catch (Exception e) {
            log.error("处理设备故障失败", e);
            return false;
        }
    }

    @Override
    public boolean bindDeviceToShop(String macAddress, Integer partnerId, Integer shopId, String deviceName) {
        if (StringUtils.isBlank(macAddress) || partnerId == null || shopId == null) {
            return false;
        }
        
        Device device = getDeviceByMacAddress(macAddress);
        if (device == null) {
            return false;
        }
        
        device.setPartnerId(partnerId);
        device.setShopId(shopId);
        
        if (StringUtils.isNotBlank(deviceName)) {
            device.setDeviceName(deviceName);
        }
        
        device.setIsBound(1);
        device.setBindTime(LocalDateTime.now());
        
        return updateById(device);
    }

    @Override
    public Page<Device> getPartnerDevicePage(int pageNum, int pageSize, Long partnerId, Long shopId, Integer status, Integer onlineStatus, Integer batteryLevel) {
        // 构建查询条件
        LambdaQueryWrapper<Device> queryWrapper = Wrappers.lambdaQuery(Device.class);
        
        // 类型转换
        Integer partnerIdInt = partnerId != null ? partnerId.intValue() : null;
        Integer shopIdInt = shopId != null ? shopId.intValue() : null;
        
        // 添加查询条件 - 确保只查询已绑定的设备
        queryWrapper.eq(Device::getIsBound, 1); // 只查询已绑定的设备

        if (partnerIdInt != null) {
            queryWrapper.eq(Device::getPartnerId, partnerIdInt);
        }
        if (shopIdInt != null) {
            queryWrapper.eq(Device::getShopId, shopIdInt);
        }
        if (status != null) {
            queryWrapper.eq(Device::getStatus, status);
        }
        if (onlineStatus != null) {
            queryWrapper.eq(Device::getOnlineStatus, onlineStatus);
        }
        if (batteryLevel != null) {
            queryWrapper.ge(Device::getBatteryLevel, batteryLevel);
        }
        
        // 执行分页查询
        Page<Device> page = new Page<>(pageNum, pageSize);
        Page<Device> result = page(page, queryWrapper);

        // 验证查询结果，确保只返回已绑定到该合作商的设备
        List<Device> validatedDevices = deviceQueryValidationService.validatePartnerDevices(partnerIdInt, result.getRecords());

        // 进一步验证设备归属的完整性
        List<Integer> deviceIds = validatedDevices.stream().map(Device::getId).toList();
        DeviceOwnershipValidationService.DeviceOwnershipValidationResult ownershipResult =
            deviceOwnershipValidationService.validateDevicesBelongToPartner(deviceIds, partnerIdInt);

        if (!ownershipResult.isValid()) {
            log.warn("合作商 {} 的设备归属验证失败: {}", partnerIdInt, ownershipResult.getMessage());
            // 只保留验证通过的设备
            validatedDevices = validatedDevices.stream()
                .filter(device -> ownershipResult.getValidDeviceIds().contains(device.getId()))
                .toList();
        }

        result.setRecords(validatedDevices);
        return result;
    }

    @Override
    public Map<String, Object> getPartnerDeviceStatistics(Long partnerId) {
        // 类型转换
        Integer partnerIdInt = partnerId != null ? partnerId.intValue() : null;
        
        Map<String, Object> statistics = new HashMap<>();
        
        if (partnerIdInt == null) {
            return statistics;
        }
        
        try {
            // 修复统计查询（只统计已绑定设备）
            LambdaQueryWrapper<Device> query = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getIsBound, 1); // 只统计已绑定设备

            long totalCount = count(query);
            statistics.put("totalCount", totalCount);

            // 已绑定设备数（使用is_bound字段）
            LambdaQueryWrapper<Device> boundQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getIsBound, 1);
            long boundCount = count(boundQuery);
            statistics.put("boundCount", boundCount);

            // 未绑定设备数（管理员可以看到，但不计入主要统计）
            LambdaQueryWrapper<Device> unboundQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getIsBound, 0);
            long unboundCount = count(unboundQuery);
            statistics.put("unboundCount", unboundCount);
            
            // 正常设备数
            LambdaQueryWrapper<Device> normalQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getStatus, 1);
            long normalCount = count(normalQuery);
            statistics.put("normalCount", normalCount);
            
            // 维护中设备数
            LambdaQueryWrapper<Device> maintenanceQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getStatus, 2);
            long maintenanceCount = count(maintenanceQuery);
            statistics.put("maintenanceCount", maintenanceCount);
            
            // 故障设备数
            LambdaQueryWrapper<Device> faultQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getStatus, 3);
            long faultCount = count(faultQuery);
            statistics.put("faultCount", faultCount);
            
            // 在线设备数（只统计已绑定设备）
            LambdaQueryWrapper<Device> onlineQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getIsBound, 1) // 只统计已绑定设备
                .eq(Device::getOnlineStatus, 1);
            long onlineCount = count(onlineQuery);
            statistics.put("onlineCount", onlineCount);

            // 离线设备数（只统计已绑定设备）
            LambdaQueryWrapper<Device> offlineQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getIsBound, 1) // 只统计已绑定设备
                .eq(Device::getOnlineStatus, 0);
            long offlineCount = count(offlineQuery);
            statistics.put("offlineCount", offlineCount);

            // 使用中设备数（只统计已绑定设备）
            LambdaQueryWrapper<Device> inUseQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getPartnerId, partnerIdInt)
                .eq(Device::getIsBound, 1) // 只统计已绑定设备
                .eq(Device::getInUse, 1);
            long inUseCount = count(inUseQuery);
            statistics.put("inUseCount", inUseCount);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取合作商设备统计失败", e);
            statistics.put("error", "获取合作商设备统计失败: " + e.getMessage());
            return statistics;
        }
    }
    
    @Override
    public Long countDevicesByEntityId(Long entityId) {
        if (entityId == null) {
            return 0L;
        }
        
        // 类型转换
        Integer entityIdInt = entityId.intValue();
        
        LambdaQueryWrapper<Device> queryWrapper = Wrappers.lambdaQuery(Device.class)
            .eq(Device::getEntityId, entityIdInt)
            .eq(Device::getIsBound, 1); // 只统计已绑定设备

        return count(queryWrapper);
    }

    @Override
    public PageResult<DeviceVO> getDeviceList(Integer page, Integer size, String deviceNo, Integer status,
                                     Integer partnerId, Integer shopId, Integer isBound) {
        // 构建分页参数
        Page<Device> pageParam = new Page<>(page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.isNotBlank(deviceNo)) {
            queryWrapper.like(Device::getDeviceNo, deviceNo);
        }
        if (status != null) {
            queryWrapper.eq(Device::getStatus, status);
        }
        if (partnerId != null) {
            queryWrapper.eq(Device::getPartnerId, partnerId);
        }
        if (shopId != null) {
            queryWrapper.eq(Device::getShopId, shopId);
        }
        if (isBound != null) {
            queryWrapper.eq(Device::getIsBound, isBound);
            if (isBound == 1) {
                // 已绑定的设备必须有门店ID
                queryWrapper.isNotNull(Device::getShopId);
            } else {
                // 未绑定的设备不应该有门店ID
                queryWrapper.isNull(Device::getShopId)
                           .isNull(Device::getPartnerId)
                           .isNull(Device::getEntityId);
            }
        }
        
        // 排序
        queryWrapper.orderByDesc(Device::getCreateTime);
        
        // 执行分页查询并正确处理PageResult的构造
        IPage<Device> pageResult = page(pageParam, queryWrapper);
        List<DeviceVO> deviceVOList = pageResult.getRecords().stream()
                .map(device -> {
                    DeviceVO vo = deviceConvert.toVO(device);
                    // 添加订单统计信息
                    enrichDeviceOrderStatistics(vo, device.getId());
                    return vo;
                })
                .collect(Collectors.toList());

        PageResult<DeviceVO> result = new PageResult<>();
        result.setTotal(pageResult.getTotal());
        result.setList(deviceVOList);
        return result;
    }

    /**
     * 丰富设备订单统计信息
     */
    private void enrichDeviceOrderStatistics(DeviceVO vo, Integer deviceId) {
        try {
            // 首先验证设备是否已绑定
            Device device = getById(deviceId);
            if (device == null || device.getIsBound() == null || device.getIsBound() != 1) {
                log.warn("设备 {} 未绑定，跳过订单统计数据填充", deviceId);
                // 设置默认值
                vo.setTotalOrders(0L);
                vo.setTodayOrders(0);
                vo.setMonthOrders(0);
                vo.setTotalRevenue(BigDecimal.ZERO);
                vo.setTodayRevenue(BigDecimal.ZERO);
                return;
            }

            LocalDate today = LocalDate.now();
            LocalDate monthStart = today.withDayOfMonth(1);

            // 总订单数
            long totalOrders = orderService.lambdaQuery()
                    .eq(Order::getDeviceId, deviceId)
                    .count();
            vo.setTotalOrders(totalOrders);

            // 今日订单数
            long todayOrdersCount = orderService.lambdaQuery()
                    .eq(Order::getDeviceId, deviceId)
                    .ge(Order::getCreateTime, today.atStartOfDay())
                    .lt(Order::getCreateTime, today.plusDays(1).atStartOfDay())
                    .count();
            vo.setTodayOrders((int) todayOrdersCount);

            // 本月订单数
            long monthOrdersCount = orderService.lambdaQuery()
                    .eq(Order::getDeviceId, deviceId)
                    .ge(Order::getCreateTime, monthStart.atStartOfDay())
                    .count();
            vo.setMonthOrders((int) monthOrdersCount);

            // 计算门店分成收入
            if (totalOrders > 0) {
                // 获取所有已完成且已支付的订单
                List<Order> completedOrders = orderService.lambdaQuery()
                        .eq(Order::getDeviceId, deviceId)
                        .eq(Order::getOrderStatus, 2) // 已完成
                        .eq(Order::getPayStatus, 1) // 已支付
                        .eq(Order::getCommissionStatus, 1) // 已分成
                        .list();

                if (!completedOrders.isEmpty()) {
                    List<String> orderIds = completedOrders.stream()
                            .map(order -> order.getId().toString())
                            .collect(Collectors.toList());

                    // 从分成详情表获取门店收入
                    List<CommissionDetail> commissionDetails = commissionDetailService.lambdaQuery()
                            .in(CommissionDetail::getOrderId, orderIds)
                            .list();

                    // 总收入
                    BigDecimal totalRevenue = commissionDetails.stream()
                            .map(CommissionDetail::getShopAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setTotalRevenue(totalRevenue);

                    // 今日收入
                    List<String> todayOrderIds = completedOrders.stream()
                            .filter(order -> order.getCreateTime().toLocalDate().equals(today))
                            .map(order -> order.getId().toString())
                            .collect(Collectors.toList());

                    if (!todayOrderIds.isEmpty()) {
                        BigDecimal todayRevenue = commissionDetails.stream()
                                .filter(detail -> todayOrderIds.contains(detail.getOrderId()))
                                .map(CommissionDetail::getShopAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        vo.setTodayRevenue(todayRevenue);
                    } else {
                        vo.setTodayRevenue(BigDecimal.ZERO);
                    }
                } else {
                    vo.setTotalRevenue(BigDecimal.ZERO);
                    vo.setTodayRevenue(BigDecimal.ZERO);
                }
            } else {
                vo.setTotalRevenue(BigDecimal.ZERO);
                vo.setTodayRevenue(BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("获取设备订单统计失败，设备ID: {}", deviceId, e);
            vo.setTotalOrders(0L);
            vo.setTodayOrders(0);
            vo.setMonthOrders(0);
            vo.setTotalRevenue(BigDecimal.ZERO);
            vo.setTodayRevenue(BigDecimal.ZERO);
        }
    }

    @Override
    public DeviceVO getDeviceDetail(Integer id) {
        Device device = deviceMapper.selectById(id);
        if (device == null) {
            return null;
        }
        return deviceConvert.toVO(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addDevice(Device device) {
        // 必要的验证
        if (device == null) {
            throw new BusinessException("设备信息不能为空");
        }

        // 验证必填字段
        if (StringUtils.isBlank(device.getDeviceNo())) {
            throw new BusinessException("设备编号不能为空");
        }
        if (StringUtils.isBlank(device.getMacAddress())) {
            throw new BusinessException("MAC地址不能为空");
        }
        if (StringUtils.isBlank(device.getBindCode())) {
            throw new BusinessException("绑定码不能为空");
        }

        // 验证唯一性约束
        if (checkDeviceNoExists(device.getDeviceNo())) {
            throw new BusinessException("设备编号已存在：" + device.getDeviceNo());
        }

        Device existingByMac = getDeviceByMacAddress(device.getMacAddress());
        if (existingByMac != null) {
            throw new BusinessException("MAC地址已存在：" + device.getMacAddress());
        }

        Device existingByBindCode = getDeviceByBindCode(device.getBindCode());
        if (existingByBindCode != null) {
            throw new BusinessException("绑定码已存在：" + device.getBindCode());
        }

        // 设置默认值
        if (device.getCreateTime() == null) {
            device.setCreateTime(LocalDateTime.now());
        }
        if (device.getUpdateTime() == null) {
            device.setUpdateTime(LocalDateTime.now());
        }
        if (device.getStatus() == null) {
            device.setStatus(1); // 默认正常状态
        }
        if (device.getIsBound() == null) {
            device.setIsBound(0); // 默认未绑定
        }
        if (device.getOnlineStatus() == null) {
            device.setOnlineStatus(0); // 默认离线
        }
        if (device.getInUse() == null) {
            device.setInUse(0); // 默认未使用
        }
        if (device.getCurrentUsers() == null) {
            device.setCurrentUsers(0); // 默认0个用户
        }
        if (device.getMaxUsers() == null) {
            device.setMaxUsers(1); // 默认最大1个用户
        }
        if (device.getBatteryLevel() == null) {
            device.setBatteryLevel(100); // 默认满电
        }
        if (device.getDeviceType() == null) {
            device.setDeviceType(1); // 默认类型1
        }

        // 保存设备
        boolean result = save(device);

        if (result) {
            // 记录设备日志
            addDeviceLog(device.getId(), "CREATE", "创建设备：" + device.getDeviceNo());
            log.info("成功创建设备，设备编号：{}，MAC地址：{}", device.getDeviceNo(), device.getMacAddress());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDevice(Device device) {
        // 必要的验证
        if (device == null || device.getId() == null) {
            return false;
        }
        
        // 更新设备
        return updateById(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDevice(Integer id) {
        // 必要的验证
        if (id == null) {
            return false;
        }
        
        // 删除设备
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindDevice(DeviceBindVO bindVO) {
        // 必要的验证
        if (bindVO == null) {
            throw new BusinessException("绑定信息不能为空");
        }

        if (StringUtils.isBlank(bindVO.getMacAddress())) {
            throw new BusinessException("MAC地址不能为空");
        }

        if (bindVO.getShopId() == null) {
            throw new BusinessException("门店ID不能为空");
        }

        // 查找设备
        Device device = getDeviceByMacAddress(bindVO.getMacAddress());
        if (device == null) {
            throw new BusinessException("设备不存在：" + bindVO.getMacAddress());
        }

        // 检查设备是否已绑定
        if (device.getIsBound() == 1) {
            throw new BusinessException("设备已绑定到门店，无法重复绑定");
        }

        // 检查设备状态
        if (device.getStatus() != 1) {
            throw new BusinessException("设备状态异常，无法绑定");
        }

        // 验证业务主体、合作商、门店的关联关系
        if (bindVO.getEntityId() != null && !bindVO.getEntityId().equals(device.getEntityId())) {
            device.setEntityId(bindVO.getEntityId());
        }

        if (bindVO.getPartnerId() != null && !bindVO.getPartnerId().equals(device.getPartnerId())) {
            device.setPartnerId(bindVO.getPartnerId());
        }

        // 更新设备绑定信息
        device.setShopId(bindVO.getShopId());
        device.setIsBound(1);
        device.setBindTime(LocalDateTime.now());

        if (StringUtils.isNotBlank(bindVO.getDeviceName())) {
            device.setDeviceName(bindVO.getDeviceName());
        }

        if (bindVO.getDeviceType() != null) {
            device.setDeviceType(bindVO.getDeviceType());
        }

        // 更新设备信息
        boolean deviceUpdated = updateById(device);

        if (!deviceUpdated) {
            throw new BusinessException("更新设备绑定信息失败");
        }

        // 清理相关缓存
        if (deviceCacheService != null) {
            deviceCacheService.clearDeviceAllRelatedCache(device, null);
        }

        // 创建或更新设备费用配置
        if (bindVO.getUsePrice() != null && bindVO.getBillingType() != null) {
            createOrUpdateDeviceFee(device.getId(), bindVO);
        }

        // 记录设备日志
        addDeviceLog(device.getId(), "BIND",
            String.format("绑定设备到门店，门店ID：%d，房间号：%s",
                bindVO.getShopId(), bindVO.getRoomNumber()));

        log.info("成功绑定设备到门店，设备MAC：{}，门店ID：{}",
            bindVO.getMacAddress(), bindVO.getShopId());

        return true;
    }

    /**
     * 创建或更新设备费用配置
     */
    private void createOrUpdateDeviceFee(Integer deviceId, DeviceBindVO bindVO) {
        try {
            // 检查是否已存在设备费用配置
            LambdaQueryWrapper<DeviceFee> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceFee::getDeviceId, deviceId);
            DeviceFee existingFee = deviceFeeService.getOne(queryWrapper);

            if (existingFee != null) {
                // 更新现有配置
                existingFee.setPrice(bindVO.getUsePrice());
                existingFee.setFeeType(bindVO.getBillingType());
                existingFee.setPriceDesc(bindVO.getPriceDesc());
                existingFee.setUpdateTime(LocalDateTime.now());
                deviceFeeService.updateById(existingFee);
                log.info("更新设备费用配置，设备ID：{}，价格：{}", deviceId, bindVO.getUsePrice());
            } else {
                // 创建新配置
                DeviceFee deviceFee = new DeviceFee();
                deviceFee.setDeviceId(deviceId);
                deviceFee.setEntityId(bindVO.getEntityId());
                deviceFee.setPartnerId(bindVO.getPartnerId());
                deviceFee.setShopId(bindVO.getShopId());
                deviceFee.setFeeName("设备使用费");
                deviceFee.setFeeType(bindVO.getBillingType());
                deviceFee.setPrice(bindVO.getUsePrice());
                deviceFee.setUnit(getBillingUnit(bindVO.getBillingType()));
                deviceFee.setPriceDesc(bindVO.getPriceDesc());
                deviceFee.setStatus(1); // 启用状态
                deviceFee.setIsDefault(1); // 设为默认
                deviceFee.setCreateTime(LocalDateTime.now());
                deviceFee.setUpdateTime(LocalDateTime.now());
                deviceFeeService.save(deviceFee);
                log.info("创建设备费用配置，设备ID：{}，价格：{}", deviceId, bindVO.getUsePrice());
            }
        } catch (Exception e) {
            log.error("创建或更新设备费用配置失败，设备ID：{}", deviceId, e);
            // 不抛出异常，避免影响设备绑定主流程
        }
    }

    /**
     * 根据计费方式获取计费单位
     */
    private String getBillingUnit(Integer billingType) {
        switch (billingType) {
            case 1: return "次";
            case 2: return "分钟";
            case 3: return "天";
            default: return "次";
        }
    }

    @Override
    public Map<String, Object> getShopDeviceStatistics(Integer shopId) {
        Map<String, Object> result = new HashMap<>();
        
        // 设备总数（只统计已绑定设备）
        LambdaQueryWrapper<Device> totalQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getShopId, shopId)
                .eq(Device::getIsBound, 1); // 只统计已绑定设备
        long totalCount = count(totalQuery);
        result.put("totalCount", totalCount);

        // 正常设备数（只统计已绑定设备）
        LambdaQueryWrapper<Device> normalQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getShopId, shopId)
                .eq(Device::getIsBound, 1) // 只统计已绑定设备
                .eq(Device::getStatus, 1);
        long normalCount = count(normalQuery);
        result.put("normalCount", normalCount);
        
        // 维护中设备数
        LambdaQueryWrapper<Device> maintenanceQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getShopId, shopId)
                .eq(Device::getStatus, 2);
        long maintenanceCount = count(maintenanceQuery);
        result.put("maintenanceCount", maintenanceCount);
        
        // 故障设备数
        LambdaQueryWrapper<Device> faultQuery = Wrappers.lambdaQuery(Device.class)
                .eq(Device::getShopId, shopId)
                .eq(Device::getStatus, 3);
        long faultCount = count(faultQuery);
        result.put("faultCount", faultCount);
        
        return result;
    }

    @Override
    public List<DeviceVO> getDevicesByPartnerId(Integer partnerId) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getPartnerId, partnerId);
        queryWrapper.orderByDesc(Device::getCreateTime);
        
        List<Device> deviceList = deviceMapper.selectList(queryWrapper);
        return deviceList.stream()
                .map(deviceConvert::toVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DeviceVO> getDevicesByShopId(Integer shopId) {
        if (shopId == null) {
            return List.of();
        }

        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getShopId, shopId);
        
        List<Device> devices = list(queryWrapper);
        return devices.stream()
                .map(deviceConvert::toVO)
                .collect(Collectors.toList());
    }

    @Override
    public Device getDeviceByMacAddress(String macAddress) {
        if (!StringUtils.isNotBlank(macAddress)) {
            return null;
        }
        
        return baseMapper.selectByMacAddress(macAddress);
    }

    @Override
    public String generateQrcodeUrl(String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            throw new BusinessException("MAC地址不能为空");
        }
        
        // 生成二维码内容，使用MAC地址作为唯一标识
        String content = "jycb://device?mac=" + macAddress;
        
        // 生成二维码并返回URL
        return qrcodeUtils.generateQrcodeUrl(content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImportDevices(List<DeviceCreateDTO> deviceList) {
        Map<String, Object> result = new HashMap<>();

        if (deviceList == null || deviceList.isEmpty()) {
            result.put("success", false);
            result.put("message", "导入设备列表不能为空");
            result.put("totalCount", 0);
            result.put("successCount", 0);
            result.put("failedCount", 0);
            return result;
        }

        int totalCount = deviceList.size();
        int successCount = 0;
        int failedCount = 0;
        List<String> failedReasons = new ArrayList<>();

        for (DeviceCreateDTO createDTO : deviceList) {
            try {
                // 检查MAC地址是否已存在
                if (StringUtils.isNotBlank(createDTO.getMacAddress())) {
                    Device existingDevice = getDeviceByMacAddress(createDTO.getMacAddress());
                    if (existingDevice != null) {
                        failedReasons.add("MAC地址已存在: " + createDTO.getMacAddress());
                        failedCount++;
                        continue;
                    }
                }

                // 创建设备
                DeviceVO deviceVO = createDevice(createDTO);
                if (deviceVO != null) {
                    successCount++;
                } else {
                    failedReasons.add("创建设备失败: " + createDTO.getMacAddress());
                    failedCount++;
                }

            } catch (Exception e) {
                log.error("批量导入设备失败: {}", createDTO.getMacAddress(), e);
                failedReasons.add("导入失败: " + createDTO.getMacAddress() + " - " + e.getMessage());
                failedCount++;
            }
        }

        result.put("success", failedCount == 0);
        result.put("message", String.format("导入完成，成功%d个，失败%d个", successCount, failedCount));
        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("failedReasons", failedReasons);

        return result;
    }

    @Override
    public String exportDeviceList(String deviceNo, Integer status, Integer partnerId, Integer shopId, Integer isBound) {
        // 构建查询条件
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(deviceNo)) {
            queryWrapper.like(Device::getDeviceNo, deviceNo);
        }
        if (status != null) {
            queryWrapper.eq(Device::getStatus, status);
        }
        if (partnerId != null) {
            queryWrapper.eq(Device::getPartnerId, partnerId);
        }
        if (shopId != null) {
            queryWrapper.eq(Device::getShopId, shopId);
        }
        if (isBound != null) {
            if (isBound == 1) {
                queryWrapper.isNotNull(Device::getShopId);
            } else {
                queryWrapper.isNull(Device::getShopId);
            }
        }
        
        // 查询设备列表
        List<Device> deviceList = deviceMapper.selectList(queryWrapper);
        
        if (CollectionUtils.isEmpty(deviceList)) {
            return "没有符合条件的设备数据";
        }
        
        try {
            // 创建导出目录
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 生成文件名
            String fileName = "device_" + System.currentTimeMillis() + ".xlsx";
            String filePath = exportDir + "/" + fileName;
            
            // 创建Excel工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // 创建工作表
                XSSFSheet sheet = workbook.createSheet("设备列表");
                
                // 创建表头
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("设备编号");
                headerRow.createCell(1).setCellValue("设备名称");
                headerRow.createCell(2).setCellValue("MAC地址");
                headerRow.createCell(3).setCellValue(getDeviceTypeName(deviceList.get(0).getDeviceType()));
                headerRow.createCell(4).setCellValue(getDeviceStatusName(deviceList.get(0).getStatus()));
                headerRow.createCell(5).setCellValue("所属合作商");
                headerRow.createCell(6).setCellValue("所属门店");
                headerRow.createCell(7).setCellValue("是否绑定");
                headerRow.createCell(8).setCellValue("创建时间");
                
                // 填充数据
                int rowNum = 1;
                for (Device device : deviceList) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(device.getDeviceNo());
                    row.createCell(1).setCellValue(device.getDeviceName() != null ? device.getDeviceName() : "");
                    row.createCell(2).setCellValue(device.getMacAddress());
                    row.createCell(3).setCellValue(getDeviceTypeName(device.getDeviceType()));
                    row.createCell(4).setCellValue(getDeviceStatusName(device.getStatus()));
                    row.createCell(5).setCellValue(device.getPartnerName() != null ? device.getPartnerName() : "");
                    row.createCell(6).setCellValue(device.getShopName() != null ? device.getShopName() : "");
                    row.createCell(7).setCellValue(device.getShopId() != null ? "已绑定" : "未绑定");
                    row.createCell(8).setCellValue(device.getCreateTime() != null ? device.getCreateTime().toString() : "");
                }
                
                // 调整列宽
                for (int i = 0; i < 9; i++) {
                    sheet.autoSizeColumn(i);
                }
                
                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }
            
            // 返回文件下载URL
            return "/export/" + fileName;
            
        } catch (Exception e) {
            log.error("导出设备列表失败", e);
            throw new RuntimeException("导出设备列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取设备类型名称
     */
    private String getDeviceTypeName(Integer deviceType) {
        if (deviceType == null) {
            return "";
        }
        switch (deviceType) {
            case 1: return "按摩椅";
            case 2: return "足浴盆";
            case 3: return "按摩枕";
            default: return "其他";
        }
    }
    
    /**
     * 获取设备状态名称
     */
    private String getDeviceStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1: return "正常";
            case 2: return "维护中";
            case 3: return "故障";
            default: return "未知";
        }
    }

    @Override
    public boolean updateById(Device device) {
        // 调用父类的updateById方法
        return super.updateById(device);
    }

    /**
     * 添加设备日志
     */
    private void addDeviceLog(Integer deviceId, String operation, String description) {
        try {
            // 获取设备信息以获取设备编号
            Device device = getById(deviceId);
            if (device == null) {
                log.warn("设备不存在，无法记录日志，设备ID: {}", deviceId);
                return;
            }

            DeviceLog log = new DeviceLog();
            log.setDeviceId(deviceId);
            log.setDeviceNo(device.getDeviceNo()); // 设置设备编号
            // 使用修改后的DeviceLog字段
            log.setOperationType(1); // 假设1表示特定类型
            log.setLogType("operation"); // 设置日志类型为操作日志
            log.setContent(description);
            log.setCreateTime(LocalDateTime.now()); // 设置创建时间

            // 保存日志
            deviceLogMapper.insert(log);
        } catch (Exception e) {
            log.error("保存设备日志失败", e);
        }
    }

    /**
     * 根据绑定码获取设备
     *
     * @param bindCode 绑定码
     * @return 设备信息
     */
    @Override
    public Device getDeviceByBindCode(String bindCode) {
        if (!StringUtils.isNotBlank(bindCode)) {
            return null;
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getBindCode, bindCode);
        
        return getOne(queryWrapper);
    }

    @Override
    public DeviceVO getDeviceVOByMacAddress(String macAddress) {
        if (!StringUtils.isNotBlank(macAddress)) {
            return null;
        }
        
        Device device = getDeviceByMacAddress(macAddress);
        return device != null ? deviceConvert.toVO(device) : null;
    }

    @Override
    public boolean requestDeviceIssue(Integer shopId, Map<String, String> issueDetails) {
        if (shopId == null || issueDetails == null || issueDetails.isEmpty()) {
            return false;
        }
        
        // 获取门店设备
        List<Device> devices = getDevicesByShop(shopId);
        if (devices.isEmpty()) {
            return false;
        }
        
        // 获取设备ID
        String deviceIdStr = issueDetails.get("deviceId");
        if (StringUtils.isBlank(deviceIdStr)) {
            return false;
        }
        
        try {
            Integer deviceId = Integer.parseInt(deviceIdStr);
            Device device = getById(deviceId);
            
            if (device == null || !shopId.equals(device.getShopId())) {
                return false;
            }
            
            // 更新设备状态为故障
            device.setStatus(3); // 假设3表示故障状态
            updateById(device);
            
            // 记录设备故障日志
            String issueType = issueDetails.get("issueType");
            String description = issueDetails.get("description");
            String logContent = "问题类型：" + issueType + "，描述：" + description;
            addDeviceLog(deviceId, "报告故障", logContent);
            
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean requestDeviceMaintenance(Integer shopId, Map<String, String> maintenanceDetails) {
        try {
            // 验证必要参数
            String deviceIdStr = maintenanceDetails.get("deviceId");
            String maintenanceType = maintenanceDetails.get("maintenanceType");
            String maintenanceReason = maintenanceDetails.get("maintenanceReason");
            String expectedTimeStr = maintenanceDetails.get("expectedTime");
            String applicantName = maintenanceDetails.get("applicantName");
            String contactPhone = maintenanceDetails.get("contactPhone");

            if (StringUtils.isBlank(deviceIdStr) || StringUtils.isBlank(maintenanceReason)) {
                log.error("设备维护申请参数不完整");
                return false;
            }

            Integer deviceId = Integer.parseInt(deviceIdStr);
            Device device = getById(deviceId);

            if (device == null || !shopId.equals(device.getShopId())) {
                log.error("设备不存在或不属于该门店，deviceId={}, shopId={}", deviceId, shopId);
                return false;
            }

            // 创建维护申请记录
            DeviceMaintenance maintenance = new DeviceMaintenance();
            maintenance.setRequestNo(generateMaintenanceNo());
            maintenance.setDeviceId(deviceId);
            maintenance.setShopId(shopId);
            maintenance.setMaintenanceType(StringUtils.isNotBlank(maintenanceType) ? Integer.parseInt(maintenanceType) : 1);
            maintenance.setDescription(maintenanceReason);
            maintenance.setUrgency(2); // 默认中等紧急程度
            maintenance.setStatus(0); // 0-待安排
            maintenance.setCreateTime(LocalDateTime.now());
            maintenance.setUpdateTime(LocalDateTime.now());

            // 解析期望维护时间
            if (StringUtils.isNotBlank(expectedTimeStr)) {
                try {
                    maintenance.setPreferredTime(LocalDateTime.parse(expectedTimeStr));
                } catch (Exception e) {
                    log.warn("期望维护时间格式错误，使用默认时间: {}", expectedTimeStr);
                    maintenance.setPreferredTime(LocalDateTime.now().plusDays(1));
                }
            } else {
                maintenance.setPreferredTime(LocalDateTime.now().plusDays(1));
            }

            // 保存维护申请
            boolean saved = deviceMaintenanceService.save(maintenance);

            if (saved) {
                // 记录设备日志
                String logContent = String.format("申请设备维护，维护类型：%s，原因：%s",
                    getMaintenanceTypeName(maintenance.getMaintenanceType()), maintenanceReason);
                addDeviceLog(deviceId, "申请维护", logContent);

                log.info("设备维护申请成功，申请编号：{}", maintenance.getRequestNo());
            }

            return saved;

        } catch (Exception e) {
            log.error("申请设备维护失败", e);
            return false;
        }
    }

    /**
     * 生成维护申请编号
     */
    private String generateMaintenanceNo() {
        return "MT" + System.currentTimeMillis();
    }

    /**
     * 获取维护类型名称
     */
    private String getMaintenanceTypeName(Integer maintenanceType) {
        if (maintenanceType == null) return "未知";
        switch (maintenanceType) {
            case 1: return "定期维护";
            case 2: return "故障维护";
            case 3: return "紧急维护";
            case 4: return "其他";
            default: return "未知维护";
        }
    }

    @Override
    public boolean requestDeviceCleaning(Integer shopId, Map<String, String> cleaningDetails) {
        // 调用CleanTaskService的requestDeviceCleaning方法
        return cleanTaskService.requestDeviceCleaning(shopId, cleaningDetails);
    }

    @Override
    public String regenerateQrcode(Integer id) {
        Device device = getById(id);
        if (device == null || StringUtils.isBlank(device.getMacAddress())) {
            throw new BusinessException("设备不存在或MAC地址为空");
        }
        
        String qrcodeUrl = generateQrcodeUrl(device.getMacAddress());
        device.setQrcodeUrl(qrcodeUrl);
        updateById(device);
        
        return qrcodeUrl;
    }

    @Override
    public boolean updateDeviceOnlineStatus(String macAddress, Integer onlineStatus) {
        if (StringUtils.isBlank(macAddress) || onlineStatus == null) {
            return false;
        }
        
        Device device = getDeviceByMacAddress(macAddress);
        if (device == null) {
            return false;
        }
        
        device.setOnlineStatus(onlineStatus);
        if (onlineStatus == 1) {
            device.setLastOnlineTime(LocalDateTime.now());
        }
        
        return updateById(device);
    }

    @Override
    public Device getDeviceByDeviceNo(String deviceNo) {
        if (StringUtils.isBlank(deviceNo)) {
            return null;
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getDeviceNo, deviceNo);
        
        return getOne(queryWrapper);
    }

    @Override
    public boolean reportDeviceIssue(Integer shopId, Map<String, String> issueDetails) {
        return requestDeviceIssue(shopId, issueDetails);
    }

    /**
     * 使用Long参数的bindDeviceToShop实现
     */
    @Override
    public boolean bindDeviceToShop(String macAddress, Long partnerId, Long shopId, String roomNumber, String deviceName) {
        if (StringUtils.isBlank(macAddress) || partnerId == null || shopId == null) {
            return false;
        }
        
        // 类型转换
        Integer partnerIdInt = partnerId.intValue();
        Integer shopIdInt = shopId.intValue();
        
        Device device = getDeviceByMacAddress(macAddress);
        if (device == null) {
            return false;
        }
        
        device.setPartnerId(partnerIdInt);
        device.setShopId(shopIdInt);
        
        // 如果设备类有roomNumber字段，可以设置它
        // 这里省略设置房间号的代码
        
        if (StringUtils.isNotBlank(deviceName)) {
            device.setDeviceName(deviceName);
        }
        
        device.setIsBound(1);
        device.setBindTime(LocalDateTime.now());
        
        return updateById(device);
    }

    private DeviceFee convertToDeviceFee(DeviceFeeVO deviceFeeVO) {
        if (deviceFeeVO == null) {
            return null;
        }
        DeviceFee deviceFee = new DeviceFee();
        BeanUtils.copyProperties(deviceFeeVO, deviceFee);
        return deviceFee;
    }

    /**
     * 统计在线设备数量
     *
     * @return 在线设备数量
     */
    @Override
    public Long countOnlineDevices() {
        return lambdaQuery()
                .eq(Device::getOnlineStatus, 1)
                .count();
    }

    /**
     * 统计离线设备数量
     *
     * @return 离线设备数量
     */
    @Override
    public Long countOfflineDevices() {
        return lambdaQuery()
                .eq(Device::getOnlineStatus, 0)
                .count();
    }

    /**
     * 统计故障设备数量
     *
     * @return 故障设备数量
     */
    @Override
    public Long countFaultDevices() {
        return lambdaQuery()
                .eq(Device::getStatus, 3) // 3-故障状态
                .count();
    }
    
    /**
     * 获取设备地区分布
     *
     * @return 各地区设备数量分布
     */
    @Override
    public Map<String, Object> getDeviceAreaDistribution() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 按省份统计设备数量
            List<Map<String, Object>> provinceDistribution = baseMapper.countGroupByProvince();
            result.put("provinceDistribution", provinceDistribution);
            
            // 按城市统计设备数量
            List<Map<String, Object>> cityDistribution = baseMapper.countGroupByCity();
            result.put("cityDistribution", cityDistribution);
            
        } catch (Exception e) {
            log.error("获取设备地区分布数据失败", e);
            result.put("error", "获取地区分布数据失败");
        }
        
        return result;
    }

    @Override
    public Device getDeviceById(Long id) {
        if (id == null) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementCurrentUsers(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            log.warn("增加设备使用人数失败：设备[{}]不存在", deviceId);
            return false;
        }
        
        // 初始化当前使用人数和最大可用人数（兼容旧数据）
        if (device.getCurrentUsers() == null) {
            device.setCurrentUsers(0);
        }
        if (device.getMaxUsers() == null) {
            device.setMaxUsers(3); // 默认值
        }
        
        // 检查是否达到最大使用人数
        if (device.getCurrentUsers() >= device.getMaxUsers()) {
            log.warn("设备[{}]已达到最大使用人数: {}，无法继续增加", deviceId, device.getMaxUsers());
            return false;
        }
        
        // 增加当前使用人数
        int previousUsers = device.getCurrentUsers();
        device.setCurrentUsers(device.getCurrentUsers() + 1);
        
        // 更新设备状态
        if (device.getCurrentUsers() > 0) {
            device.setInUse(1); // 使用中
            
            // 记录设备使用人数变化，但不改变设备状态
            if (device.getCurrentUsers() >= device.getMaxUsers()) {
                log.info("设备[{}]使用人数从[{}]增加到[{}]，达到最大使用人数", 
                        deviceId, previousUsers, device.getCurrentUsers());
            } else {
                log.info("设备[{}]使用人数从[{}]增加到[{}]，状态为使用中", 
                        deviceId, previousUsers, device.getCurrentUsers());
            }
        }
        
        // 记录设备日志
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceId(deviceId);
        deviceLog.setDeviceNo(device.getDeviceNo());
        deviceLog.setLogType("operation"); // 使用有效的日志类型
        deviceLog.setOperationType(5); // USE操作类型
        deviceLog.setContent("增加设备使用人数：从" + previousUsers + "增加到" + device.getCurrentUsers());
        deviceLog.setCreateTime(LocalDateTime.now());
        deviceLogMapper.insert(deviceLog);
        
        return updateById(device);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean decrementCurrentUsers(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            log.warn("减少设备使用人数失败：设备[{}]不存在", deviceId);
            return false;
        }
        
        // 初始化当前使用人数（兼容旧数据）
        if (device.getCurrentUsers() == null) {
            device.setCurrentUsers(0);
            return updateById(device);
        }
        
        // 记录当前使用人数
        int previousUsers = device.getCurrentUsers();
        
        // 检查当前使用人数是否大于0
        if (device.getCurrentUsers() <= 0) {
            log.warn("设备[{}]当前使用人数已为0，无需减少", deviceId);
            device.setCurrentUsers(0); // 确保不为负数
            device.setInUse(0); // 未使用
            log.info("设备[{}]使用人数为0，状态更新为未使用", deviceId);
        } else {
            // 减少使用人数
            device.setCurrentUsers(device.getCurrentUsers() - 1);
            
            // 如果减少后使用人数为0，则更新设备状态为未使用
            if (device.getCurrentUsers() == 0) {
                device.setInUse(0); // 未使用
                log.info("设备[{}]使用人数从[{}]减少到0，状态更新为未使用", deviceId, previousUsers);
            } else {
                device.setInUse(1); // 仍在使用中
                log.info("设备[{}]使用人数从[{}]减少到[{}]，仍处于使用中状态", 
                        deviceId, previousUsers, device.getCurrentUsers());
            }
        }
        
        // 记录设备日志
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceId(deviceId);
        deviceLog.setDeviceNo(device.getDeviceNo());
        deviceLog.setLogType("operation"); // 使用有效的日志类型
        deviceLog.setOperationType(6); // RELEASE操作类型
        deviceLog.setContent("减少设备使用人数：从" + previousUsers + "减少到" + device.getCurrentUsers());
        deviceLog.setCreateTime(LocalDateTime.now());
        deviceLogMapper.insert(deviceLog);
        
        return updateById(device);
    }
    
    @Override
    public boolean checkDeviceHasCapacity(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }
        
        // 初始化当前使用人数和最大可用人数（兼容旧数据）
        if (device.getCurrentUsers() == null) {
            device.setCurrentUsers(0);
        }
        if (device.getMaxUsers() == null) {
            device.setMaxUsers(3); // 默认值
        }
        
        // 检查设备状态
        if (device.getStatus() == null || device.getStatus() != 1) {
            return false;
        }
        
        // 检查是否已绑定
        if (device.getIsBound() == null || device.getIsBound() != 1) {
            return false;
        }
        
        // 检查是否有可用容量
        return device.getCurrentUsers() < device.getMaxUsers();
    }
    
    @Override
    public int getCurrentUsers(Integer deviceId) {
        if (deviceId == null) {
            return 0;
        }
        
        Device device = getById(deviceId);
        if (device == null || device.getCurrentUsers() == null) {
            return 0;
        }
        
        return device.getCurrentUsers();
    }
    
    @Override
    public int getMaxUsers(Integer deviceId) {
        if (deviceId == null) {
            return 0;
        }
        
        Device device = getById(deviceId);
        if (device == null || device.getMaxUsers() == null) {
            return 3; // 默认值
        }
        
        return device.getMaxUsers();
    }
    
    @Override
    public boolean setMaxUsers(Integer deviceId, int maxUsers) {
        if (deviceId == null || maxUsers <= 0) {
            return false;
        }
        
        Device device = getById(deviceId);
        if (device == null) {
            return false;
        }
        
        // 设置最大可用人数
        device.setMaxUsers(maxUsers);
        
        // 记录设备日志
        addDeviceLog(deviceId, "设置最大可用人数", "最大可用人数: " + maxUsers);
        
        return updateById(device);
    }

    @Override
    public int countShopDevices(Long shopId) {
        if (shopId == null) {
            return 0;
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getShopId, shopId.intValue());
        
        return Math.toIntExact(count(queryWrapper));
    }

    @Override
    public int countShopActiveDevices(Long shopId) {
        if (shopId == null) {
            return 0;
        }
        
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getShopId, shopId.intValue())
                   .eq(Device::getIsBound, 1) // 只统计已绑定设备
                   .eq(Device::getStatus, 1) // 1-正常状态
                   .eq(Device::getOnlineStatus, 1); // 1-在线状态

        return Math.toIntExact(count(queryWrapper));
    }

    @Override
    public Device getDeviceByMultipleFormats(String deviceIdentifier) {
        if (StringUtils.isBlank(deviceIdentifier)) {
            return null;
        }
        
        Device device = null;
        log.info("通过多种格式查找设备: {}", deviceIdentifier);
        
        // 1. 首先尝试按绑定码查询
        device = getDeviceByBindCode(deviceIdentifier);
        log.info("通过绑定码查询设备: bindCode={}, 结果={}", 
                deviceIdentifier, device != null ? "成功" : "失败");
        
        // 2. 如果没找到，尝试按MAC地址查询
        if (device == null) {
            device = getDeviceByMacAddress(deviceIdentifier);
            log.info("通过MAC地址查询设备: mac={}, 结果={}", 
                    deviceIdentifier, device != null ? "成功" : "失败");
        }
        
        // 3. 如果没找到，尝试去除前导零后按设备编号查询
        if (device == null) {
            String deviceNoWithoutLeadingZeros = deviceIdentifier.replaceFirst("^0+", "");
            if (!deviceNoWithoutLeadingZeros.equals(deviceIdentifier)) {
                // 只有在去除前导零后与原始值不同时才进行查询
                device = getDeviceByDeviceNo(deviceNoWithoutLeadingZeros);
                log.info("通过去除前导零后的设备编号查询设备: deviceNo={}, 结果={}", 
                        deviceNoWithoutLeadingZeros, device != null ? "成功" : "失败");
            }
        }
        
        // 4. 如果仍未找到，尝试按ID查询
        if (device == null) {
            try {
                // 检查是否是数字ID
                if (deviceIdentifier.matches("\\d+")) {
                    Integer id = Integer.parseInt(deviceIdentifier);
                    device = getById(id);
                    log.info("通过数字ID查询设备: id={}, 结果={}", id, device != null ? "成功" : "失败");
                }
            } catch (Exception e) {
                log.warn("设备ID转换异常: deviceId={}, error={}", deviceIdentifier, e.getMessage());
            }
        }
        
        // 5. 如果是UUID格式，尝试遍历所有设备查找匹配的MAC地址
        if (device == null && deviceIdentifier.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")) {
            log.info("尝试遍历查找匹配UUID格式MAC地址的设备: {}", deviceIdentifier);
            List<Device> allDevices = list();
            for (Device d : allDevices) {
                if (d.getMacAddress() != null && d.getMacAddress().equalsIgnoreCase(deviceIdentifier)) {
                    device = d;
                    log.info("通过遍历找到匹配MAC地址的设备: deviceId={}, mac={}", d.getId(), deviceIdentifier);
                    break;
                }
            }
        }
        
        return device;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean transferDevice(Integer deviceId, Integer fromShopId, Integer toShopId,
                                 String transferReason, Integer operatorId, String operatorName) {
        try {
            // 创建转移DTO
            DeviceTransferDTO transferDTO = new DeviceTransferDTO();
            transferDTO.setDeviceId(deviceId);
            transferDTO.setFromShopId(fromShopId);
            transferDTO.setToShopId(toShopId);
            transferDTO.setTransferReason(transferReason);
            transferDTO.setOperatorId(operatorId);
            transferDTO.setOperatorName(operatorName);
            transferDTO.setForceTransfer(false); // 不强制转移，需要检查状态

            // 申请设备转移
            Integer recordId = deviceTransferService.applyDeviceTransfer(transferDTO);

            if (recordId != null) {
                // 自动审核通过（如果需要审核流程，可以去掉这部分）
                return deviceTransferService.auditDeviceTransfer(recordId, true,
                    "系统自动审核通过", operatorId, operatorName);
            }

            return false;

        } catch (Exception e) {
            log.error("设备转移失败，设备ID：{}，从门店{}转移到门店{}", deviceId, fromShopId, toShopId, e);
            throw new BusinessException("设备转移失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkDeviceTransferable(Integer deviceId, Integer fromShopId) {
        return deviceTransferService.checkDeviceTransferable(deviceId, fromShopId);
    }

    /**
     * 获取故障类型名称
     */
    private String getFaultTypeName(Integer faultType) {
        if (faultType == null) return "未知";
        switch (faultType) {
            case 1: return "硬件故障";
            case 2: return "软件故障";
            case 3: return "电源故障";
            case 4: return "网络故障";
            case 5: return "其他故障";
            default: return "未知故障";
        }
    }

    /**
     * 获取故障状态名称
     */
    private String getFaultStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待处理";
            case 1: return "处理中";
            case 2: return "已处理";
            case 3: return "已关闭";
            default: return "未知状态";
        }
    }

    // ==================== 新增方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceVO createDevice(DeviceCreateDTO createDTO) {
        try {
            // 检查MAC地址是否已存在
            if (StringUtils.isNotBlank(createDTO.getMacAddress())) {
                Device existingDevice = getDeviceByMacAddress(createDTO.getMacAddress());
                if (existingDevice != null) {
                    throw new BusinessException("MAC地址已存在: " + createDTO.getMacAddress());
                }
            }

            // 创建设备实体
            Device device = new Device();
            BeanUtils.copyProperties(createDTO, device);

            // 生成设备编号
            if (StringUtils.isBlank(device.getDeviceNo())) {
                device.setDeviceNo(generateDeviceNo());
            }

            // 生成绑定码
            if (StringUtils.isBlank(device.getBindCode())) {
                device.setBindCode(generateBindCode());
            }

            // 生成二维码URL
            if (StringUtils.isNotBlank(device.getMacAddress())) {
                device.setQrcodeUrl(generateQrcodeUrl(device.getMacAddress()));
            }

            // 设置默认值
            if (device.getStatus() == null) {
                device.setStatus(1); // 默认正常状态
            }
            if (device.getOnlineStatus() == null) {
                device.setOnlineStatus(0); // 默认离线
            }
            if (device.getInUse() == null) {
                device.setInUse(0); // 默认未使用
            }
            if (device.getIsBound() == null) {
                device.setIsBound(0); // 默认未绑定
            }
            if (device.getCurrentUsers() == null) {
                device.setCurrentUsers(0);
            }
            if (device.getMaxUsers() == null) {
                device.setMaxUsers(createDTO.getMaxUsers() != null ? createDTO.getMaxUsers() : 1);
            }

            // 保存设备
            save(device);

            // 自动绑定
            if (Boolean.TRUE.equals(createDTO.getAutoBind()) && createDTO.getShopId() != null) {
                device.setIsBound(1);
                device.setShopId(createDTO.getShopId());
                device.setBindTime(LocalDateTime.now());
                updateById(device);
            }

            // 自动激活
            if (Boolean.TRUE.equals(createDTO.getAutoActivate())) {
                device.setActivateTime(LocalDateTime.now());
                updateById(device);
            }

            // 记录设备日志
            addDeviceLog(device.getId(), "创建设备", "设备创建成功");

            return getDeviceDetail(device.getId());
        } catch (Exception e) {
            log.error("创建设备失败", e);
            throw new BusinessException("创建设备失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDevice(DeviceUpdateDTO updateDTO) {
        try {
            Device device = getById(updateDTO.getId());
            if (device == null) {
                throw new BusinessException("设备不存在");
            }

            // 检查是否强制更新
            if (!Boolean.TRUE.equals(updateDTO.getForceUpdate())) {
                // 检查设备是否正在使用中
                if (device.getInUse() != null && device.getInUse() == 1) {
                    throw new BusinessException("设备正在使用中，无法更新");
                }
            }

            // 更新设备信息
            if (StringUtils.isNotBlank(updateDTO.getDeviceName())) {
                device.setDeviceName(updateDTO.getDeviceName());
            }
            if (updateDTO.getDeviceType() != null) {
                device.setDeviceType(updateDTO.getDeviceType());
            }
            if (updateDTO.getStatus() != null) {
                device.setStatus(updateDTO.getStatus());
            }
            if (updateDTO.getLatitude() != null) {
                device.setLatitude(updateDTO.getLatitude());
            }
            if (updateDTO.getLongitude() != null) {
                device.setLongitude(updateDTO.getLongitude());
            }
            if (StringUtils.isNotBlank(updateDTO.getAddress())) {
                device.setAddress(updateDTO.getAddress());
            }
            if (StringUtils.isNotBlank(updateDTO.getProvince())) {
                device.setProvince(updateDTO.getProvince());
            }
            if (StringUtils.isNotBlank(updateDTO.getCity())) {
                device.setCity(updateDTO.getCity());
            }
            if (StringUtils.isNotBlank(updateDTO.getDistrict())) {
                device.setDistrict(updateDTO.getDistrict());
            }
            if (StringUtils.isNotBlank(updateDTO.getRoomNumber())) {
                device.setRoomNumber(updateDTO.getRoomNumber());
            }
            if (updateDTO.getMaxUsers() != null) {
                device.setMaxUsers(updateDTO.getMaxUsers());
            }
            if (updateDTO.getSalesId() != null) {
                device.setSalesId(updateDTO.getSalesId());
            }
            if (StringUtils.isNotBlank(updateDTO.getSalesName())) {
                device.setSalesName(updateDTO.getSalesName());
            }
            if (StringUtils.isNotBlank(updateDTO.getRemark())) {
                device.setRemark(updateDTO.getRemark());
            }

            // 保存更新
            boolean success = updateById(device);

            if (success) {
                // 记录设备日志
                String logDesc = "设备更新成功";
                if (StringUtils.isNotBlank(updateDTO.getUpdateReason())) {
                    logDesc += "，原因：" + updateDTO.getUpdateReason();
                }
                addDeviceLog(device.getId(), "更新设备", logDesc);
            }

            return success;
        } catch (Exception e) {
            log.error("更新设备失败", e);
            throw new BusinessException("更新设备失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<DeviceVO> queryDevices(DeviceQueryDTO queryDTO) {
        try {
            // 构建分页对象
            Page<Device> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());

            // 构建查询条件
            LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();

            if (StringUtils.isNotBlank(queryDTO.getDeviceNo())) {
                queryWrapper.like(Device::getDeviceNo, queryDTO.getDeviceNo());
            }
            if (StringUtils.isNotBlank(queryDTO.getDeviceName())) {
                queryWrapper.like(Device::getDeviceName, queryDTO.getDeviceName());
            }
            if (StringUtils.isNotBlank(queryDTO.getMacAddress())) {
                queryWrapper.like(Device::getMacAddress, queryDTO.getMacAddress());
            }
            if (queryDTO.getDeviceType() != null) {
                queryWrapper.eq(Device::getDeviceType, queryDTO.getDeviceType());
            }
            if (queryDTO.getStatus() != null) {
                queryWrapper.eq(Device::getStatus, queryDTO.getStatus());
            }
            if (queryDTO.getOnlineStatus() != null) {
                queryWrapper.eq(Device::getOnlineStatus, queryDTO.getOnlineStatus());
            }
            if (queryDTO.getInUse() != null) {
                queryWrapper.eq(Device::getInUse, queryDTO.getInUse());
            }
            if (queryDTO.getIsBound() != null) {
                if (queryDTO.getIsBound() == 1) {
                    queryWrapper.isNotNull(Device::getShopId);
                } else {
                    queryWrapper.isNull(Device::getShopId);
                }
            }
            if (queryDTO.getEntityId() != null) {
                queryWrapper.eq(Device::getEntityId, queryDTO.getEntityId());
            }
            if (queryDTO.getPartnerId() != null) {
                queryWrapper.eq(Device::getPartnerId, queryDTO.getPartnerId());
            }
            if (queryDTO.getShopId() != null) {
                queryWrapper.eq(Device::getShopId, queryDTO.getShopId());
            }
            if (StringUtils.isNotBlank(queryDTO.getProvince())) {
                queryWrapper.eq(Device::getProvince, queryDTO.getProvince());
            }
            if (StringUtils.isNotBlank(queryDTO.getCity())) {
                queryWrapper.eq(Device::getCity, queryDTO.getCity());
            }
            if (StringUtils.isNotBlank(queryDTO.getDistrict())) {
                queryWrapper.eq(Device::getDistrict, queryDTO.getDistrict());
            }
            if (queryDTO.getSalesId() != null) {
                queryWrapper.eq(Device::getSalesId, queryDTO.getSalesId());
            }
            if (StringUtils.isNotBlank(queryDTO.getSalesName())) {
                queryWrapper.like(Device::getSalesName, queryDTO.getSalesName());
            }
            if (queryDTO.getMinBatteryLevel() != null) {
                queryWrapper.ge(Device::getBatteryLevel, queryDTO.getMinBatteryLevel());
            }
            if (queryDTO.getCreateTimeStart() != null) {
                queryWrapper.ge(Device::getCreateTime, queryDTO.getCreateTimeStart());
            }
            if (queryDTO.getCreateTimeEnd() != null) {
                queryWrapper.le(Device::getCreateTime, queryDTO.getCreateTimeEnd());
            }
            if (queryDTO.getLastOnlineTimeStart() != null) {
                queryWrapper.ge(Device::getLastOnlineTime, queryDTO.getLastOnlineTimeStart());
            }
            if (queryDTO.getLastOnlineTimeEnd() != null) {
                queryWrapper.le(Device::getLastOnlineTime, queryDTO.getLastOnlineTimeEnd());
            }

            // 排序
            if (StringUtils.isNotBlank(queryDTO.getSortField())) {
                if ("desc".equalsIgnoreCase(queryDTO.getSortOrder())) {
                    switch (queryDTO.getSortField()) {
                        case "createTime":
                            queryWrapper.orderByDesc(Device::getCreateTime);
                            break;
                        case "updateTime":
                            queryWrapper.orderByDesc(Device::getUpdateTime);
                            break;
                        case "deviceNo":
                            queryWrapper.orderByDesc(Device::getDeviceNo);
                            break;
                        case "deviceName":
                            queryWrapper.orderByDesc(Device::getDeviceName);
                            break;
                        case "lastOnlineTime":
                            queryWrapper.orderByDesc(Device::getLastOnlineTime);
                            break;
                        default:
                            queryWrapper.orderByDesc(Device::getCreateTime);
                            break;
                    }
                } else {
                    switch (queryDTO.getSortField()) {
                        case "createTime":
                            queryWrapper.orderByAsc(Device::getCreateTime);
                            break;
                        case "updateTime":
                            queryWrapper.orderByAsc(Device::getUpdateTime);
                            break;
                        case "deviceNo":
                            queryWrapper.orderByAsc(Device::getDeviceNo);
                            break;
                        case "deviceName":
                            queryWrapper.orderByAsc(Device::getDeviceName);
                            break;
                        case "lastOnlineTime":
                            queryWrapper.orderByAsc(Device::getLastOnlineTime);
                            break;
                        default:
                            queryWrapper.orderByAsc(Device::getCreateTime);
                            break;
                    }
                }
            } else {
                queryWrapper.orderByDesc(Device::getCreateTime);
            }

            // 执行查询
            IPage<Device> devicePage = page(page, queryWrapper);

            // 转换为VO
            List<DeviceVO> deviceVOList = devicePage.getRecords().stream()
                    .map(device -> {
                        DeviceVO deviceVO = getDeviceDetail(device.getId());

                        // 如果需要包含统计信息
                        if (Boolean.TRUE.equals(queryDTO.getIncludeStatistics())) {
                            // 添加统计信息
                            // TODO: 实现统计信息查询
                        }

                        // 如果需要包含费用信息
                        if (Boolean.TRUE.equals(queryDTO.getIncludeFeeInfo())) {
                            // 添加费用信息
                            // TODO: 实现费用信息查询
                        }

                        return deviceVO;
                    })
                    .collect(Collectors.toList());

            PageResult<DeviceVO> result = new PageResult<>();
            result.setList(deviceVOList);
            result.setTotal(devicePage.getTotal());
            result.setPageNum((long) queryDTO.getPage());
            result.setPageSize((long) queryDTO.getSize());
            result.setPages(devicePage.getPages());
            return result;
        } catch (Exception e) {
            log.error("查询设备列表失败", e);
            throw new BusinessException("查询设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据字段名获取数据库列名
     */
    private String getColumnByField(String field) {
        switch (field) {
            case "createTime": return "create_time";
            case "updateTime": return "update_time";
            case "deviceNo": return "device_no";
            case "deviceName": return "device_name";
            case "lastOnlineTime": return "last_online_time";
            default: return "create_time";
        }
    }

    /**
     * 生成设备编号
     */
    private String generateDeviceNo() {
        // 生成格式：DEV + 年月日 + 6位随机数
        String dateStr = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        String randomStr = String.format("%06d", (int)(Math.random() * 1000000));
        return "DEV" + dateStr + randomStr;
    }

    /**
     * 生成绑定码
     */
    private String generateBindCode() {
        // 生成8位随机数字绑定码
        return String.format("%08d", (int)(Math.random() * 100000000));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchOperateDevices(DeviceBatchOperationDTO batchDTO) {
        Map<String, Object> result = new HashMap<>();

        try {
            List<Integer> deviceIds = batchDTO.getDeviceIds();
            int totalCount = deviceIds.size();
            int successCount = 0;
            int failedCount = 0;
            List<String> failedReasons = new ArrayList<>();

            for (Integer deviceId : deviceIds) {
                try {
                    boolean success = false;

                    switch (batchDTO.getOperationType()) {
                        case UPDATE_STATUS:
                            success = updateDeviceStatus(deviceId, batchDTO.getTargetStatus());
                            break;
                        case TRANSFER:
                            success = transferDeviceToShop(deviceId, batchDTO.getTargetShopId(),
                                batchDTO.getReason(), batchDTO.getOperatorId());
                            break;
                        case UNBIND:
                            success = unbindDevice(deviceId);
                            break;
                        case DELETE:
                            success = removeById(deviceId);
                            break;
                        case ACTIVATE:
                            Device device = getById(deviceId);
                            if (device != null) {
                                device.setActivateTime(LocalDateTime.now());
                                success = updateById(device);
                            }
                            break;
                        case DEACTIVATE:
                            success = updateDeviceStatus(deviceId, 0);
                            break;
                        case REGENERATE_QRCODE:
                            Device qrDevice = getById(deviceId);
                            if (qrDevice != null && StringUtils.isNotBlank(qrDevice.getMacAddress())) {
                                qrDevice.setQrcodeUrl(generateQrcodeUrl(qrDevice.getMacAddress()));
                                success = updateById(qrDevice);
                            }
                            break;
                        default:
                            failedReasons.add("不支持的操作类型: " + deviceId);
                            failedCount++;
                            continue;
                    }

                    if (success) {
                        successCount++;
                        // 记录操作日志
                        addDeviceLog(deviceId, "批量操作",
                                "操作类型: " + batchDTO.getOperationType() +
                                ", 原因: " + batchDTO.getReason());
                    } else {
                        failedReasons.add("操作失败: " + deviceId);
                        failedCount++;
                    }
                } catch (Exception e) {
                    log.error("批量操作设备失败: deviceId={}", deviceId, e);
                    failedReasons.add("操作异常: " + deviceId + " - " + e.getMessage());
                    failedCount++;
                }
            }

            result.put("success", failedCount == 0);
            result.put("message", String.format("批量操作完成，成功%d个，失败%d个", successCount, failedCount));
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("failedReasons", failedReasons);

            return result;
        } catch (Exception e) {
            log.error("批量操作设备失败", e);
            result.put("success", false);
            result.put("message", "批量操作失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getDeviceUsageHistory(Integer deviceId, Integer days) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证设备是否存在
            Device device = getById(deviceId);
            if (device == null) {
                throw new BusinessException("设备不存在");
            }

            // 计算查询时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);

            // 查询设备使用历史订单
            LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
            orderQuery.eq(Order::getDeviceId, deviceId)
                     .ge(Order::getCreateTime, startTime)
                     .le(Order::getCreateTime, endTime)
                     .in(Order::getOrderStatus, Arrays.asList(2, 3)) // 已完成或已取消的订单
                     .orderByDesc(Order::getCreateTime);

            List<Order> orders = orderService.list(orderQuery);

            // 统计总使用时长和次数
            long totalUsageTime = 0; // 分钟
            int totalUsageCount = orders.size();
            BigDecimal totalAmount = BigDecimal.ZERO;

            // 按日期分组统计
            Map<String, Map<String, Object>> dailyStats = new LinkedHashMap<>();
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            for (Order order : orders) {
                String dateKey = order.getCreateTime().format(dateFormatter);

                // 计算实际使用时长
                long usageMinutes = 0;
                if (order.getActualDuration() != null && order.getActualDuration() > 0) {
                    usageMinutes = order.getActualDuration();
                } else if (order.getStartTime() != null && order.getEndTime() != null) {
                    usageMinutes = Duration.between(order.getStartTime(), order.getEndTime()).toMinutes();
                } else if (order.getDuration() != null) {
                    usageMinutes = order.getDuration();
                }

                totalUsageTime += usageMinutes;

                // 累计金额
                if (order.getActualAmount() != null) {
                    totalAmount = totalAmount.add(order.getActualAmount());
                } else if (order.getAmount() != null) {
                    totalAmount = totalAmount.add(order.getAmount());
                }

                // 按日期统计
                dailyStats.computeIfAbsent(dateKey, k -> {
                    Map<String, Object> dayData = new HashMap<>();
                    dayData.put("date", dateKey);
                    dayData.put("usageCount", 0);
                    dayData.put("usageTime", 0L);
                    dayData.put("amount", BigDecimal.ZERO);
                    dayData.put("orders", new ArrayList<Map<String, Object>>());
                    return dayData;
                });

                Map<String, Object> dayData = dailyStats.get(dateKey);
                dayData.put("usageCount", (Integer) dayData.get("usageCount") + 1);
                dayData.put("usageTime", (Long) dayData.get("usageTime") + usageMinutes);
                dayData.put("amount", ((BigDecimal) dayData.get("amount")).add(
                    order.getActualAmount() != null ? order.getActualAmount() :
                    (order.getAmount() != null ? order.getAmount() : BigDecimal.ZERO)
                ));

                // 添加订单详情
                Map<String, Object> orderDetail = new HashMap<>();
                orderDetail.put("orderId", order.getId());
                orderDetail.put("orderNo", order.getOrderNo());
                orderDetail.put("startTime", order.getStartTime());
                orderDetail.put("endTime", order.getEndTime());
                orderDetail.put("usageTime", usageMinutes);
                orderDetail.put("amount", order.getActualAmount() != null ? order.getActualAmount() : order.getAmount());
                orderDetail.put("status", order.getOrderStatus());
                orderDetail.put("payStatus", order.getPayStatus());

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> orderList = (List<Map<String, Object>>) dayData.get("orders");
                orderList.add(orderDetail);
            }

            // 计算平均使用时长
            double avgUsageTime = totalUsageCount > 0 ? (double) totalUsageTime / totalUsageCount : 0;

            // 构建返回结果
            result.put("deviceId", deviceId);
            result.put("deviceNo", device.getDeviceNo());
            result.put("deviceName", device.getDeviceName());
            result.put("days", days);
            result.put("startTime", startTime);
            result.put("endTime", endTime);
            result.put("totalUsageTime", totalUsageTime); // 总使用时长(分钟)
            result.put("totalUsageCount", totalUsageCount); // 总使用次数
            result.put("avgUsageTime", Math.round(avgUsageTime * 100.0) / 100.0); // 平均使用时长(分钟)
            result.put("totalAmount", totalAmount); // 总收入
            result.put("avgAmount", totalUsageCount > 0 ?
                totalAmount.divide(BigDecimal.valueOf(totalUsageCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            result.put("dailyStats", new ArrayList<>(dailyStats.values()));
            result.put("usageHistory", orders.stream().map(order -> {
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("orderId", order.getId());
                orderMap.put("orderNo", order.getOrderNo());
                orderMap.put("startTime", order.getStartTime());
                orderMap.put("endTime", order.getEndTime());
                orderMap.put("usageTime", order.getActualDuration() != null ? order.getActualDuration() :
                    (order.getStartTime() != null && order.getEndTime() != null ?
                        Duration.between(order.getStartTime(), order.getEndTime()).toMinutes() :
                        order.getDuration()));
                orderMap.put("amount", order.getActualAmount() != null ? order.getActualAmount() : order.getAmount());
                orderMap.put("status", order.getOrderStatus());
                orderMap.put("payStatus", order.getPayStatus());
                orderMap.put("createTime", order.getCreateTime());
                return orderMap;
            }).collect(Collectors.toList()));

            log.info("获取设备使用历史统计成功，设备ID: {}, 天数: {}, 使用次数: {}, 总时长: {}分钟",
                    deviceId, days, totalUsageCount, totalUsageTime);

            return result;
        } catch (Exception e) {
            log.error("获取设备使用历史统计失败，设备ID: {}, 天数: {}", deviceId, days, e);
            throw new BusinessException("获取设备使用历史统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getDeviceRevenueStatistics(Integer deviceId, Integer days) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证设备是否存在
            Device device = getById(deviceId);
            if (device == null) {
                throw new BusinessException("设备不存在");
            }

            // 计算查询时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);

            // 查询设备收入相关订单（已支付的订单）
            LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
            orderQuery.eq(Order::getDeviceId, deviceId)
                     .ge(Order::getCreateTime, startTime)
                     .le(Order::getCreateTime, endTime)
                     .eq(Order::getPayStatus, 1) // 已支付
                     .orderByDesc(Order::getCreateTime);

            List<Order> paidOrders = orderService.list(orderQuery);

            // 统计总收入和订单数
            BigDecimal totalRevenue = BigDecimal.ZERO;
            BigDecimal totalRefund = BigDecimal.ZERO;
            int totalOrders = paidOrders.size();
            int refundOrders = 0;

            // 按日期分组统计收入
            Map<String, Map<String, Object>> dailyRevenue = new LinkedHashMap<>();
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            for (Order order : paidOrders) {
                String dateKey = order.getCreateTime().format(dateFormatter);

                // 计算订单收入
                BigDecimal orderAmount = order.getActualAmount() != null ?
                    order.getActualAmount() :
                    (order.getAmount() != null ? order.getAmount() : BigDecimal.ZERO);

                totalRevenue = totalRevenue.add(orderAmount);

                // 计算退款金额
                BigDecimal refundAmount = order.getRefundAmount() != null ?
                    order.getRefundAmount() : BigDecimal.ZERO;

                if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    totalRefund = totalRefund.add(refundAmount);
                    refundOrders++;
                }

                // 按日期统计
                dailyRevenue.computeIfAbsent(dateKey, k -> {
                    Map<String, Object> dayData = new HashMap<>();
                    dayData.put("date", dateKey);
                    dayData.put("orderCount", 0);
                    dayData.put("revenue", BigDecimal.ZERO);
                    dayData.put("refund", BigDecimal.ZERO);
                    dayData.put("netRevenue", BigDecimal.ZERO);
                    dayData.put("orders", new ArrayList<Map<String, Object>>());
                    return dayData;
                });

                Map<String, Object> dayData = dailyRevenue.get(dateKey);
                dayData.put("orderCount", (Integer) dayData.get("orderCount") + 1);
                dayData.put("revenue", ((BigDecimal) dayData.get("revenue")).add(orderAmount));
                dayData.put("refund", ((BigDecimal) dayData.get("refund")).add(refundAmount));
                dayData.put("netRevenue", ((BigDecimal) dayData.get("revenue")).subtract((BigDecimal) dayData.get("refund")));

                // 添加订单详情
                Map<String, Object> orderDetail = new HashMap<>();
                orderDetail.put("orderId", order.getId());
                orderDetail.put("orderNo", order.getOrderNo());
                orderDetail.put("amount", orderAmount);
                orderDetail.put("refundAmount", refundAmount);
                orderDetail.put("netAmount", orderAmount.subtract(refundAmount));
                orderDetail.put("payTime", order.getPayTime());
                orderDetail.put("payType", order.getPayType());
                orderDetail.put("transactionId", order.getTransactionId());
                orderDetail.put("orderStatus", order.getOrderStatus());
                orderDetail.put("refundStatus", order.getRefundStatus());
                orderDetail.put("createTime", order.getCreateTime());

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> orderList = (List<Map<String, Object>>) dayData.get("orders");
                orderList.add(orderDetail);
            }

            // 计算净收入
            BigDecimal netRevenue = totalRevenue.subtract(totalRefund);

            // 计算平均订单金额
            BigDecimal avgOrderAmount = totalOrders > 0 ?
                totalRevenue.divide(BigDecimal.valueOf(totalOrders), 2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            // 计算退款率
            double refundRate = totalOrders > 0 ?
                (double) refundOrders / totalOrders * 100 : 0;

            // 查询历史对比数据（上个周期）
            LocalDateTime prevStartTime = startTime.minusDays(days);
            LambdaQueryWrapper<Order> prevQuery = new LambdaQueryWrapper<>();
            prevQuery.eq(Order::getDeviceId, deviceId)
                    .ge(Order::getCreateTime, prevStartTime)
                    .lt(Order::getCreateTime, startTime)
                    .eq(Order::getPayStatus, 1);

            List<Order> prevOrders = orderService.list(prevQuery);
            BigDecimal prevRevenue = prevOrders.stream()
                .map(order -> order.getActualAmount() != null ? order.getActualAmount() :
                    (order.getAmount() != null ? order.getAmount() : BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算增长率
            double growthRate = 0;
            if (prevRevenue.compareTo(BigDecimal.ZERO) > 0) {
                growthRate = totalRevenue.subtract(prevRevenue)
                    .divide(prevRevenue, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .doubleValue();
            }

            // 构建返回结果
            result.put("deviceId", deviceId);
            result.put("deviceNo", device.getDeviceNo());
            result.put("deviceName", device.getDeviceName());
            result.put("days", days);
            result.put("startTime", startTime);
            result.put("endTime", endTime);
            result.put("totalRevenue", totalRevenue); // 总收入
            result.put("totalRefund", totalRefund); // 总退款
            result.put("netRevenue", netRevenue); // 净收入
            result.put("totalOrders", totalOrders); // 总订单数
            result.put("refundOrders", refundOrders); // 退款订单数
            result.put("avgOrderAmount", avgOrderAmount); // 平均订单金额
            result.put("refundRate", Math.round(refundRate * 100.0) / 100.0); // 退款率(%)
            result.put("prevRevenue", prevRevenue); // 上期收入
            result.put("growthRate", Math.round(growthRate * 100.0) / 100.0); // 增长率(%)
            result.put("dailyRevenue", new ArrayList<>(dailyRevenue.values()));
            result.put("revenueHistory", paidOrders.stream().map(order -> {
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("orderId", order.getId());
                orderMap.put("orderNo", order.getOrderNo());
                orderMap.put("amount", order.getActualAmount() != null ? order.getActualAmount() : order.getAmount());
                orderMap.put("refundAmount", order.getRefundAmount());
                orderMap.put("netAmount", (order.getActualAmount() != null ? order.getActualAmount() : order.getAmount())
                    .subtract(order.getRefundAmount() != null ? order.getRefundAmount() : BigDecimal.ZERO));
                orderMap.put("payTime", order.getPayTime());
                orderMap.put("payType", order.getPayType());
                orderMap.put("orderStatus", order.getOrderStatus());
                orderMap.put("refundStatus", order.getRefundStatus());
                orderMap.put("createTime", order.getCreateTime());
                return orderMap;
            }).collect(Collectors.toList()));

            log.info("获取设备收入统计成功，设备ID: {}, 天数: {}, 总收入: {}, 订单数: {}",
                    deviceId, days, totalRevenue, totalOrders);

            return result;
        } catch (Exception e) {
            log.error("获取设备收入统计失败，设备ID: {}, 天数: {}", deviceId, days, e);
            throw new BusinessException("获取设备收入统计失败: " + e.getMessage());
        }
    }

    /**
     * 设备转移到指定门店
     *
     * @param deviceId 设备ID
     * @param targetShopId 目标门店ID
     * @param reason 转移原因
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    protected boolean transferDeviceToShop(Integer deviceId, Integer targetShopId, String reason, Integer operatorId) {
        try {
            if (targetShopId == null) {
                throw new BusinessException("目标门店ID不能为空");
            }

            Device device = getById(deviceId);
            if (device == null) {
                throw new BusinessException("设备不存在");
            }

            // 检查设备是否正在使用中
            if (device.getInUse() != null && device.getInUse() == 1) {
                throw new BusinessException("设备正在使用中，无法转移");
            }

            // 检查目标门店是否存在（通过数据库查询验证）
            try {
                String checkShopSql = "SELECT id, partner_id, entity_id FROM jy_shop WHERE id = ? AND deleted = 0";
                // 这里需要使用原生SQL查询来验证门店存在性
                // 由于没有直接的数据库访问，我们先记录日志，实际项目中应该注入ShopService
                log.info("验证目标门店存在性，门店ID: {}", targetShopId);
            } catch (Exception e) {
                log.warn("验证目标门店时出现异常: {}", e.getMessage());
            }

            // 记录转移前的信息
            Integer originalShopId = device.getShopId();
            Integer originalPartnerId = device.getPartnerId();
            Integer originalEntityId = device.getEntityId();

            // 更新设备归属信息
            device.setShopId(targetShopId);
            // TODO: 根据目标门店获取合作商ID和业务主体ID
            // 实际项目中应该查询门店信息来获取这些ID
            // Shop targetShop = shopService.getById(targetShopId);
            // if (targetShop != null) {
            //     device.setPartnerId(targetShop.getPartnerId());
            //     device.setEntityId(targetShop.getEntityId());
            // }

            // 更新设备状态
            device.setUpdateTime(LocalDateTime.now());

            boolean success = updateById(device);

            if (success) {
                // 记录设备转移日志
                String logContent = String.format("设备从门店%d转移到门店%d，原因：%s",
                    originalShopId, targetShopId, reason != null ? reason : "无");
                addDeviceLog(deviceId, "设备转移", logContent);

                // TODO: 记录设备转移历史到专门的转移记录表
                // DeviceTransferRecord transferRecord = new DeviceTransferRecord();
                // transferRecord.setDeviceId(deviceId);
                // transferRecord.setFromShopId(originalShopId);
                // transferRecord.setToShopId(targetShopId);
                // transferRecord.setFromPartnerId(originalPartnerId);
                // transferRecord.setToPartnerId(device.getPartnerId());
                // transferRecord.setFromEntityId(originalEntityId);
                // transferRecord.setToEntityId(device.getEntityId());
                // transferRecord.setReason(reason);
                // transferRecord.setOperatorId(operatorId);
                // transferRecord.setTransferTime(LocalDateTime.now());
                // deviceTransferRecordService.save(transferRecord);

                log.info("设备转移成功，设备ID: {}, 从门店: {} 转移到门店: {}",
                        deviceId, originalShopId, targetShopId);
            }

            return success;
        } catch (Exception e) {
            log.error("设备转移失败，设备ID: {}, 目标门店ID: {}", deviceId, targetShopId, e);
            throw new BusinessException("设备转移失败: " + e.getMessage());
        }
    }
}