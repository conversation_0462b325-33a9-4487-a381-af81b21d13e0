package com.jycb.jycbz.modules.admin.convert;

import com.jycb.jycbz.common.enums.AdminTypeEnum;
import com.jycb.jycbz.modules.admin.entity.Admin;
import com.jycb.jycbz.modules.admin.vo.AdminVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 管理员对象转换器
 */
@Mapper(componentModel = "spring")
public interface AdminConvert {

    AdminConvert INSTANCE = Mappers.getMapper(AdminConvert.class);

    /**
     * 实体转VO
     */
    @Mapping(source = "adminType", target = "adminTypeName", qualifiedByName = "getAdminTypeName")
    @Mapping(source = "status", target = "statusName", qualifiedByName = "getStatusName")
    @Mapping(target = "entityName", ignore = true)
    @Mapping(target = "partnerName", ignore = true)
    @Mapping(target = "shopName", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "permissions", ignore = true)
    @Mapping(target = "remark", ignore = true)
    AdminVO convert(Admin admin);

    /**
     * VO转实体
     */
    @Mapping(target = "password", ignore = true)
    Admin convert(AdminVO adminVO);

    /**
     * 实体列表转VO列表
     */
    List<AdminVO> convertList(List<Admin> adminList);

    /**
     * 获取管理员类型名称
     */
    @Named("getAdminTypeName")
    default String getAdminTypeName(String adminType) {
        if (adminType == null) {
            return null;
        }
        AdminTypeEnum typeEnum = AdminTypeEnum.getByValue(adminType);
        return typeEnum == null ? adminType : typeEnum.getDesc();
    }

    /**
     * 获取状态名称
     */
    @Named("getStatusName")
    default String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        return status == 1 ? "启用" : "禁用";
    }
} 