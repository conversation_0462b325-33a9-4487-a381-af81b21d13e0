package com.jycb.jycbz.modules.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jycb.jycbz.modules.admin.entity.AdminOperationLog;

/**
 * 管理员操作日志服务接口
 */
public interface AdminOperationLogService extends IService<AdminOperationLog> {

    /**
     * 记录操作日志
     *
     * @param log 操作日志
     */
    void recordLog(AdminOperationLog log);

    /**
     * 异步记录操作日志
     *
     * @param log 操作日志
     */
    void recordLogAsync(AdminOperationLog log);

    /**
     * 分页查询操作日志
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param adminId 管理员ID（可选）
     * @param module 操作模块（可选）
     * @param operationType 操作类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<AdminOperationLog> getLogPage(Integer pageNum, Integer pageSize, 
                                       Long adminId, String module, String operationType,
                                       String startTime, String endTime);

    /**
     * 清理过期日志
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredLogs(int days);
}
