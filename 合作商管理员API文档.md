# 今夜城堡 - 合作商管理端API对接文档

## 📋 文档概述

本文档专门为**合作商管理端**前端开发提供API对接指南，详细描述了合作商管理员在管理端系统中可以使用的所有API接口。

### 🎯 目标用户
- **合作商管理员**: 管理自己的合作商信息和下属门店
- **前端开发人员**: 对接合作商管理端功能
- **测试人员**: API接口测试和验证

### 🔐 认证方式
- 使用Sa-Token进行身份认证
- 角色要求: `PARTNER_ADMIN` (合作商管理员)
- 所有接口需要登录后访问
- 数据权限: 只能访问自己合作商的数据

### 📊 功能模块
- **个人信息管理**: 查看和修改合作商基本信息
- **门店管理**: 管理下属门店的完整生命周期
- **设备管理**: 管理合作商设备和设备绑定
- **订单管理**: 查看和管理门店订单
- **财务管理**: 财务统计、提现申请、银行卡管理
- **用户管理**: 查看门店用户信息
- **数据统计**: 营收分析和业务报表

---

## 1. 🔐 认证登录API

### 1.1 管理员登录

**接口地址**: `POST /auth/admin/login`

**接口描述**: 合作商管理员登录系统

**请求体**:
```json
{
  "username": "partner_admin",
  "password": "123456",
  "adminType": "partner"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "adminInfo": {
      "id": 5,
      "username": "partner_admin",
      "realName": "张三",
      "adminType": "partner",
      "partnerId": 1,
      "entityId": 1,
      "roles": ["partner_admin"],
      "permissions": ["partner:read", "partner:update", "shop:list", "shop:read"]
    }
  }
}
```

### 1.2 获取用户信息

**接口地址**: `GET /auth/admin/info`

**接口描述**: 获取当前登录用户的详细信息

### 1.3 修改密码

**接口地址**: `PUT /auth/admin/password`

**接口描述**: 修改登录密码

**请求体**:
```json
{
  "oldPassword": "123456",
  "newPassword": "654321"
}
```

### 1.4 更新个人资料

**接口地址**: `PUT /auth/admin/profile`

**接口描述**: 更新个人基本信息

### 1.5 退出登录

**接口地址**: `POST /auth/admin/logout`

**接口描述**: 退出系统登录

---

## 2. 👤 合作商信息管理API

### 2.1 获取合作商详情

**接口地址**: `GET /api/partner/{id}`

**接口描述**: 合作商管理员获取自己的详细信息

**权限要求**:
- 角色: `PARTNER_ADMIN`
- 权限: `partner:read`
- 数据权限: 只能查看自己的信息

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 合作商ID（必须是当前登录用户的合作商ID） |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "entityId": 1,
    "partnerName": "北京合作商",
    "partnerCode": "BJ001",
    "contactName": "张三",
    "contactPhone": "***********",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区某某街道",
    "revenueRatio": 30.00,
    "bankName": "中国银行",
    "bankAccount": "6217****0000",
    "accountName": "张三",
    "deviceCount": 10,
    "shopCount": 3,
    "totalRevenue": 25000.00,
    "status": 1,
    "adminId": 5,
    "adminName": "张三",
    "createTime": "2025-01-01 10:00:00"
  }
}
```

### 1.2 创建合作商

**接口地址**: `POST /admin/partner`

**接口描述**: 系统管理员创建新的合作商

**权限要求**: 
- 角色: `SUPER_ADMIN`

**请求体**:
```json
{
  "entityId": 1,
  "partnerName": "上海合作商",
  "partnerCode": "SH001",
  "contactName": "李四",
  "contactPhone": "***********",
  "province": "上海市",
  "city": "上海市",
  "district": "浦东新区",
  "address": "浦东新区某某路",
  "revenueRatio": 25.00,
  "bankName": "中国银行",
  "bankAccount": "****************",
  "accountName": "李四",
  "adminInfo": {
    "username": "sh_admin",
    "password": "123456",
    "realName": "李四",
    "mobile": "***********",
    "email": "<EMAIL>"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 2,
    "entityId": 1,
    "partnerName": "上海合作商",
    "partnerCode": "SH001",
    "adminId": 10,
    "status": 1,
    "createTime": "2025-01-01 10:00:00"
  }
}
```

### 1.3 更新合作商信息

**接口地址**: `PUT /admin/partner/{id}`

**接口描述**: 系统管理员更新合作商信息

**权限要求**: 
- 角色: `SUPER_ADMIN`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 合作商ID |

**请求体**: 同创建合作商接口

### 1.4 更新合作商状态

**接口地址**: `PUT /admin/partner/{id}/status`

**接口描述**: 启用或禁用合作商

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 合作商ID |
| status | Integer | 是 | 状态：1-启用 0-禁用 |

### 1.5 更新合作商分成比例

**接口地址**: `PUT /admin/partner/{id}/revenue-ratio`

**接口描述**: 更新合作商的分成比例

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 合作商ID |
| revenueRatio | BigDecimal | 是 | 分成比例(%) |

### 1.6 获取合作商统计数据

**接口地址**: `GET /admin/partner/{id}/statistics`

**接口描述**: 获取指定合作商的统计数据

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopCount": 5,
    "deviceCount": 20,
    "totalRevenue": 50000.00,
    "monthlyRevenue": 8000.00,
    "orderCount": 1200,
    "activeDeviceCount": 18
  }
}
```

### 1.7 获取业务主体下合作商列表

**接口地址**: `GET /admin/partner/entity/{entityId}`

**接口描述**: 获取指定业务主体下的合作商列表

### 1.8 统计业务主体下合作商数量

**接口地址**: `GET /admin/partner/count/entity/{entityId}`

**接口描述**: 统计指定业务主体下的合作商数量

---

## 2. 业务主体管理员 - 合作商管理API

### 2.1 获取合作商分页列表

**接口地址**: `GET /entity/partner/page`

**接口描述**: 业务主体管理员分页获取下属合作商列表

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:list`

**请求参数**: 同系统管理员接口，但只能查看当前业务主体下的合作商

### 2.2 获取合作商详情

**接口地址**: `GET /entity/partner/{id}`

**接口描述**: 获取合作商详细信息

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:read`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "entityId": 1,
    "partnerName": "北京合作商",
    "partnerCode": "BJ001",
    "contactName": "张三",
    "contactPhone": "***********",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区某某街道",
    "revenueRatio": 30.00,
    "bankName": "中国银行",
    "bankAccount": "6217****0000",
    "accountName": "张三",
    "deviceCount": 10,
    "shopCount": 3,
    "totalRevenue": 25000.00,
    "status": 1,
    "adminId": 5,
    "adminName": "张三",
    "createTime": "2025-01-01 10:00:00"
  }
}
```

### 2.3 添加合作商

**接口地址**: `POST /entity/partner`

**接口描述**: 业务主体管理员添加新的合作商

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:add`

### 2.4 更新合作商

**接口地址**: `PUT /entity/partner/{id}`

**接口描述**: 更新合作商信息

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:update`

### 2.5 删除合作商

**接口地址**: `DELETE /entity/partner/{id}`

**接口描述**: 删除合作商（软删除）

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:delete`

### 2.6 更新合作商状态

**接口地址**: `PUT /entity/partner/{id}/status`

**接口描述**: 启用或禁用合作商

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:update`

### 2.7 更新合作商分成比例

**接口地址**: `PUT /entity/partner/{id}/revenue-ratio`

**接口描述**: 更新合作商分成比例

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:update`

### 2.8 获取合作商统计数据

**接口地址**: `GET /entity/partner/statistics`

**接口描述**: 获取当前业务主体下所有合作商的统计数据

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:read`

### 2.9 获取所有启用的合作商

**接口地址**: `GET /entity/partner/list`

**接口描述**: 获取所有启用的合作商（下拉选择用）

**权限要求**: 
- 角色: `ENTITY_ADMIN`
- 权限: `entity:partner:list`

---

## 3. 合作商管理员 - 自身管理API

### 3.1 获取合作商详情

**接口地址**: `GET /api/partner/{id}`

**接口描述**: 合作商管理员获取自己的详细信息

**权限要求**: 
- 登录用户
- 权限: `partner:read`
- 数据权限: 只能查看自己的信息

### 3.2 修改合作商信息

**接口地址**: `PUT /api/partner/{id}`

**接口描述**: 合作商管理员修改自己的信息

**权限要求**: 
- 登录用户
- 权限: `partner:update`
- 数据权限: 只能修改自己的信息

**注意**: 某些敏感字段（如分成比例）可能无法修改

---

## 4. 合作商财务管理API

### 4.1 获取财务统计信息

**接口地址**: `GET /partner/finance/statistics`

**接口描述**: 获取合作商财务统计信息

**权限要求**: 合作商管理员登录

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalBalance": 15000.00,
    "availableBalance": 12000.00,
    "frozenBalance": 3000.00,
    "totalIncome": 50000.00,
    "totalWithdraw": 35000.00,
    "monthlyIncome": 8000.00,
    "todayIncome": 500.00,
    "pendingWithdraw": 2000.00
  }
}
```

### 4.2 获取财务账户信息

**接口地址**: `GET /partner/finance/account`

**接口描述**: 获取合作商财务账户详细信息

### 4.3 获取财务流水列表

**接口地址**: `GET /partner/finance/logs`

**接口描述**: 分页获取合作商财务流水记录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10 |
| type | String | 否 | 流水类型 |
| startTime | String | 否 | 开始时间 yyyy-MM-dd HH:mm |
| endTime | String | 否 | 结束时间 yyyy-MM-dd HH:mm |

### 4.4 获取财务流水详情

**接口地址**: `GET /partner/finance/log/{id}`

**接口描述**: 获取指定财务流水的详细信息

### 4.5 申请提现

**接口地址**: `POST /partner/finance/apply-withdraw`

**接口描述**: 合作商申请提现

**请求体**:
```json
{
  "amount": 1000.00,
  "bankCardId": 1,
  "remark": "提现申请"
}
```

### 4.6 获取提现记录

**接口地址**: `GET /partner/finance/withdraw-records`

**接口描述**: 分页获取提现记录

### 4.7 获取提现详情

**接口地址**: `GET /partner/finance/withdraw/{id}`

**接口描述**: 获取指定提现记录的详细信息

### 4.8 银行卡管理

#### 4.8.1 获取银行卡列表

**接口地址**: `GET /partner/finance/bank-cards`

**接口描述**: 获取合作商的银行卡列表

#### 4.8.2 添加银行卡

**接口地址**: `POST /partner/finance/bank-card`

**接口描述**: 添加新的银行卡

**请求体**:
```json
{
  "bankName": "中国银行",
  "bankAccount": "****************",
  "accountName": "张三",
  "isDefault": true
}
```

#### 4.8.3 更新银行卡

**接口地址**: `PUT /partner/finance/bank-card/{id}`

**接口描述**: 更新银行卡信息

#### 4.8.4 删除银行卡

**接口地址**: `DELETE /partner/finance/bank-card/{id}`

**接口描述**: 删除银行卡

#### 4.8.5 设置默认银行卡

**接口地址**: `PUT /partner/finance/bank-card/{id}/default`

**接口描述**: 设置默认银行卡

### 4.9 财务安全验证

#### 4.9.1 验证账户余额

**接口地址**: `POST /partner/finance/validate-balance`

**接口描述**: 验证合作商账户余额的准确性

#### 4.9.2 获取可提现余额

**接口地址**: `GET /partner/finance/withdrawable-balance`

**接口描述**: 计算合作商当前可提现的余额

#### 4.9.3 检查账户安全状态

**接口地址**: `GET /partner/finance/security-status`

**接口描述**: 检查合作商账户的安全状态

### 4.10 系统配置

#### 4.10.1 获取最小提现金额

**接口地址**: `GET /partner/finance/min-withdraw-amount`

**接口描述**: 获取系统设置的最小提现金额

---

## 5. 数据模型定义

### 5.1 合作商实体 (Partner)

```json
{
  "id": "合作商ID",
  "entityId": "所属业务主体ID",
  "partnerName": "合作商名称",
  "partnerCode": "合作商编码",
  "contactName": "联系人姓名",
  "contactPhone": "联系电话",
  "province": "省份",
  "city": "城市",
  "district": "区县",
  "address": "详细地址",
  "revenueRatio": "分成比例(%)",
  "bankName": "开户银行",
  "bankAccount": "银行账号",
  "accountName": "收款人姓名",
  "deviceFee": "设备费",
  "systemFee": "系统使用费",
  "deviceCount": "设备数量",
  "cooperationTime": "合作时间",
  "salesperson": "业务员",
  "operator": "运营人员",
  "status": "状态：1-启用 0-禁用",
  "remark": "备注",
  "adminId": "关联管理员ID",
  "salesId": "归属员工ID",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### 5.2 合作商查询DTO (PartnerQueryDTO)

```json
{
  "keyword": "合作商名称/编码",
  "status": "状态：1-启用 0-禁用",
  "entityId": "所属业务主体ID",
  "province": "省份",
  "city": "城市",
  "district": "区县",
  "salesId": "归属员工ID"
}
```

### 5.3 合作商管理员DTO (PartnerAdminDTO)

```json
{
  "username": "用户名",
  "password": "密码",
  "realName": "真实姓名",
  "mobile": "手机号",
  "email": "邮箱"
}
```

---

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录已过期 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 7. 注意事项

### 7.1 权限控制
- 系统管理员拥有所有合作商的管理权限
- 业务主体管理员只能管理下属合作商
- 合作商管理员只能查看和修改自己的信息

### 7.2 数据安全
- 敏感信息（如银行账号）在返回时会进行脱敏处理
- 所有财务操作都有审计日志记录
- 提现操作需要经过审核流程

### 7.3 业务规则
- 合作商编码在同一业务主体下必须唯一
- 分成比例的修改可能需要特殊权限
- 删除合作商时会检查是否有关联的门店和设备

### 7.4 审计日志
- 所有关键操作都会记录审计日志
- 包括操作人、操作时间、操作内容等信息
- 支持按模块、操作类型等条件查询审计记录

---

**文档版本**: v1.0  
**最后更新**: 2025-07-25  
**维护人员**: 系统开发团队
