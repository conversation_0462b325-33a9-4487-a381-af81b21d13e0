package com.jycb.jycbz.common.interceptor;

import com.jycb.jycbz.common.context.UserContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户上下文拦截器
 * 在每个Web请求开始时捕获用户上下文信息
 */
@Slf4j
@Component
public class UserContextInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 捕获当前用户上下文并设置到ThreadLocal
            UserContextHolder.UserContext context = UserContextHolder.captureCurrentContext();
            UserContextHolder.setContext(context);
            
            log.debug("Web请求开始，用户上下文已设置: userId={}, uri={}", 
                context.getUserId(), request.getRequestURI());
                
        } catch (Exception e) {
            log.debug("设置用户上下文失败: {}", e.getMessage());
        }
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 请求完成后清除用户上下文
            UserContextHolder.clear();
            log.debug("Web请求完成，用户上下文已清除: uri={}", request.getRequestURI());
        } catch (Exception e) {
            log.debug("清除用户上下文失败: {}", e.getMessage());
        }
    }
}
