# 今夜城堡 - 合作商角色API接口整理

## 📋 概述

本文档整理了项目中所有提供给**合作商角色(PARTNER_ADMIN)**的API接口，按功能模块分类展示。

### 🔐 权限说明
- **角色要求**: `PARTNER_ADMIN` (合作商管理员)
- **认证方式**: Sa-Token身份认证
- **数据权限**: 只能访问自己合作商的数据

---

## 1. 🔐 认证登录API

### 1.1 管理员登录
- **接口**: `POST /auth/admin/login`
- **描述**: 合作商管理员登录系统
- **权限**: 无需登录
- **请求体**:
```json
{
  "username": "partner_admin",
  "password": "123456"
}
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "userId": 5,
    "username": "partner_admin",
    "role": "PARTNER_ADMIN",
    "adminType": "partner",
    "entityId": 1,
    "partnerId": 1,
    "shopId": null,
    "realName": "张三",
    "avatar": null
  },
  "success": true
}
```

### 1.2 获取用户信息
- **接口**: `GET /auth/admin/info`
- **描述**: 获取当前登录用户的详细信息
- **权限**: 需要登录
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 5,
    "username": "partner_admin",
    "realName": "张三",
    "mobile": "***********",
    "email": "<EMAIL>",
    "adminType": "partner",
    "entityId": 1,
    "partnerId": 1,
    "shopId": null,
    "status": 1,
    "loginIp": "*************",
    "loginTime": "2025-07-27 10:30:00",
    "roles": ["PARTNER_ADMIN"],
    "permissions": ["partner:read", "partner:update", "shop:list", "shop:read"],
    "createTime": "2025-01-01 10:00:00"
  },
  "success": true
}
```

### 1.3 获取当前用户信息
- **接口**: `GET /auth/profile`
- **描述**: 获取当前登录用户信息
- **权限**: 需要登录

### 1.4 获取权限列表
- **接口**: `GET /auth/permissions`
- **描述**: 获取当前登录用户的权限列表
- **权限**: 需要登录
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    "partner:read",
    "partner:update",
    "shop:list",
    "shop:read",
    "shop:create",
    "shop:update",
    "device:list",
    "device:read",
    "finance:read"
  ],
  "success": true
}
```

### 1.5 获取菜单列表
- **接口**: `GET /auth/menus`
- **描述**: 获取当前登录用户的菜单列表
- **权限**: 需要登录

### 1.6 验证密码
- **接口**: `POST /auth/verify-password`
- **描述**: 验证当前登录用户的密码
- **权限**: 需要登录
- **请求参数**:
  - `password` (String): 要验证的密码
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true,
  "success": true
}
```

### 1.7 修改密码
- **接口**: `PUT /auth/change-password`
- **描述**: 修改当前登录用户的密码
- **权限**: 需要登录
- **请求体**:
```json
{
  "oldPassword": "123456",
  "newPassword": "654321"
}
```

### 1.8 退出登录
- **接口**: `POST /auth/logout`
- **描述**: 退出系统登录
- **权限**: 需要登录

---

## 2. 👤 合作商自身信息管理API

### 2.1 获取合作商详情
- **接口**: `GET /partner/info`
- **描述**: 获取当前登录合作商的详细信息
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "entityId": 1,
    "entityName": "北京业务主体",
    "partnerName": "北京合作商",
    "partnerCode": "BJ001",
    "contactName": "张三",
    "contactPhone": "***********",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区某某街道123号",
    "idCard": "110101199001011234",
    "revenueRatio": 30.00,
    "bankName": "中国银行",
    "bankAccount": "6217****0000",
    "accountName": "张三",
    "deviceFee": 100.00,
    "systemFee": 50.00,
    "cooperationTime": "2025-01-01 00:00:00",
    "salesperson": "李四",
    "operator": "王五",
    "status": 1,
    "statusDesc": "启用",
    "remark": "优质合作商",
    "adminId": 5,
    "adminName": "张三",
    "salesId": 10,
    "salesName": "李四",
    "shopCount": 3,
    "deviceTotal": 15,
    "totalRevenue": 25000.00,
    "createTime": "2025-01-01 10:00:00",
    "updateTime": "2025-07-27 10:30:00"
  }
}
```

### 2.2 更新合作商基本信息
- **接口**: `PUT /partner/info`
- **描述**: 更新当前登录合作商的基本信息
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "partnerName": "北京合作商",
  "contactName": "张三",
  "contactPhone": "***********",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "address": "朝阳区某某街道123号",
  "idCard": "110101199001011234",
  "remark": "优质合作商"
}
```

### 2.3 更新银行信息
- **接口**: `PUT /partner/bank`
- **描述**: 更新合作商银行信息
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "bankName": "中国银行",
  "bankAccount": "****************",
  "accountName": "张三"
}
```

### 2.4 更新联系信息
- **接口**: `PUT /partner/contact`
- **描述**: 更新合作商联系信息
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "contactName": "张三",
  "contactPhone": "***********",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "address": "朝阳区某某街道123号"
}
```

### 2.5 获取合作商统计数据
- **接口**: `GET /partner/statistics`
- **描述**: 获取当前登录合作商的统计数据
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopCount": 5,
    "deviceCount": 20,
    "totalRevenue": 50000.00,
    "monthlyRevenue": 8000.00,
    "orderCount": 1200,
    "activeDeviceCount": 18,
    "todayRevenue": 500.00,
    "yesterdayRevenue": 450.00
  }
}
```

---

## 3. 🏪 管理门店模块

### 3.1 获取门店分页列表
- **接口**: `GET /partner/shop/page`
- **描述**: 分页获取当前合作商下的门店列表
- **权限**: `PARTNER_ADMIN`
- **请求参数**:
  - `pageNum` (Integer): 页码，默认1
  - `pageSize` (Integer): 每页数量，默认10
  - `shopName` (String): 门店名称（可选）
  - `status` (Integer): 状态：1-启用 0-禁用（可选）
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "朝阳门店",
        "code": "BJ001-001",
        "contactPerson": "李四",
        "contactPhone": "13900139000",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "朝阳区某某路123号",
        "revenueRatio": 70.00,
        "status": 1,
        "deviceCount": 5,
        "todayRevenue": 200.00,
        "createTime": "2025-01-01 10:00:00"
      }
    ],
    "total": 3,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3.2 获取门店列表
- **接口**: `GET /partner/shop/list`
- **描述**: 获取合作商旗下门店列表
- **权限**: `PARTNER_ADMIN`
- **请求参数**:
  - `keyword` (String): 关键词（门店名称或编码）
  - `status` (Integer): 状态
  - `pageNum` (Integer): 页码
  - `pageSize` (Integer): 每页数量

### 3.3 获取门店详情
- **接口**: `GET /partner/shop/{id}`
- **描述**: 获取指定门店的详细信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 门店ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "entityId": 1,
    "partnerId": 1,
    "name": "朝阳门店",
    "code": "BJ001-001",
    "contactPerson": "李四",
    "contactPhone": "13900139000",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区某某路123号",
    "longitude": 116.397128,
    "latitude": 39.916527,
    "revenueRatio": 70.00,
    "status": 1,
    "remark": "优质门店",
    "deviceCount": 5,
    "totalRevenue": 15000.00,
    "createTime": "2025-01-01 10:00:00",
    "updateTime": "2025-07-27 10:30:00"
  }
}
```

### 3.4 创建门店
- **接口**: `POST /partner/shop`
- **描述**: 创建新的门店
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "name": "新门店",
  "code": "BJ001-002",
  "contactPerson": "王五",
  "contactPhone": "13800138001",
  "province": "北京市",
  "city": "北京市",
  "district": "海淀区",
  "address": "海淀区某某路456号",
  "longitude": 116.297128,
  "latitude": 39.916527,
  "revenueRatio": 70.00,
  "remark": "新开门店",
  "adminInfo": {
    "username": "shop_admin",
    "password": "123456",
    "realName": "王五",
    "mobile": "13800138001",
    "email": "<EMAIL>"
  }
}
```

### 3.5 更新门店信息
- **接口**: `PUT /partner/shop/{id}`
- **描述**: 更新门店基本信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 门店ID
- **请求体**: 同创建门店接口

### 3.6 删除门店
- **接口**: `DELETE /partner/shop/{id}`
- **描述**: 删除门店（软删除）
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 门店ID

### 3.7 更新门店状态
- **接口**: `PUT /partner/shop/{id}/status`
- **描述**: 启用或禁用门店
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 门店ID
- **请求参数**:
  - `status` (Integer): 状态：1-启用 0-禁用

### 3.8 获取门店统计数据
- **接口**: `GET /partner/shop/statistics`
- **描述**: 获取当前合作商下门店统计数据
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalShops": 5,
    "activeShops": 4,
    "totalDevices": 20,
    "activeDevices": 18,
    "todayRevenue": 1200.00,
    "monthRevenue": 25000.00,
    "todayOrders": 45,
    "monthOrders": 800
  }
}
```

### 3.9 获取门店营业概况
- **接口**: `GET /partner/shop/overview`
- **描述**: 获取合作商旗下所有门店的营业概况
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalShops": 5,
    "activeShops": 4,
    "inactiveShops": 1,
    "totalDevices": 20,
    "activeDevices": 18,
    "offlineDevices": 2,
    "todayOverview": {
      "totalRevenue": 1200.00,
      "totalOrders": 45,
      "completedOrders": 42,
      "cancelledOrders": 3,
      "averageOrderAmount": 26.67,
      "deviceUtilization": 85.5
    },
    "monthOverview": {
      "totalRevenue": 25000.00,
      "totalOrders": 800,
      "completedOrders": 750,
      "cancelledOrders": 50,
      "averageOrderAmount": 31.25,
      "deviceUtilization": 78.2
    },
    "shopDetails": [
      {
        "shopId": 1,
        "shopName": "朝阳门店",
        "shopCode": "BJ001-001",
        "status": 1,
        "deviceCount": 5,
        "activeDeviceCount": 5,
        "todayRevenue": 300.00,
        "todayOrders": 12,
        "monthRevenue": 6000.00,
        "monthOrders": 180,
        "utilizationRate": 90.0,
        "lastOrderTime": "2025-07-27 14:30:00"
      },
      {
        "shopId": 2,
        "shopName": "海淀门店",
        "shopCode": "BJ001-002",
        "status": 1,
        "deviceCount": 4,
        "activeDeviceCount": 3,
        "todayRevenue": 250.00,
        "todayOrders": 10,
        "monthRevenue": 5200.00,
        "monthOrders": 150,
        "utilizationRate": 75.0,
        "lastOrderTime": "2025-07-27 13:45:00"
      },
      {
        "shopId": 3,
        "shopName": "西城门店",
        "shopCode": "BJ001-003",
        "status": 1,
        "deviceCount": 3,
        "activeDeviceCount": 3,
        "todayRevenue": 200.00,
        "todayOrders": 8,
        "monthRevenue": 4500.00,
        "monthOrders": 120,
        "utilizationRate": 80.0,
        "lastOrderTime": "2025-07-27 15:20:00"
      },
      {
        "shopId": 4,
        "shopName": "东城门店",
        "shopCode": "BJ001-004",
        "status": 1,
        "deviceCount": 4,
        "activeDeviceCount": 4,
        "todayRevenue": 280.00,
        "todayOrders": 11,
        "monthRevenue": 5800.00,
        "monthOrders": 160,
        "utilizationRate": 85.0,
        "lastOrderTime": "2025-07-27 14:10:00"
      },
      {
        "shopId": 5,
        "shopName": "丰台门店",
        "shopCode": "BJ001-005",
        "status": 0,
        "deviceCount": 4,
        "activeDeviceCount": 3,
        "todayRevenue": 170.00,
        "todayOrders": 4,
        "monthRevenue": 3500.00,
        "monthOrders": 90,
        "utilizationRate": 45.0,
        "lastOrderTime": "2025-07-27 11:30:00"
      }
    ],
    "revenueChart": {
      "last7Days": [
        {
          "date": "2025-07-21",
          "revenue": 980.00,
          "orders": 38
        },
        {
          "date": "2025-07-22",
          "revenue": 1050.00,
          "orders": 42
        },
        {
          "date": "2025-07-23",
          "revenue": 1150.00,
          "orders": 45
        },
        {
          "date": "2025-07-24",
          "revenue": 1080.00,
          "orders": 40
        },
        {
          "date": "2025-07-25",
          "revenue": 1200.00,
          "orders": 48
        },
        {
          "date": "2025-07-26",
          "revenue": 1100.00,
          "orders": 44
        },
        {
          "date": "2025-07-27",
          "revenue": 1200.00,
          "orders": 45
        }
      ]
    },
    "topPerformingShops": [
      {
        "shopId": 1,
        "shopName": "朝阳门店",
        "monthRevenue": 6000.00,
        "monthOrders": 180,
        "rank": 1
      },
      {
        "shopId": 4,
        "shopName": "东城门店",
        "monthRevenue": 5800.00,
        "monthOrders": 160,
        "rank": 2
      },
      {
        "shopId": 2,
        "shopName": "海淀门店",
        "monthRevenue": 5200.00,
        "monthOrders": 150,
        "rank": 3
      }
    ],
    "alerts": [
      {
        "type": "device_offline",
        "shopId": 5,
        "shopName": "丰台门店",
        "message": "设备DEV005离线超过2小时",
        "time": "2025-07-27 13:00:00"
      },
      {
        "type": "low_utilization",
        "shopId": 5,
        "shopName": "丰台门店",
        "message": "门店设备利用率低于50%",
        "time": "2025-07-27 12:00:00"
      }
    ],
    "updateTime": "2025-07-27 15:30:00"
  }
}
```

---

## 4. 📱 管理设备模块

### 4.1 获取设备列表
- **接口**: `GET /partner/device/list`
- **描述**: 获取合作商下的设备列表
- **权限**: `PARTNER_ADMIN`
- **请求参数**:
  - `partnerId` (Integer): 合作商ID（可选）
  - `shopId` (Integer): 门店ID（可选）
  - `status` (Integer): 设备状态：1-正常 2-维护中 3-故障（可选）
  - `pageNum` (Integer): 页码，默认1
  - `pageSize` (Integer): 每页数量，默认10
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "deviceNo": "DEV001",
        "deviceName": "设备001",
        "deviceType": 1,
        "deviceTypeName": "标准设备",
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "qrcodeUrl": "https://example.com/qr/DEV001",
        "isBound": 1,
        "status": 1,
        "statusName": "正常",
        "onlineStatus": 1,
        "onlineStatusName": "在线",
        "shopId": 1,
        "shopName": "朝阳门店",
        "roomNumber": "101",
        "usePrice": 10.00,
        "billingType": 1,
        "createTime": "2025-01-01 10:00:00"
      }
    ],
    "total": 20,
    "size": 10,
    "current": 1,
    "pages": 2
  }
}
```

### 4.2 获取设备详情
- **接口**: `GET /partner/device/{id}`
- **描述**: 获取指定设备的详细信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Integer): 设备ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "deviceType": 1,
    "deviceTypeName": "标准设备",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "qrcodeUrl": "https://example.com/qr/DEV001",
    "bindCode": "BIND001",
    "isBound": 1,
    "status": 1,
    "statusName": "正常",
    "onlineStatus": 1,
    "onlineStatusName": "在线",
    "inUse": 0,
    "entityId": 1,
    "entityName": "北京业务主体",
    "partnerId": 1,
    "partnerName": "北京合作商",
    "shopId": 1,
    "shopName": "朝阳门店",
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "朝阳区某某路123号",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "roomNumber": "101",
    "maxUsers": 1,
    "usePrice": 10.00,
    "billingType": 1,
    "remark": "设备运行正常",
    "activateTime": "2025-01-01 10:00:00",
    "createTime": "2025-01-01 10:00:00",
    "updateTime": "2025-07-27 10:30:00"
  }
}
```

### 4.3 添加设备
- **接口**: `POST /partner/device`
- **描述**: 添加新设备
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "deviceName": "新设备",
  "deviceType": 1,
  "macAddress": "AA:BB:CC:DD:EE:01",
  "shopId": 1,
  "latitude": 39.916527,
  "longitude": 116.397128,
  "address": "朝阳区某某路123号",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "roomNumber": "102",
  "maxUsers": 1,
  "remark": "新增设备"
}
```

### 4.4 更新设备
- **接口**: `PUT /partner/device/{id}`
- **描述**: 更新设备信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Integer): 设备ID
- **请求体**: 同添加设备接口

### 4.5 删除设备
- **接口**: `DELETE /partner/device/{id}`
- **描述**: 删除设备
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Integer): 设备ID

### 4.6 绑定设备
- **接口**: `POST /partner/device/bind`
- **描述**: 将设备绑定到门店
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "macAddress": "AA:BB:CC:DD:EE:FF",
  "shopId": 1,
  "roomNumber": "101"
}
```

### 4.7 解绑设备
- **接口**: `POST /partner/device/{id}/unbind`
- **描述**: 解绑设备
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Integer): 设备ID

### 4.8 获取设备统计
- **接口**: `GET /partner/device/statistics`
- **描述**: 获取合作商设备统计信息
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalDevices": 20,
    "onlineDevices": 18,
    "offlineDevices": 2,
    "normalDevices": 17,
    "maintenanceDevices": 1,
    "faultDevices": 2,
    "boundDevices": 18,
    "unboundDevices": 2,
    "inUseDevices": 5
  }
}
```

### 4.9 获取门店设备列表
- **接口**: `GET /partner/device/shops/{shopId}`
- **描述**: 获取合作商下门店的设备列表
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `shopId` (Integer): 门店ID

---

## 5. 💰 财务管理模块

### 5.1 获取财务统计信息
- **接口**: `GET /partner/finance/statistics`
- **描述**: 获取合作商财务统计信息
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalBalance": 15000.00,
    "availableBalance": 12000.00,
    "frozenBalance": 3000.00,
    "totalIncome": 50000.00,
    "totalWithdraw": 35000.00,
    "monthlyIncome": 8000.00,
    "todayIncome": 500.00,
    "pendingWithdraw": 2000.00,
    "withdrawingAmount": 1000.00,
    "todayWithdrawCount": 1,
    "todayWithdrawAmount": 1000.00
  }
}
```

### 5.2 获取财务统计信息（详细版）
- **接口**: `GET /partner/finance/statistics/detailed`
- **描述**: 获取合作商财务统计信息（详细版）
- **权限**: `PARTNER_ADMIN`

### 5.3 获取财务账户信息
- **接口**: `GET /partner/finance/account`
- **描述**: 获取合作商财务账户详细信息
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "accountType": "PARTNER",
    "accountId": 1,
    "partnerId": 1,
    "accountName": "北京合作商财务账户",
    "balance": 15000.00,
    "frozenBalance": 3000.00,
    "totalIncome": 50000.00,
    "totalExpense": 35000.00,
    "status": 1,
    "createTime": "2025-01-01 10:00:00",
    "updateTime": "2025-07-27 10:30:00"
  }
}
```

### 5.4 获取财务流水列表
- **接口**: `GET /partner/finance/logs`
- **描述**: 分页获取合作商财务流水记录
- **权限**: `PARTNER_ADMIN`
- **请求参数**:
  - `pageNum` (Integer): 页码，默认1
  - `pageSize` (Integer): 每页数量，默认10
  - `type` (Integer): 流水类型（可选）
  - `startTime` (String): 开始时间，格式：yyyy-MM-dd HH:mm（可选）
  - `endTime` (String): 结束时间，格式：yyyy-MM-dd HH:mm（可选）
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "logNo": "FL20250727001",
        "accountType": "PARTNER",
        "accountId": 1,
        "type": 1,
        "typeName": "收入",
        "amount": 100.00,
        "beforeBalance": 14900.00,
        "afterBalance": 15000.00,
        "orderId": "ORD20250727001",
        "description": "订单收入",
        "operatorId": 5,
        "operatorName": "张三",
        "createTime": "2025-07-27 10:30:00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 5.5 获取财务流水详情
- **接口**: `GET /partner/finance/log/{id}`
- **描述**: 获取指定财务流水的详细信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 流水ID

### 5.6 申请提现
- **接口**: `POST /partner/finance/apply-withdraw`
- **描述**: 合作商申请提现
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "amount": 1000.00,
  "bankCardId": 1,
  "remark": "提现申请"
}
```

### 5.7 获取提现记录
- **接口**: `GET /partner/finance/withdraw-records`
- **描述**: 分页获取提现记录
- **权限**: `PARTNER_ADMIN`
- **请求参数**:
  - `pageNum` (Integer): 页码，默认1
  - `pageSize` (Integer): 每页数量，默认10
  - `status` (Integer): 提现状态（可选）
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "withdrawNo": "WD20250727001",
        "amount": 1000.00,
        "fee": 5.00,
        "actualAmount": 995.00,
        "bankName": "中国银行",
        "bankCardNo": "6217****0000",
        "cardholderName": "张三",
        "status": 1,
        "statusName": "待审核",
        "applyTime": "2025-07-27 10:30:00",
        "remark": "提现申请"
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 5.8 获取提现详情
- **接口**: `GET /partner/finance/withdraw/{id}`
- **描述**: 获取指定提现记录的详细信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 提现记录ID

### 5.9 银行卡管理

#### 5.9.1 获取银行卡列表
- **接口**: `GET /partner/finance/bank-cards`
- **描述**: 获取合作商的银行卡列表
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "bankName": "中国银行",
      "cardNo": "6217****0000",
      "holderName": "张三",
      "bankBranch": "中国银行北京朝阳支行",
      "reservedPhone": "***********",
      "isDefault": true,
      "status": 1,
      "createTime": "2025-01-01 10:00:00"
    }
  ]
}
```

#### 5.9.2 添加银行卡
- **接口**: `POST /partner/finance/bank-card`
- **描述**: 添加新的银行卡
- **权限**: `PARTNER_ADMIN`
- **请求体**:
```json
{
  "bankName": "中国银行",
  "cardNumber": "****************",
  "cardHolderName": "张三",
  "idCard": "110101199001011234",
  "mobile": "***********",
  "isDefault": true,
  "remark": "主要银行卡"
}
```

#### 5.9.3 更新银行卡
- **接口**: `PUT /partner/finance/bank-card/{id}`
- **描述**: 更新银行卡信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 银行卡ID
- **请求体**: 同添加银行卡接口

#### 5.9.4 删除银行卡
- **接口**: `DELETE /partner/finance/bank-card/{id}`
- **描述**: 删除银行卡
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 银行卡ID

#### 5.9.5 设置默认银行卡
- **接口**: `PUT /partner/finance/bank-card/{id}/default`
- **描述**: 设置默认银行卡
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 银行卡ID

### 5.10 财务安全验证

#### 5.10.1 验证账户余额
- **接口**: `POST /partner/finance/validate-balance`
- **描述**: 验证合作商账户余额的准确性
- **权限**: `PARTNER_ADMIN`

#### 5.10.2 获取可提现余额
- **接口**: `GET /partner/finance/withdrawable-balance`
- **描述**: 计算合作商当前可提现的余额
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "withdrawableBalance": 12000.00,
    "totalBalance": 15000.00,
    "frozenBalance": 3000.00,
    "minWithdrawAmount": 100.00
  }
}
```

#### 5.10.3 检查账户安全状态
- **接口**: `GET /partner/finance/security-status`
- **描述**: 检查合作商账户的安全状态
- **权限**: `PARTNER_ADMIN`

### 5.11 系统配置

#### 5.11.1 获取最小提现金额
- **接口**: `GET /partner/finance/min-withdraw-amount`
- **描述**: 获取系统设置的最小提现金额
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "minWithdrawAmount": 100.00
  }
}
```

---

## 6. 📊 统计分析模块

### 6.1 合作商统计数据
- **接口**: `GET /partner/statistics`
- **描述**: 获取当前登录合作商的统计数据
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopCount": 5,
    "deviceCount": 20,
    "totalRevenue": 50000.00,
    "monthlyRevenue": 8000.00,
    "orderCount": 1200,
    "activeDeviceCount": 18,
    "todayRevenue": 500.00,
    "yesterdayRevenue": 450.00,
    "todayOrderCount": 25,
    "monthOrderCount": 800
  }
}
```

### 6.2 门店统计数据
- **接口**: `GET /partner/shop/statistics`
- **描述**: 获取当前合作商下门店统计数据
- **权限**: `PARTNER_ADMIN`

### 6.3 设备统计数据
- **接口**: `GET /partner/device/statistics`
- **描述**: 获取合作商设备统计信息
- **权限**: `PARTNER_ADMIN`

### 6.4 财务统计数据
- **接口**: `GET /partner/finance/statistics`
- **描述**: 获取合作商财务统计信息
- **权限**: `PARTNER_ADMIN`

---

## 7. 📋 订单管理模块

### 7.1 获取订单列表
- **接口**: `GET /partner/order/list`
- **描述**: 分页获取合作商旗下订单列表
- **权限**: `PARTNER_ADMIN`
- **请求参数**:
  - `orderNo` (String): 订单号（可选）
  - `deviceNo` (String): 设备编号（可选）
  - `orderStatus` (Integer): 订单状态：0-待支付 1-已支付 2-使用中 3-已完成 4-已取消（可选）
  - `payStatus` (Integer): 支付状态（可选）
  - `shopId` (Long): 门店ID（可选）
  - `startTime` (LocalDateTime): 开始时间（可选）
  - `endTime` (LocalDateTime): 结束时间（可选）
  - `pageNum` (Integer): 页码，默认1
  - `pageSize` (Integer): 每页数量，默认10
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "orderNo": "ORD20250727001",
        "userId": 100,
        "userName": "用户001",
        "userPhone": "13900139000",
        "deviceId": 1,
        "deviceName": "设备001",
        "deviceNo": "DEV001",
        "shopId": 1,
        "shopName": "朝阳门店",
        "partnerId": 1,
        "partnerName": "北京合作商",
        "amount": 10.00,
        "payAmount": 10.00,
        "payTime": "2025-07-27 10:30:00",
        "payType": 1,
        "payTypeName": "微信支付",
        "status": 3,
        "statusName": "已完成",
        "createTime": "2025-07-27 10:00:00",
        "remark": "正常订单"
      }
    ],
    "total": 1200,
    "size": 10,
    "current": 1,
    "pages": 120
  }
}
```

### 7.2 获取订单详情
- **接口**: `GET /partner/order/{id}`
- **描述**: 获取指定订单的详细信息
- **权限**: `PARTNER_ADMIN`
- **路径参数**:
  - `id` (Long): 订单ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderNo": "ORD20250727001",
    "userId": 100,
    "userName": "用户001",
    "userPhone": "13900139000",
    "deviceId": 1,
    "deviceName": "设备001",
    "deviceNo": "DEV001",
    "shopId": 1,
    "shopName": "朝阳门店",
    "partnerId": 1,
    "partnerName": "北京合作商",
    "entityId": 1,
    "entityName": "北京业务主体",
    "roomId": 1,
    "roomName": "101房间",
    "amount": 10.00,
    "payAmount": 10.00,
    "payTime": "2025-07-27 10:30:00",
    "payType": 1,
    "payTypeName": "微信支付",
    "payTradeNo": "WX20250727001",
    "status": 3,
    "statusName": "已完成",
    "createTime": "2025-07-27 10:00:00",
    "updateTime": "2025-07-27 11:00:00",
    "remark": "正常订单"
  }
}
```

### 7.3 获取订单统计
- **接口**: `GET /partner/order/statistics`
- **描述**: 获取合作商订单统计数据
- **权限**: `PARTNER_ADMIN`
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOrders": 1200,
    "todayOrders": 25,
    "monthOrders": 800,
    "completedOrders": 1100,
    "cancelledOrders": 50,
    "totalAmount": 12000.00,
    "todayAmount": 250.00,
    "monthAmount": 8000.00
  }
}
```

### 7.4 导出订单数据
- **接口**: `GET /partner/order/export`
- **描述**: 导出合作商订单数据
- **权限**: `PARTNER_ADMIN`
- **请求参数**: 同获取订单列表接口
- **响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "https://example.com/download/orders_20250727.xlsx"
}
```

---

## 📝 注意事项

### 🔒 权限控制
- 所有接口都需要 `PARTNER_ADMIN` 角色权限
- 数据权限：合作商只能访问自己的数据
- 某些敏感操作可能需要额外的权限验证
- 使用Sa-Token进行身份认证和权限控制

### 🛡️ 数据安全
- 敏感信息（如银行账号）在返回时会进行脱敏处理
- 所有财务操作都有审计日志记录
- 提现操作需要经过审核流程
- 密码等敏感信息需要加密传输

### 📋 业务规则
- 合作商只能管理自己下属的门店和设备
- 财务操作有最小金额限制（通常为100元）
- 删除操作通常为软删除，保留数据完整性
- 设备绑定需要验证门店归属关系

### 🔄 响应格式
所有接口统一使用以下响应格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "success": true
}
```

### 📊 状态码说明
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未登录或登录已过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 📅 时间格式
- 所有时间字段统一使用格式：`yyyy-MM-dd HH:mm:ss`
- 时区：东八区（北京时间）

### 💰 金额格式
- 所有金额字段使用BigDecimal类型
- 保留2位小数
- 单位：人民币元

---

## 📈 接口统计

### 模块统计
- **认证登录模块**: 8个接口
- **合作商信息管理**: 5个接口
- **门店管理模块**: 9个接口
- **设备管理模块**: 9个接口
- **财务管理模块**: 16个接口
- **统计分析模块**: 4个接口
- **订单管理模块**: 4个接口

**总计**: **55个API接口**

### 功能覆盖
✅ 完整覆盖合作商管理端所有核心功能
✅ 支持完整的CRUD操作
✅ 包含详细的统计分析功能
✅ 提供完善的财务管理功能
✅ 支持数据导出功能

---

**文档版本**: v2.0
**最后更新**: 2025-07-27
**整理人员**: Augment Agent
**审核状态**: ✅ 已与项目代码核对一致
