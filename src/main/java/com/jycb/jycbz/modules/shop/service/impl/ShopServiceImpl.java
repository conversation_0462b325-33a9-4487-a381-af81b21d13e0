package com.jycb.jycbz.modules.shop.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.entity.entity.Entity;
import com.jycb.jycbz.modules.entity.service.EntityService;
import com.jycb.jycbz.modules.finance.entity.CommissionDetail;
import com.jycb.jycbz.modules.finance.entity.SettlementConfig;
import com.jycb.jycbz.modules.finance.service.CommissionConfigService;
import com.jycb.jycbz.modules.finance.service.CommissionDetailService;
import com.jycb.jycbz.modules.finance.service.FinanceAccountService;
import com.jycb.jycbz.modules.finance.service.SettlementConfigService;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.partner.entity.Partner;
import com.jycb.jycbz.modules.partner.service.PartnerService;
import com.jycb.jycbz.modules.shop.convert.ShopConvert;
import com.jycb.jycbz.modules.shop.dto.ShopCreateDTO;
import com.jycb.jycbz.modules.shop.dto.ShopDTO;
import com.jycb.jycbz.modules.shop.dto.ShopQueryDTO;
import com.jycb.jycbz.modules.shop.dto.ShopUpdateDTO;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.mapper.ShopMapper;
import com.jycb.jycbz.modules.shop.service.ShopService;
import com.jycb.jycbz.modules.shop.vo.ShopDetailVO;
import com.jycb.jycbz.modules.shop.vo.ShopStatisticsVO;
import com.jycb.jycbz.modules.shop.vo.ShopVO;
import com.jycb.jycbz.modules.finance.constant.FinanceConstants;
import com.jycb.jycbz.modules.finance.service.FinanceCoordinatorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门店服务实现�?
 *
 * 重构说明�?
 * 1. 优化门店基础CRUD操作
 * 2. 实现门店统计和仪表盘功能
 * 3. 完善门店数据权限控制
 * 4. 优化查询性能和分页功�?
 * 5. 实现门店业务数据统计分析
 *
 * 核心功能�?
 * - 门店基础信息管理
 * - 门店统计数据分析
 * - 门店仪表盘数�?
 * - 门店设备和订单统�?
 * - 门店收益分析
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements ShopService {

    private final ObjectProvider<EntityService> entityServiceProvider;
    private final ObjectProvider<PartnerService> partnerServiceProvider;
    private final ObjectProvider<DeviceService> deviceServiceProvider;
    private final ObjectProvider<FinanceAccountService> financeAccountServiceProvider;
    private final ObjectProvider<OrderService> orderServiceProvider;
    private final SettlementConfigService settlementConfigService;
    private final CommissionConfigService commissionConfigService;
    private final CommissionDetailService commissionDetailService;
    private final ObjectProvider<FinanceCoordinatorService> financeCoordinatorServiceProvider;

    private EntityService getEntityService() {
        return entityServiceProvider.getObject();
    }

    private PartnerService getPartnerService() {
        return partnerServiceProvider.getObject();
    }

    private DeviceService getDeviceService() {
        return deviceServiceProvider.getObject();
    }

    private OrderService getOrderService() {
        return orderServiceProvider.getObject();
    }

    private FinanceCoordinatorService getFinanceCoordinatorService() {
        return financeCoordinatorServiceProvider.getObject();
    }

    private FinanceAccountService getFinanceAccountService() {
        return financeAccountServiceProvider.getObject();
    }

    @Override
    public Page<ShopVO> getShopPage(ShopQueryDTO queryDTO, int pageNum, int pageSize) {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        
        // 设置查询条件
        if (StringUtils.hasText(queryDTO.getName())) {
            queryWrapper.like(Shop::getShopName, queryDTO.getName());
        }
        if (queryDTO.getEntityId() != null) {
            queryWrapper.eq(Shop::getEntityId, queryDTO.getEntityId());
        }
        if (queryDTO.getPartnerId() != null) {
            queryWrapper.eq(Shop::getPartnerId, queryDTO.getPartnerId());
        }
        if (StringUtils.hasText(queryDTO.getContactPerson())) {
            queryWrapper.like(Shop::getContactName, queryDTO.getContactPerson());
        }
        if (StringUtils.hasText(queryDTO.getContactPhone())) {
            queryWrapper.like(Shop::getContactPhone, queryDTO.getContactPhone());
        }
        if (StringUtils.hasText(queryDTO.getProvince())) {
            queryWrapper.like(Shop::getProvince, queryDTO.getProvince());
        }
        if (StringUtils.hasText(queryDTO.getCity())) {
            queryWrapper.like(Shop::getCity, queryDTO.getCity());
        }
        if (StringUtils.hasText(queryDTO.getDistrict())) {
            queryWrapper.like(Shop::getDistrict, queryDTO.getDistrict());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(Shop::getStatus, queryDTO.getStatus());
        }
        
        // 设置时间范围
        if (StringUtils.hasText(queryDTO.getStartTime()) && StringUtils.hasText(queryDTO.getEndTime())) {
            queryWrapper.between(Shop::getCreateTime, queryDTO.getStartTime(), queryDTO.getEndTime());
        } else if (StringUtils.hasText(queryDTO.getStartTime())) {
            queryWrapper.ge(Shop::getCreateTime, queryDTO.getStartTime());
        } else if (StringUtils.hasText(queryDTO.getEndTime())) {
            queryWrapper.le(Shop::getCreateTime, queryDTO.getEndTime());
        }
        
        // 设置排序
        if (StringUtils.hasText(queryDTO.getOrderBy())) {
            if ("name".equals(queryDTO.getOrderBy())) {
                queryWrapper.orderBy(true, "asc".equalsIgnoreCase(queryDTO.getOrderDirection()), Shop::getShopName);
            } else if ("createTime".equals(queryDTO.getOrderBy())) {
                queryWrapper.orderBy(true, "asc".equalsIgnoreCase(queryDTO.getOrderDirection()), Shop::getCreateTime);
            } else {
                queryWrapper.orderByDesc(Shop::getCreateTime);
            }
        } else {
            queryWrapper.orderByDesc(Shop::getCreateTime);
        }
        
        Page<Shop> shopPage = page(new Page<>(pageNum, pageSize), queryWrapper);

        // 转换为ShopVO
        Page<ShopVO> result = new Page<>(shopPage.getCurrent(), shopPage.getSize(), shopPage.getTotal());
        List<ShopVO> shopVOList = shopPage.getRecords().stream()
            .map(this::convertToShopVO)
            .collect(Collectors.toList());
        result.setRecords(shopVOList);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Shop createShop(ShopCreateDTO createDTO) {
        // 转换为ShopDTO进行处理
        ShopDTO shopDTO = new ShopDTO();
        shopDTO.setEntityId(createDTO.getEntityId());
        shopDTO.setPartnerId(createDTO.getPartnerId());
        shopDTO.setName(createDTO.getShopName());
        shopDTO.setContactPerson(createDTO.getContactName());
        shopDTO.setContactPhone(createDTO.getContactPhone());
        shopDTO.setAddress(createDTO.getAddress());
        shopDTO.setProvince(createDTO.getProvince());
        shopDTO.setCity(createDTO.getCity());
        shopDTO.setDistrict(createDTO.getDistrict());
        shopDTO.setStatus(createDTO.getStatus());
        // 参数校验
        if (shopDTO == null) {
            throw new BusinessException("门店信息不能为空");
        }
        if (shopDTO.getEntityId() == null) {
            throw new BusinessException("所属业务主体不能为空");
        }
        if (shopDTO.getPartnerId() == null) {
            throw new BusinessException("所属合作商不能为空");
        }
        if (!StringUtils.hasText(shopDTO.getName())) {
            throw new BusinessException("门店名称不能为空");
        }
        
        // 检查业务主体是否存�?
        Entity entity = getEntityService().getById(shopDTO.getEntityId());
        if (entity == null) {
            throw new BusinessException("所属业务主体不存在");
        }
        
        // 检查合作商是否存在
        Partner partner = getPartnerService().getById(shopDTO.getPartnerId());
        if (partner == null) {
            throw new BusinessException("所属合作商不存在");
        }
        
        // 检查合作商是否属于指定的业务主�?
        if (!partner.getEntityId().equals(entity.getId())) {
            throw new BusinessException("所选合作商不属于指定的业务主体");
        }
        
        // 检查门店名称是否已存在
        if (checkNameExists(shopDTO.getName())) {
            throw new BusinessException("门店名称已存在");
        }
        
        // 默认状态为启用
        if (shopDTO.getStatus() == null) {
            shopDTO.setStatus(1);
        }
        
        // DTO转实�?
        Shop shop = ShopConvert.dtoToEntity(shopDTO);
        
        // 保存门店
        save(shop);

        // 创建财务账户和配�?
        try {
            // 创建财务账户
            getFinanceCoordinatorService().createAccount(
                FinanceConstants.AccountType.SHOP,
                shop.getId(),
                shop.getEntityId(),
                shop.getPartnerId(),
                shop.getId()
            );

            // 初始化分成配�?
            getFinanceCoordinatorService().initCommissionConfig(
                FinanceConstants.ConfigType.SHOP,
                shop.getId(),
                shop.getPartnerId(),
                shop.getEntityId(),
                shop.getPartnerId(),
                shop.getId()
            );

            // 初始化结算配�?
            getFinanceCoordinatorService().initSettlementConfig(
                FinanceConstants.ConfigType.SHOP,
                shop.getId(),
                shop.getPartnerId()
            );

        } catch (Exception e) {
            log.error("创建门店财务配置失败", e);
            // 不抛出异常，允许门店创建成功，财务配置可以后续补充
        }

        return shop;
    }

    /**
     * 创建门店
     *
     * @param shopDTO 门店信息
     * @return 创建后的门店
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Shop createShop(ShopDTO shopDTO) {
        // 参数校验
        if (shopDTO == null) {
            throw new BusinessException("门店信息不能为空");
        }

        if (!StringUtils.hasText(shopDTO.getName())) {
            throw new BusinessException("门店名称不能为空");
        }

        if (shopDTO.getEntityId() == null) {
            throw new BusinessException("所属业务主体ID不能为空");
        }

        if (shopDTO.getPartnerId() == null) {
            throw new BusinessException("所属合作商ID不能为空");
        }

        // 检查业务主体是否存�?
        Entity entity = getEntityService().getById(shopDTO.getEntityId());
        if (entity == null) {
            throw new BusinessException("所属业务主体不存在");
        }

        // 检查合作商是否存在
        Partner partner = getPartnerService().getById(shopDTO.getPartnerId());
        if (partner == null) {
            throw new BusinessException("所属合作商不存在");
        }

        // 检查合作商是否属于指定的业务主�?
        if (!shopDTO.getEntityId().equals(partner.getEntityId())) {
            throw new BusinessException("所选合作商不属于指定的业务主体");
        }

        // 检查门店名称是否已存在
        if (existsByName(shopDTO.getName(), shopDTO.getEntityId(), null)) {
            throw new BusinessException("门店名称已存在");
        }

        // 默认状态为启用
        if (shopDTO.getStatus() == null) {
            shopDTO.setStatus(1);
        }

        // DTO转实�?
        Shop shop = ShopConvert.dtoToEntity(shopDTO);

        // 设置门店编码（如果未提供�?
        if (!StringUtils.hasText(shop.getShopCode())) {
            shop.setShopCode(generateShopCode());
        }

        // 保存门店
        save(shop);

        // 创建财务账户
        try {
            getFinanceAccountService().createShopAccount(shop.getId());
        } catch (Exception e) {
            log.warn("创建门店财务账户失败，门店ID: {}", shop.getId(), e);
        }

        log.info("门店创建成功，门店ID: {}, 门店名称: {}", shop.getId(), shop.getShopName());
        return shop;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Shop updateShop(Long id, ShopUpdateDTO updateDTO) {
        // 转换为ShopDTO进行处理
        ShopDTO shopDTO = new ShopDTO();
        // ShopUpdateDTO没有entityId和partnerId字段，这些字段不应该在更新时修改
        shopDTO.setName(updateDTO.getShopName());
        shopDTO.setContactPerson(updateDTO.getContactName());
        shopDTO.setContactPhone(updateDTO.getContactPhone());
        shopDTO.setAddress(updateDTO.getAddress());
        shopDTO.setProvince(updateDTO.getProvince());
        shopDTO.setCity(updateDTO.getCity());
        shopDTO.setDistrict(updateDTO.getDistrict());
        // ShopUpdateDTO没有status字段，状态更新应该通过专门的方�?
        // 参数校验
        if (id == null || shopDTO == null) {
            throw new BusinessException("参数错误");
        }
        
        // 检查门店是否存在
        Shop shop = getById(id);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }
        
        // 检查业务主体是否存�?
        if (shopDTO.getEntityId() != null) {
            Entity entity = getEntityService().getById(shopDTO.getEntityId());
            if (entity == null) {
                throw new BusinessException("所属业务主体不存在");
            }
        }
        
        // 检查合作商是否存在
        if (shopDTO.getPartnerId() != null) {
            Partner partner = getPartnerService().getById(shopDTO.getPartnerId());
            if (partner == null) {
                throw new BusinessException("所属合作商不存在");
            }
            
            // 检查合作商是否属于指定的业务主�?
            Long entityId = shopDTO.getEntityId() != null ? shopDTO.getEntityId() : shop.getEntityId();
            if (!entityId.equals(partner.getEntityId())) {
                throw new BusinessException("所选合作商不属于指定的业务主体");
            }
        }
        
        // 如果修改了名称，检查名称是否已存在
        if (StringUtils.hasText(shopDTO.getName()) &&
            !shop.getShopName().equals(shopDTO.getName()) &&
            checkNameExists(shopDTO.getName())) {
            throw new BusinessException("门店名称已存在");
        }
        
        // 更新实体
        ShopConvert.updateEntityFromDto(shopDTO, shop);
        
        // 保存更新
        updateById(shop);
        
        return shop;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        Shop shop = getById(id);
        if (shop == null) {
            return false;
        }

        shop.setStatus(status);
        return updateById(shop);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 批量更新状�?
        return lambdaUpdate()
                .in(Shop::getId, ids)
                .set(Shop::getStatus, status)
                .update();
    }

    public Shop getByName(String name) {
        return lambdaQuery().eq(Shop::getShopName, name).one();
    }

    @Override
    public boolean checkNameExists(String name) {
        return lambdaQuery().eq(Shop::getShopName, name).exists();
    }



    /**
     * 生成门店编码
     *
     * @return 门店编码
     */
    private String generateShopCode() {
        // 生成格式：SHOP + 年月�?+ 4位随机数
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String randomStr = String.format("%04d", (int)(Math.random() * 10000));
        return "SHOP" + dateStr + randomStr;
    }
    
    public List<Shop> getByEntityId(Integer entityId) {
        return lambdaQuery()
                .eq(Shop::getEntityId, entityId)
                .orderByDesc(Shop::getCreateTime)
                .list();
    }

    public List<Shop> getByPartnerId(Integer partnerId) {
        return lambdaQuery()
                .eq(Shop::getPartnerId, partnerId)
                .orderByDesc(Shop::getCreateTime)
                .list();
    }
    
    @Override
    public ShopDetailVO getShopDetail(Long id) {
        // 获取门店信息
        Shop shop = getById(id);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }
        
        // 转换为VO
        ShopDetailVO detailVO = ShopConvert.entityToDetailVO(shop);
        
        // 设置业务主体名称
        if (shop.getEntityId() != null) {
            Entity entity = getEntityService().getById(shop.getEntityId());
            if (entity != null) {
                detailVO.setEntityName(entity.getName());
            }
        }
        
        // 设置合作商名�?
        if (shop.getPartnerId() != null) {
            Partner partner = getPartnerService().getById(shop.getPartnerId());
            if (partner != null) {
                detailVO.setPartnerName(partner.getName());
            }
        }
        
        // 设备统计
        List<Device> devices = getDeviceService().getDevicesByShop(Math.toIntExact(id));
        detailVO.setDeviceCount(devices.size());
        
        // 在线设备计数
        int onlineCount = 0;
        int faultCount = 0;
        for (Device device : devices) {
            if (device.getStatus() != null) {
                if (device.getStatus() == 1) { // 1-正常
                    onlineCount++;
                } else if (device.getStatus() == 3) { // 3-故障
                    faultCount++;
                }
            }
        }
        detailVO.setOnlineDeviceCount(onlineCount);
        detailVO.setOfflineDeviceCount(devices.size() - onlineCount - faultCount);
        detailVO.setFaultDeviceCount(faultCount);
        
        // 获取财务数据
        LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().withDayOfMonth(1), LocalTime.MIN);
        
        Map<String, Object> financeStats = getFinanceAccountService().getShopFinanceStats(id, today, monthStart);
        detailVO.setTodayOrderCount((Integer) financeStats.getOrDefault("todayOrderCount", 0));
        detailVO.setTodayIncome((BigDecimal) financeStats.getOrDefault("todayIncome", BigDecimal.ZERO));
        detailVO.setMonthIncome((BigDecimal) financeStats.getOrDefault("monthIncome", BigDecimal.ZERO));
        detailVO.setTotalIncome((BigDecimal) financeStats.getOrDefault("totalIncome", BigDecimal.ZERO));
        
        return detailVO;
    }
    
    @Override
    public List<ShopDetailVO> getShopListByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        
        List<Shop> shops = listByIds(ids);
        return shops.stream()
                .map(ShopConvert::entityToDetailVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取门店仪表盘数�?
     *
     * @param shopId 门店ID
     * @return 仪表盘数�?
     */
    @Override
    public Map<String, Object> getShopDashboard(Long shopId) {
        log.info("获取门店仪表盘数据，门店ID: {}", shopId);

        Map<String, Object> result = new HashMap<>();

        if (shopId == null) {
            throw new BusinessException("门店ID不能为空");
        }

        try {
            // 验证门店存在性
            Shop shop = getById(shopId);
            if (shop == null) {
                throw new BusinessException("门店不存在");
            }

            // 1. 门店基本信息
            Map<String, Object> shopInfo = new HashMap<>();
            shopInfo.put("id", shop.getId());
            shopInfo.put("shopName", shop.getShopName());
            shopInfo.put("status", shop.getStatus());
            shopInfo.put("statusName", getShopStatusName(shop.getStatus()));
            shopInfo.put("contactName", shop.getContactName());
            shopInfo.put("contactPhone", shop.getContactPhone());
            shopInfo.put("address", shop.getAddress());
            shopInfo.put("createTime", shop.getCreateTime());
            result.put("shopInfo", shopInfo);

            // 2. 设备统计
            result.put("deviceStats", getShopDeviceStatistics(shopId));

            // 3. 订单统计
            result.put("orderStats", getShopOrderStatistics(shopId));

            // 4. 财务统计
            result.put("financeStats", getShopFinanceStatistics(shopId));

            // 5. 今日实时数据
            result.put("todayRealTime", getShopTodayRealTimeData(shopId));

            // 6. 趋势数据（最�?天）
            result.put("trendData", getShopTrendData(shopId, 7));

            // 7. 结算配置信息
            result.put("settlementInfo", getShopSettlementInfo(shopId));

            // 8. 预警信息
            result.put("alertInfo", getShopAlertInfo(shopId));

            result.put("updateTime", LocalDateTime.now());

            log.info("获取门店仪表盘数据成功");
            return result;

        } catch (Exception e) {
            log.error("获取门店仪表盘数据失败", e);
            throw new BusinessException("获取仪表盘数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取门店在指定时间范围内的订单金额（分成后的门店收入�?
     *
     * @param shopId 门店ID
     * @param startTime 开始时�?
     * @param endTime 结束时间
     * @return 门店分成后的收入金额
     */
    private BigDecimal getShopOrderAmount(Long shopId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 查询指定时间范围内已完成且已支付的订�?
            List<Order> orders = getOrderService().lambdaQuery()
                    .eq(Order::getShopId, shopId)
                    .eq(Order::getOrderStatus, 2) // 已完成状�?
                    .eq(Order::getPayStatus, 1) // 已支付状�?
                    .eq(Order::getCommissionStatus, 1) // 已分成状�?
                    .ge(startTime != null, Order::getCreateTime, startTime)
                    .lt(endTime != null, Order::getCreateTime, endTime)
                    .list();

            if (orders.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 从分成详情表中获取门店实际收�?
            @NotNull List<String> orderIds = orders.stream()
                    .map(order -> order.getId().toString())
                    .collect(Collectors.toList());

            // 查询分成详情，获取门店实际收�?
            return commissionDetailService.lambdaQuery()
                    .in(CommissionDetail::getOrderId, orderIds)
                    .eq(CommissionDetail::getShopId, shopId.intValue())
                    .list()
                    .stream()
                    .map(CommissionDetail::getShopAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.warn("获取门店订单分成金额失败，门店ID: {}", shopId, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRevenueRatio(Long id, BigDecimal revenueRatio) {
        if (id == null || revenueRatio == null) {
            return false;
        }
        
        // 验证分成比例范围
        if (revenueRatio.compareTo(BigDecimal.ZERO) < 0 || revenueRatio.compareTo(new BigDecimal(100)) > 0) {
            throw new BusinessException("分成比例必须�?-100之间");
        }
        
        Shop shop = getById(id);
        if (shop == null) {
            return false;
        }
        
        shop.setRevenueRatio(revenueRatio);
        boolean updated = updateById(shop);
        
        if (updated) {
            // 更新分成配置
            try {
                commissionConfigService.updateShopCommissionRatio(id, revenueRatio);
            } catch (Exception e) {
                log.error("更新门店分成配置失败", e);
                // 不影响主流程，所以不抛出异常
            }
        }
        
        return updated;
    }

    @Override
    public Page<ShopDetailVO> getPartnerShopPage(int pageNum, int pageSize, Long partnerId, String shopName, Integer status) {
        Page<Shop> page = new Page<>(pageNum, pageSize);
        
        LambdaQueryWrapper<Shop> wrapper = Wrappers.<Shop>lambdaQuery()
                .eq(partnerId != null, Shop::getPartnerId, partnerId)
                .like(StringUtils.hasText(shopName), Shop::getShopName, shopName)
                .eq(status != null, Shop::getStatus, status)
                .orderByDesc(Shop::getCreateTime);
        
        Page<Shop> resultPage = page(page, wrapper);
        
        // 转换为VO
        Page<ShopDetailVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ShopDetailVO> voList = resultPage.getRecords().stream()
                .map(shop -> {
                    ShopDetailVO vo = ShopConvert.entityToDetailVO(shop);
                    
                    // 填充额外数据
                    if (shop.getPartnerId() != null) {
                        Partner partner = getPartnerService().getById(shop.getPartnerId());
                        if (partner != null) {
                            vo.setPartnerName(partner.getPartnerName());
                        }
                    }
                    
                    // 填充设备数量和订单数
                    int deviceCount = Math.toIntExact(getDeviceService().lambdaQuery()
                            .eq(Device::getShopId, shop.getId())
                            .count());
                    vo.setDeviceCount(deviceCount);
                    
                    return vo;
                })
                .collect(Collectors.toList());
        
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public Long countShopsByEntityId(Long entityId) {
        if (entityId == null) {
            return 0L;
        }
        return lambdaQuery()
                .eq(Shop::getEntityId, entityId)
                .eq(Shop::getStatus, 1) // 只统计启用状态的门店
                .count();
    }

    /**
     * 获取业务主体下的门店列表
     *
     * @param entityId 业务主体ID
     * @param shopName 门店名称
     * @param status 状�?
     * @return 门店列表
     */
    @Override
    public List<Shop> getEntityShops(Long entityId, String shopName, Integer status) {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getEntityId, entityId);
        
        if (StringUtils.hasText(shopName)) {
            queryWrapper.like(Shop::getShopName, shopName);
        }
        
        if (status != null) {
            queryWrapper.eq(Shop::getStatus, status);
        }
        
        queryWrapper.orderByDesc(Shop::getCreateTime);
        
        return this.list(queryWrapper);
    }

    /**
     * 获取业务主体下门店统计数�?
     *
     * @param entityId 业务主体ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getEntityShopStatistics(Long entityId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取门店总数
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getEntityId, entityId);
        long totalCount = this.count(queryWrapper);
        statistics.put("totalCount", totalCount);
        
        // 获取启用门店数量
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getEntityId, entityId);
        queryWrapper.eq(Shop::getStatus, 1);
        long activeCount = this.count(queryWrapper);
        statistics.put("activeCount", activeCount);
        
        // 获取禁用门店数量
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getEntityId, entityId);
        queryWrapper.eq(Shop::getStatus, 0);
        long inactiveCount = this.count(queryWrapper);
        statistics.put("inactiveCount", inactiveCount);
        
        // 获取最近一周新增门店数�?
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getEntityId, entityId);
        queryWrapper.ge(Shop::getCreateTime, LocalDateTime.now().minusDays(7));
        long recentAddedCount = this.count(queryWrapper);
        statistics.put("recentAddedCount", recentAddedCount);
        
        return statistics;
    }

    /**
     * 获取合作商下门店统计数据
     *
     * @param partnerId 合作商ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getPartnerShopStatistics(Long partnerId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取门店总数
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getPartnerId, partnerId);
        long totalCount = this.count(queryWrapper);
        statistics.put("totalCount", totalCount);
        
        // 获取启用门店数量
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getPartnerId, partnerId);
        queryWrapper.eq(Shop::getStatus, 1);
        long activeCount = this.count(queryWrapper);
        statistics.put("activeCount", activeCount);
        
        // 获取禁用门店数量
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getPartnerId, partnerId);
        queryWrapper.eq(Shop::getStatus, 0);
        long inactiveCount = this.count(queryWrapper);
        statistics.put("inactiveCount", inactiveCount);
        
        // 获取最近一周新增门店数�?
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getPartnerId, partnerId);
        queryWrapper.ge(Shop::getCreateTime, LocalDateTime.now().minusDays(7));
        long recentAddedCount = this.count(queryWrapper);
        statistics.put("recentAddedCount", recentAddedCount);
        
        return statistics;
    }

    /**
     * 根据管理员ID获取门店详情
     *
     * @param adminId 管理员ID
     * @return 门店详情
     */
    @Override
    public ShopDetailVO getShopDetailByAdminId(Long adminId) {
        // 根据管理员ID查询门店
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getAdminId, adminId);
        Shop shop = getOne(queryWrapper);
        if (shop == null) {
            throw new BusinessException("未找到与该管理员关联的门店");
        }
        // 获取门店详情
        return getShopDetail(shop.getId());
    }

    /**
     * 根据管理员ID获取门店信息
     *
     * @param adminId 管理员ID
     * @return 门店信息
     */
    @Override
    public Shop getShopByAdminId(Long adminId) {
        if (adminId == null) {
            return null;
        }

        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getAdminId, adminId);
        return getOne(queryWrapper);
    }

    /**
     * 更新门店联系人信�?
     *
     * @param id          门店ID
     * @param contactName 联系人姓�?
     * @param contactPhone 联系人电�?
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateShopContact(Long id, String contactName, String contactPhone) {
        // 校验参数
        if (!StringUtils.hasText(contactName)) {
            throw new BusinessException("联系人姓名不能为空");
        }
        if (!StringUtils.hasText(contactPhone)) {
            throw new BusinessException("联系人电话不能为空");
        }
        // 校验手机号格�?
        if (!contactPhone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("联系人电话格式不正确");
        }
        
        // 获取门店信息
        Shop shop = getById(id);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }
        
        // 更新联系人信�?
        shop.setContactName(contactName);
        shop.setContactPhone(contactPhone);
        return updateById(shop);
    }

    /**
     * 更新门店银行账户信息
     *
     * @param id          门店ID
     * @param bankName    开户银�?
     * @param bankAccount 银行卡号
     * @param accountName 收款人姓�?
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBankAccount(Long id, String bankName, String bankAccount, String accountName) {
        // 校验参数
        if (!StringUtils.hasText(bankName)) {
            throw new BusinessException("开户银行不能为空");
        }
        if (!StringUtils.hasText(bankAccount)) {
            throw new BusinessException("银行卡号不能为空");
        }
        if (!StringUtils.hasText(accountName)) {
            throw new BusinessException("收款人姓名不能为空");
        }
        // 简单校验银行卡号格式
        if (!bankAccount.matches("^\\d{16,19}$")) {
            throw new BusinessException("银行卡号格式不正确");
        }
        
        // 获取门店信息
        Shop shop = getById(id);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }
        
        // 更新银行账户信息
        shop.setBankName(bankName);
        shop.setBankAccount(bankAccount);
        shop.setAccountName(accountName);
        return updateById(shop);
    }

    /**
     * 获取所有门店的统计数据
     *
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getAllShopStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        // 统计门店总数
        result.put("totalShops", count());
        
        // 统计不同状态的门店数量
        LambdaQueryWrapper<Shop> enabledQuery = new LambdaQueryWrapper<>();
        enabledQuery.eq(Shop::getStatus, 1);
        long enabledCount = count(enabledQuery);
        result.put("enabledShops", enabledCount);
        
        LambdaQueryWrapper<Shop> disabledQuery = new LambdaQueryWrapper<>();
        disabledQuery.eq(Shop::getStatus, 0);
        long disabledCount = count(disabledQuery);
        result.put("disabledShops", disabledCount);
        
        // 统计门店设备总数
        result.put("totalDevices", baseMapper.sumDeviceCount());
        
        // 统计各省份门店分�?
        result.put("provinceDistribution", baseMapper.countGroupByProvince());
        
        // 统计�?0天新增门店数�?
        LocalDate thirtyDaysAgo = LocalDate.now().minusDays(30);
        LambdaQueryWrapper<Shop> recentQuery = new LambdaQueryWrapper<>();
        recentQuery.ge(Shop::getCreateTime, thirtyDaysAgo);
        long recentAddCount = count(recentQuery);
        result.put("recentAddShops", recentAddCount);
        
        return result;
    }

    /**
     * 获取门店统计数据
     *
     * @param shopId    门店ID
     * @param startTime 开始时�?
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Override
    public ShopStatisticsVO getShopStatistics(Long shopId, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取门店信息
        Shop shop = getById(shopId);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }

        ShopStatisticsVO statisticsVO = new ShopStatisticsVO();
        statisticsVO.setShopId(shopId);
        statisticsVO.setShopName(shop.getShopName());
        statisticsVO.setStartTime(startTime);
        statisticsVO.setEndTime(endTime);
        statisticsVO.setUpdateTime(LocalDateTime.now());

        // 设备统计
        Map<String, Object> deviceStats = getShopDeviceStatistics(shopId);
        if (deviceStats != null) {
            statisticsVO.setTotalDevices((Integer) deviceStats.get("totalDevices"));
            statisticsVO.setOnlineDevices((Integer) deviceStats.get("onlineDevices"));
            statisticsVO.setOfflineDevices((Integer) deviceStats.get("offlineDevices"));
            statisticsVO.setMaintenanceDevices((Integer) deviceStats.get("maintenanceDevices"));
            statisticsVO.setFaultDevices((Integer) deviceStats.get("faultDevices"));
        }

        // 订单统计
        Map<String, Object> orderStats = getShopOrderStatistics(shopId);
        if (orderStats != null) {
            statisticsVO.setTotalOrders(((Number) orderStats.get("totalOrders")).longValue());
            statisticsVO.setCompletedOrders(((Number) orderStats.get("completedOrders")).longValue());
            statisticsVO.setActiveOrders(((Number) orderStats.get("activeOrders")).longValue());
            statisticsVO.setCancelledOrders(((Number) orderStats.get("cancelledOrders")).longValue());
        }

        // 财务统计
        Map<String, Object> financeStats = getShopFinanceStatistics(shopId);
        if (financeStats != null) {
            statisticsVO.setTotalRevenue((BigDecimal) financeStats.get("totalRevenue"));
            statisticsVO.setTodayRevenue((BigDecimal) financeStats.get("todayRevenue"));
            statisticsVO.setMonthRevenue((BigDecimal) financeStats.get("monthRevenue"));
            statisticsVO.setShopCommission((BigDecimal) financeStats.get("shopCommission"));
        }

        return statisticsVO;
    }

    /**
     * 统计指定合作商下的门店数�?
     *
     * @param partnerId 合作商ID
     * @return 门店数量
     */
    @Override
    public Long countShopsByPartnerId(Long partnerId) {
        if (partnerId == null) {
            return 0L;
        }
        
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getPartnerId, partnerId);
        return count(queryWrapper);
    }

    /**
     * 根据ID查询门店
     *
     * @param id 门店ID
     * @return 门店信息
     */
    public Shop getShopById(Integer id) {
        if (id == null) {
            return null;
        }
        return getById(id);
    }

    /**
     * 获取门店地区分布数据
     *
     * @return 门店地区分布数据
     */
    @Override
    public Map<String, Object> getShopAreaDistribution() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 按省份统计门店数�?
            List<Map<String, Object>> provinceDistribution = baseMapper.countGroupByProvince();
            result.put("provinceDistribution", provinceDistribution);
            
            // 按城市统计门店数�?
            List<Map<String, Object>> cityDistribution = baseMapper.countGroupByCity();
            result.put("cityDistribution", cityDistribution);
            
            // 按地区统计门店数�?
            List<Map<String, Object>> districtDistribution = baseMapper.countGroupByDistrict();
            result.put("districtDistribution", districtDistribution);
            
        } catch (Exception e) {
            log.error("获取门店地区分布数据失败", e);
            result.put("error", "获取地区分布数据失败");
        }
        
        return result;
    }
    
    /**
     * 统计指定时间范围内新增的门店数量
     *
     * @param startTime 开始时�?
     * @param endTime 结束时间
     * @return 新增门店数量
     */
    @Override
    public Long countNewShopsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(Shop::getCreateTime, startTime)
                   .le(Shop::getCreateTime, endTime);
        return count(queryWrapper);
    }

    // ==================== 仪表盘数据统计辅助方�?====================

    /**
     * 获取门店设备统计数据
     *
     * @param shopId 门店ID
     * @return 设备统计数据
     */
    private Map<String, Object> getShopDeviceStatistics(Long shopId) {
        Map<String, Object> deviceStats = new HashMap<>();

        try {
            // 设备总数
            long totalDevices = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .count();

            // 在线设备�?
            long onlineDevices = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .eq(Device::getOnlineStatus, 1)
                .count();

            // 使用中设备数
            long inUseDevices = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .eq(Device::getInUse, 1)
                .count();

            // 故障设备�?
            long faultDevices = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .eq(Device::getStatus, 3) // 故障状�?
                .count();

            deviceStats.put("totalDevices", totalDevices);
            deviceStats.put("onlineDevices", onlineDevices);
            deviceStats.put("inUseDevices", inUseDevices);
            deviceStats.put("faultDevices", faultDevices);
            deviceStats.put("onlineRate", totalDevices > 0 ? (double) onlineDevices / totalDevices : 0);
            deviceStats.put("utilizationRate", totalDevices > 0 ? (double) inUseDevices / totalDevices : 0);
            deviceStats.put("faultRate", totalDevices > 0 ? (double) faultDevices / totalDevices : 0);

        } catch (Exception e) {
            log.warn("获取门店设备统计失败，门店ID: {}", shopId, e);
        }

        return deviceStats;
    }

    /**
     * 获取门店订单统计数据
     *
     * @param shopId 门店ID
     * @return 订单统计数据
     */
    private Map<String, Object> getShopOrderStatistics(Long shopId) {
        Map<String, Object> orderStats = new HashMap<>();

        try {
            LocalDateTime today = LocalDate.now().atStartOfDay();
            LocalDateTime yesterday = today.minusDays(1);
            LocalDateTime weekStart = today.minusDays(7);
            LocalDateTime monthStart = LocalDate.now().withDayOfMonth(1).atStartOfDay();

            // 今日订单
            long todayOrderCount = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .ge(Order::getCreateTime, today)
                .count();

            // 昨日订单
            long yesterdayOrderCount = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .ge(Order::getCreateTime, yesterday)
                .lt(Order::getCreateTime, today)
                .count();

            // 本周订单
            long weekOrderCount = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .ge(Order::getCreateTime, weekStart)
                .count();

            // 本月订单
            long monthOrderCount = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .ge(Order::getCreateTime, monthStart)
                .count();

            // 今日金额
            BigDecimal todayAmount = getShopOrderAmount(shopId, today, LocalDateTime.now());

            // 昨日金额
            BigDecimal yesterdayAmount = getShopOrderAmount(shopId, yesterday, today);

            orderStats.put("todayOrderCount", todayOrderCount);
            orderStats.put("yesterdayOrderCount", yesterdayOrderCount);
            orderStats.put("weekOrderCount", weekOrderCount);
            orderStats.put("monthOrderCount", monthOrderCount);
            orderStats.put("todayAmount", todayAmount);
            orderStats.put("yesterdayAmount", yesterdayAmount);

            // 计算增长�?
            if (yesterdayOrderCount > 0) {
                double orderGrowthRate = (double) (todayOrderCount - yesterdayOrderCount) / yesterdayOrderCount * 100;
                orderStats.put("orderGrowthRate", orderGrowthRate);
            } else {
                orderStats.put("orderGrowthRate", 0.0);
            }

            if (yesterdayAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal amountGrowthRate = todayAmount.subtract(yesterdayAmount)
                    .divide(yesterdayAmount, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                orderStats.put("amountGrowthRate", amountGrowthRate);
            } else {
                orderStats.put("amountGrowthRate", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("获取门店订单统计失败，门店ID: {}", shopId, e);
        }

        return orderStats;
    }

    /**
     * 获取门店财务统计数据
     *
     * @param shopId 门店ID
     * @return 财务统计数据
     */
    private Map<String, Object> getShopFinanceStatistics(Long shopId) {
        Map<String, Object> financeStats = new HashMap<>();

        try {
            // 获取财务账户余额
            BigDecimal balance = getFinanceAccountService().getShopBalance(shopId);
            BigDecimal totalIncome = getFinanceAccountService().getShopTotalIncome(shopId);
            BigDecimal waitingSettlement = getFinanceAccountService().getShopWaitingSettlement(shopId);

            // 获取今日收入（分成后的金额）
            LocalDateTime todayStart = LocalDate.now().atStartOfDay();
            BigDecimal todayRevenue = getShopOrderAmount(shopId, todayStart, LocalDateTime.now());

            financeStats.put("balance", balance != null ? balance : BigDecimal.ZERO);
            financeStats.put("totalIncome", totalIncome != null ? totalIncome : BigDecimal.ZERO);
            financeStats.put("waitingSettlement", waitingSettlement != null ? waitingSettlement : BigDecimal.ZERO);
            financeStats.put("todayRevenue", todayRevenue != null ? todayRevenue : BigDecimal.ZERO);

        } catch (Exception e) {
            log.warn("获取门店财务统计失败，门店ID: {}", shopId, e);
            financeStats.put("balance", BigDecimal.ZERO);
            financeStats.put("totalIncome", BigDecimal.ZERO);
            financeStats.put("waitingSettlement", BigDecimal.ZERO);
            financeStats.put("todayRevenue", BigDecimal.ZERO);
        }

        return financeStats;
    }

    /**
     * 获取门店今日实时数据
     *
     * @param shopId 门店ID
     * @return 今日实时数据
     */
    private Map<String, Object> getShopTodayRealTimeData(Long shopId) {
        Map<String, Object> realTimeData = new HashMap<>();

        try {
            LocalDateTime todayStart = LocalDate.now().atStartOfDay();
            LocalDateTime now = LocalDateTime.now();

            // 今日新增订单
            long todayNewOrders = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .ge(Order::getCreateTime, todayStart)
                .count();

            // 今日完成订单
            long todayCompletedOrders = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .eq(Order::getOrderStatus, 2) // 已完�?
                .ge(Order::getCreateTime, todayStart)
                .count();

            // 今日进行中订�?
            long todayActiveOrders = getOrderService().lambdaQuery()
                .eq(Order::getShopId, shopId)
                .eq(Order::getOrderStatus, 1) // 进行�?
                .count();

            // 今日收入
            BigDecimal todayRevenue = getShopOrderAmount(shopId, todayStart, now);

            realTimeData.put("todayNewOrders", todayNewOrders);
            realTimeData.put("todayCompletedOrders", todayCompletedOrders);
            realTimeData.put("todayActiveOrders", todayActiveOrders);
            realTimeData.put("todayRevenue", todayRevenue);
            realTimeData.put("updateTime", now);

        } catch (Exception e) {
            log.warn("获取门店今日实时数据失败，门店ID: {}", shopId, e);
        }

        return realTimeData;
    }

    /**
     * 获取门店趋势数据
     *
     * @param shopId 门店ID
     * @param days 天数
     * @return 趋势数据
     */
    private List<Map<String, Object>> getShopTrendData(Long shopId, int days) {
        List<Map<String, Object>> trendData = new ArrayList<>();

        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days - 1);

            for (int i = 0; i < days; i++) {
                LocalDate currentDate = startDate.plusDays(i);
                LocalDateTime dayStart = currentDate.atStartOfDay();
                LocalDateTime dayEnd = currentDate.atTime(LocalTime.MAX);

                // 当天订单�?
                long dayOrderCount = getOrderService().lambdaQuery()
                    .eq(Order::getShopId, shopId)
                    .ge(Order::getCreateTime, dayStart)
                    .le(Order::getCreateTime, dayEnd)
                    .count();

                // 当天收入
                BigDecimal dayRevenue = getShopOrderAmount(shopId, dayStart, dayEnd);

                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", currentDate.toString());
                dayData.put("orderCount", dayOrderCount);
                dayData.put("revenue", dayRevenue);

                trendData.add(dayData);
            }

        } catch (Exception e) {
            log.warn("获取门店趋势数据失败，门店ID: {}, 天数: {}", shopId, days, e);
        }

        return trendData;
    }

    /**
     * 获取门店结算配置信息
     *
     * @param shopId 门店ID
     * @return 结算配置信息
     */
    private Map<String, Object> getShopSettlementInfo(Long shopId) {
        Map<String, Object> settlementInfo = new HashMap<>();

        try {
            SettlementConfig config = settlementConfigService.getShopConfig(Math.toIntExact(shopId));
            if (config != null) {
                settlementInfo.put("settlementType", config.getSettlementType());
                settlementInfo.put("settlementDay", config.getSettlementDay());
                settlementInfo.put("minSettlementAmount", config.getMinSettlementAmount());
                // SettlementConfig没有commissionRate字段，分成比例应该从其他地方获取
            } else {
                // 默认配置
                settlementInfo.put("settlementType", "daily");
                settlementInfo.put("settlementDay", 1);
                settlementInfo.put("minSettlementAmount", BigDecimal.valueOf(100));
                settlementInfo.put("commissionRate", BigDecimal.valueOf(30));
            }

        } catch (Exception e) {
            log.warn("获取门店结算配置失败，门店ID: {}", shopId, e);
        }

        return settlementInfo;
    }

    /**
     * 获取门店预警信息
     *
     * @param shopId 门店ID
     * @return 预警信息
     */
    private Map<String, Object> getShopAlertInfo(Long shopId) {
        Map<String, Object> alertInfo = new HashMap<>();
        List<Map<String, Object>> alerts = new ArrayList<>();

        try {
            // 检查故障设�?
            long faultDeviceCount = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .eq(Device::getStatus, 3) // 故障状�?
                .count();

            if (faultDeviceCount > 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("type", "device_fault");
                alert.put("level", "high");
                alert.put("message", "有" + faultDeviceCount + " 台设备故障，请及时处理");
                alert.put("count", faultDeviceCount);
                alerts.add(alert);
            }

            // 检查离线设�?
            long offlineDeviceCount = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .eq(Device::getOnlineStatus, 0) // 离线状�?
                .count();

            if (offlineDeviceCount > 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("type", "device_offline");
                alert.put("level", "medium");
                alert.put("message", "有" + offlineDeviceCount + " 台设备离线");
                alert.put("count", offlineDeviceCount);
                alerts.add(alert);
            }

            // 检查低电量设备
            long lowBatteryDeviceCount = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, shopId)
                .lt(Device::getBatteryLevel, 20) // 电量低于20%
                .count();

            if (lowBatteryDeviceCount > 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("type", "low_battery");
                alert.put("level", "medium");
                alert.put("message", "有" + lowBatteryDeviceCount + " 台设备电量不足");
                alert.put("count", lowBatteryDeviceCount);
                alerts.add(alert);
            }

            alertInfo.put("alerts", alerts);
            alertInfo.put("totalAlerts", alerts.size());
            alertInfo.put("hasHighLevelAlert", alerts.stream().anyMatch(alert -> "high".equals(alert.get("level"))));

        } catch (Exception e) {
            log.warn("获取门店预警信息失败，门店ID: {}", shopId, e);
        }

        return alertInfo;
    }

    /**
     * 获取门店状态名�?
     *
     * @param status 状态码
     * @return 状态名�?
     */
    private String getShopStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "禁用";
            case 1: return "正常";
            case 2: return "维护";
            default: return "未知";
        }
    }

    /**
     * 将Shop实体转换为ShopVO
     *
     * @param shop Shop实体
     * @return ShopVO
     */
    private ShopVO convertToShopVO(Shop shop) {
        if (shop == null) {
            return null;
        }

        ShopVO vo = new ShopVO();
        vo.setId(shop.getId().longValue());
        vo.setEntityId(shop.getEntityId());
        vo.setPartnerId(shop.getPartnerId());
        vo.setShopName(shop.getShopName());
        vo.setShopCode(shop.getShopCode());
        vo.setContactName(shop.getContactName());
        vo.setContactPhone(shop.getContactPhone());
        vo.setProvince(shop.getProvince());
        vo.setCity(shop.getCity());
        vo.setDistrict(shop.getDistrict());
        vo.setAddress(shop.getAddress());
        vo.setRevenueRatio(shop.getRevenueRatio());
        vo.setBankName(shop.getBankName());
        vo.setBankAccount(shop.getBankAccount());
        vo.setAccountName(shop.getAccountName());
        vo.setDeviceCount(shop.getDeviceCount());
        vo.setGroupId(shop.getGroupId());
        vo.setStatus(shop.getStatus());
        vo.setStatusName(getShopStatusName(shop.getStatus()));
        vo.setRemark(shop.getRemark());
        vo.setCreateTime(shop.getCreateTime());
        vo.setUpdateTime(shop.getUpdateTime());

        return vo;
    }

    /**
     * 验证门店是否属于指定业务主体
     *
     * @param shopId   门店ID
     * @param entityId 业务主体ID
     * @return 是否属于
     */
    @Override
    public boolean belongsToEntity(Long shopId, Long entityId) {
        if (shopId == null || entityId == null) {
            return false;
        }

        Shop shop = getById(shopId);
        if (shop == null) {
            return false;
        }

        return entityId.equals(shop.getEntityId());
    }

    /**
     * 验证门店是否属于指定合作�?
     *
     * @param shopId    门店ID
     * @param partnerId 合作商ID
     * @return 是否属于
     */
    @Override
    public boolean belongsToPartner(Long shopId, Long partnerId) {
        if (shopId == null || partnerId == null) {
            return false;
        }

        Shop shop = getById(shopId);
        if (shop == null) {
            return false;
        }

        return partnerId.equals(shop.getPartnerId());
    }

    /**
     * 检查门店编码是否已存在
     *
     * @param code      门店编码
     * @param entityId  业务主体ID
     * @param excludeId 排除的门店ID
     * @return 是否存在
     */
    @Override
    public boolean existsByCode(String code, Long entityId, Long excludeId) {
        if (!StringUtils.hasText(code) || entityId == null) {
            return false;
        }

        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getShopCode, code)
                   .eq(Shop::getEntityId, entityId);

        // 排除指定ID的门�?
        if (excludeId != null) {
            queryWrapper.ne(Shop::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    /**
     * 检查门店名称是否已存在
     *
     * @param name      门店名称
     * @param entityId  业务主体ID
     * @param excludeId 排除的门店ID
     * @return 是否存在
     */
    @Override
    public boolean existsByName(String name, Long entityId, Long excludeId) {
        if (!StringUtils.hasText(name) || entityId == null) {
            return false;
        }

        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getShopName, name)
                   .eq(Shop::getEntityId, entityId);

        // 排除指定ID的门�?
        if (excludeId != null) {
            queryWrapper.ne(Shop::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    /**
     * 获取门店选项列表（用于下拉选择等场景）
     *
     * @param entityId  业务主体ID（可选）
     * @param partnerId 合作商ID（可选）
     * @return 门店选项列表
     */
    @Override
    public List<ShopVO> getShopOptions(Long entityId, Long partnerId) {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询启用状态的门店
        queryWrapper.eq(Shop::getStatus, 1);

        // 根据业务主体ID过滤
        if (entityId != null) {
            queryWrapper.eq(Shop::getEntityId, entityId);
        }

        // 根据合作商ID过滤
        if (partnerId != null) {
            queryWrapper.eq(Shop::getPartnerId, partnerId);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Shop::getCreateTime);

        List<Shop> shops = list(queryWrapper);

        // 转换为VO
        return shops.stream()
                .map(this::convertToShopVO)
                .collect(Collectors.toList());
    }

    /**
     * 根据合作商ID获取门店列表
     *
     * @param partnerId 合作商ID
     * @param queryDTO  查询条件
     * @return 门店列表
     */
    @Override
    public List<ShopVO> getByPartnerId(Long partnerId, ShopQueryDTO queryDTO) {
        if (partnerId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getPartnerId, partnerId);

        // 应用查询条件
        if (queryDTO != null) {
            // 门店名称模糊查询
            if (StringUtils.hasText(queryDTO.getName())) {
                queryWrapper.like(Shop::getShopName, queryDTO.getName());
            }

            // 状态过�?
            if (queryDTO.getStatus() != null) {
                queryWrapper.eq(Shop::getStatus, queryDTO.getStatus());
            }

            // 省份过滤
            if (StringUtils.hasText(queryDTO.getProvince())) {
                queryWrapper.eq(Shop::getProvince, queryDTO.getProvince());
            }

            // 城市过滤
            if (StringUtils.hasText(queryDTO.getCity())) {
                queryWrapper.eq(Shop::getCity, queryDTO.getCity());
            }
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Shop::getCreateTime);

        List<Shop> shops = list(queryWrapper);

        // 转换为VO
        return shops.stream()
                .map(this::convertToShopVO)
                .collect(Collectors.toList());
    }

    /**
     * 根据业务主体ID获取门店列表
     *
     * @param entityId 业务主体ID
     * @param queryDTO 查询条件
     * @return 门店列表
     */
    @Override
    public List<ShopVO> getByEntityId(Long entityId, ShopQueryDTO queryDTO) {
        if (entityId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getEntityId, entityId);

        // 应用查询条件
        if (queryDTO != null) {
            // 门店名称模糊查询
            if (StringUtils.hasText(queryDTO.getName())) {
                queryWrapper.like(Shop::getShopName, queryDTO.getName());
            }

            // 状态过�?
            if (queryDTO.getStatus() != null) {
                queryWrapper.eq(Shop::getStatus, queryDTO.getStatus());
            }

            // 省份过滤
            if (StringUtils.hasText(queryDTO.getProvince())) {
                queryWrapper.eq(Shop::getProvince, queryDTO.getProvince());
            }

            // 城市过滤
            if (StringUtils.hasText(queryDTO.getCity())) {
                queryWrapper.eq(Shop::getCity, queryDTO.getCity());
            }
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Shop::getCreateTime);

        List<Shop> shops = list(queryWrapper);

        // 转换为VO
        return shops.stream()
                .map(this::convertToShopVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取门店基本信息
     *
     * @param id 门店ID
     * @return 门店基本信息
     */
    @Override
    public ShopVO getShopVO(Long id) {
        if (id == null) {
            return null;
        }

        Shop shop = getById(id);
        if (shop == null) {
            return null;
        }

        return convertToShopVO(shop);
    }

    /**
     * 根据ID查询门店
     *
     * @param id 门店ID
     * @return 门店信息
     */
    @Override
    public Shop getShopById(Long id) {
        if (id == null) {
            return null;
        }
        return getById(id);
    }

    /**
     * 删除门店
     *
     * @param id 门店ID
     * @return 是否删除成功
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteShop(Long id) {
        if (id == null) {
            throw new BusinessException("门店ID不能为空");
        }

        // 检查门店是否存在
        Shop shop = getById(id);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }

        // 检查门店下是否有设�?
        long deviceCount = getDeviceService().lambdaQuery()
                .eq(Device::getShopId, id)
                .count();
        if (deviceCount > 0) {
            throw new BusinessException("门店下还有设备，无法删除");
        }

        // 检查门店下是否有未完成的订�?
        long activeOrderCount = getOrderService().lambdaQuery()
                .eq(Order::getShopId, id)
                .in(Order::getOrderStatus, 1, 2, 3) // 进行中、已完成、已取消
                .count();
        if (activeOrderCount > 0) {
            throw new BusinessException("门店下还有未完成的订单，无法删除");
        }

        // 删除门店
        boolean result = removeById(id);

        if (result) {
            // 删除相关的财务账�?
            try {
                getFinanceCoordinatorService().deleteAccount(
                    FinanceConstants.AccountType.SHOP,
                    id
                );
                log.info("门店财务账户删除成功，门店ID: {}", id);
            } catch (Exception e) {
                log.warn("删除门店财务账户失败，门店ID: {}", id, e);
            }

            log.info("门店删除成功，门店ID: {}", id);
        }

        return result;
    }

    /**
     * 更新门店信息
     *
     * @param id      门店ID
     * @param shopDTO 门店信息
     * @return 更新后的门店信息
     * @throws BusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Shop updateShop(Long id, ShopDTO shopDTO) {
        if (id == null || shopDTO == null) {
            throw new BusinessException("参数错误");
        }

        // 获取现有门店信息
        Shop existingShop = getById(id);
        if (existingShop == null) {
            throw new BusinessException("门店不存在");
        }

        // 检查门店名称是否已存在（排除当前门店）
        if (StringUtils.hasText(shopDTO.getName()) &&
            !existingShop.getShopName().equals(shopDTO.getName()) &&
            existsByName(shopDTO.getName(), shopDTO.getEntityId(), id)) {
            throw new BusinessException("门店名称已存在");
        }

        // 检查门店编码是否已存在（排除当前门店）
        if (StringUtils.hasText(shopDTO.getCode()) &&
            !Objects.equals(existingShop.getShopCode(), shopDTO.getCode()) &&
            existsByCode(shopDTO.getCode(), shopDTO.getEntityId(), id)) {
            throw new BusinessException("门店编码已存在");
        }

        // 更新门店信息
        ShopConvert.updateEntityFromDto(shopDTO, existingShop);

        // 保存更新
        updateById(existingShop);

        return existingShop;
    }


}
