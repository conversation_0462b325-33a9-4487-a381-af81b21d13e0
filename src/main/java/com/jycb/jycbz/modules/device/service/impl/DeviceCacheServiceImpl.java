package com.jycb.jycbz.modules.device.service.impl;

import com.jycb.jycbz.common.constant.CacheConstants;
import com.jycb.jycbz.common.service.CacheService;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.mapper.DeviceMapper;
import com.jycb.jycbz.modules.device.service.DeviceCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 设备缓存管理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceCacheServiceImpl implements DeviceCacheService {

    private final CacheService cacheService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DeviceMapper deviceMapper;

    @Override
    public void clearDeviceCache(Integer deviceId) {
        if (deviceId == null) {
            return;
        }

        log.info("清除设备缓存，设备ID: {}", deviceId);

        // 清除设备详情缓存
        clearDeviceDetailCache(deviceId);

        // 清除设备绑定状态缓存
        clearDeviceBindStatusCache(deviceId);

        // 清除设备基础信息缓存
        String deviceCacheKey = CacheConstants.Business.DEVICE_PREFIX + deviceId;
        cacheService.delete(deviceCacheKey);

        // 清除Spring Cache注解缓存
        cacheService.deletePattern("device::" + deviceId + "*");
        cacheService.deletePattern("deviceByNo::*");

        log.debug("设备缓存清除完成，设备ID: {}", deviceId);
    }

    @Override
    public void clearDeviceDetailCache(Integer deviceId) {
        if (deviceId == null) {
            return;
        }

        String detailCacheKey = CacheConstants.Business.DEVICE_DETAIL_PREFIX + deviceId;
        cacheService.delete(detailCacheKey);
        log.debug("清除设备详情缓存，设备ID: {}", deviceId);
    }

    @Override
    public void clearDeviceMacCache(String macAddress) {
        if (!StringUtils.hasText(macAddress)) {
            return;
        }

        String macCacheKey = CacheConstants.Business.DEVICE_MAC_PREFIX + macAddress;
        cacheService.delete(macCacheKey);
        log.debug("清除设备MAC地址缓存，MAC: {}", macAddress);
    }

    @Override
    public void clearDeviceNoCache(String deviceNo) {
        if (!StringUtils.hasText(deviceNo)) {
            return;
        }

        String noCacheKey = CacheConstants.Business.DEVICE_NO_PREFIX + deviceNo;
        cacheService.delete(noCacheKey);
        
        // 清除Spring Cache注解缓存
        cacheService.deletePattern("deviceByNo::" + deviceNo + "*");
        log.debug("清除设备编号缓存，设备编号: {}", deviceNo);
    }

    @Override
    public void clearDeviceBindStatusCache(Integer deviceId) {
        if (deviceId == null) {
            return;
        }

        String bindStatusCacheKey = CacheConstants.Business.DEVICE_BIND_STATUS_PREFIX + deviceId;
        cacheService.delete(bindStatusCacheKey);
        log.debug("清除设备绑定状态缓存，设备ID: {}", deviceId);
    }

    @Override
    public void clearShopDevicesCache(Integer shopId) {
        if (shopId == null) {
            return;
        }

        String shopDevicesCacheKey = CacheConstants.Business.SHOP_DEVICES_PREFIX + shopId;
        cacheService.delete(shopDevicesCacheKey);
        
        // 清除门店设备相关的模式缓存
        cacheService.deletePattern(CacheConstants.Business.SHOP_DEVICES_PREFIX + shopId + "*");
        log.debug("清除门店设备列表缓存，门店ID: {}", shopId);
    }

    @Override
    public void clearDeviceAllRelatedCache(Device device, Integer oldShopId) {
        if (device == null) {
            return;
        }

        log.info("清除设备所有相关缓存，设备ID: {}, 设备编号: {}, 旧门店ID: {}", 
                device.getId(), device.getDeviceNo(), oldShopId);

        // 清除设备基础缓存
        clearDeviceCache(device.getId());

        // 清除MAC地址缓存
        if (StringUtils.hasText(device.getMacAddress())) {
            clearDeviceMacCache(device.getMacAddress());
        }

        // 清除设备编号缓存
        if (StringUtils.hasText(device.getDeviceNo())) {
            clearDeviceNoCache(device.getDeviceNo());
        }

        // 清除当前门店的设备列表缓存
        if (device.getShopId() != null) {
            clearShopDevicesCache(device.getShopId());
        }

        // 清除旧门店的设备列表缓存
        if (oldShopId != null && !oldShopId.equals(device.getShopId())) {
            clearShopDevicesCache(oldShopId);
        }

        log.debug("设备所有相关缓存清除完成，设备ID: {}", device.getId());
    }

    @Override
    public void batchClearDeviceCache(List<Integer> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return;
        }

        log.info("批量清除设备缓存，数量: {}", deviceIds.size());

        for (Integer deviceId : deviceIds) {
            clearDeviceCache(deviceId);
        }

        log.info("批量清除设备缓存完成，数量: {}", deviceIds.size());
    }

    @Override
    public void clearShopRelatedDeviceCache(Integer shopId) {
        if (shopId == null) {
            return;
        }

        log.info("清除门店相关的所有设备缓存，门店ID: {}", shopId);

        // 清除门店设备列表缓存
        clearShopDevicesCache(shopId);

        // 查询门店下的所有设备，清除它们的缓存
        try {
            List<Device> devices = deviceMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<Device>()
                    .eq(Device::getShopId, shopId)
            );

            for (Device device : devices) {
                clearDeviceCache(device.getId());
            }

            log.info("门店相关设备缓存清除完成，门店ID: {}, 设备数量: {}", shopId, devices.size());
        } catch (Exception e) {
            log.error("清除门店相关设备缓存失败，门店ID: {}", shopId, e);
        }
    }

    @Override
    public void refreshDeviceCache(Device device) {
        if (device == null || device.getId() == null) {
            return;
        }

        log.debug("刷新设备缓存，设备ID: {}", device.getId());

        // 先清除旧缓存
        clearDeviceCache(device.getId());

        // 重新缓存设备信息
        String deviceCacheKey = CacheConstants.Business.DEVICE_PREFIX + device.getId();
        cacheService.set(deviceCacheKey, device, CacheConstants.TTL.THIRTY_MINUTES, TimeUnit.SECONDS);

        // 缓存MAC地址映射
        if (StringUtils.hasText(device.getMacAddress())) {
            String macCacheKey = CacheConstants.Business.DEVICE_MAC_PREFIX + device.getMacAddress();
            cacheService.set(macCacheKey, device, CacheConstants.TTL.THIRTY_MINUTES, TimeUnit.SECONDS);
        }

        // 缓存设备编号映射
        if (StringUtils.hasText(device.getDeviceNo())) {
            String noCacheKey = CacheConstants.Business.DEVICE_NO_PREFIX + device.getDeviceNo();
            cacheService.set(noCacheKey, device, CacheConstants.TTL.THIRTY_MINUTES, TimeUnit.SECONDS);
        }

        log.debug("设备缓存刷新完成，设备ID: {}", device.getId());
    }

    @Override
    public void warmUpDeviceCache(List<Integer> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return;
        }

        log.info("预热设备缓存，数量: {}", deviceIds.size());

        try {
            List<Device> devices = deviceMapper.selectBatchIds(deviceIds);
            for (Device device : devices) {
                refreshDeviceCache(device);
            }
            log.info("设备缓存预热完成，数量: {}", devices.size());
        } catch (Exception e) {
            log.error("设备缓存预热失败", e);
        }
    }

    @Override
    public boolean checkAndFixCacheInconsistency(Integer deviceId) {
        if (deviceId == null) {
            return false;
        }

        try {
            // 从数据库获取最新数据
            Device dbDevice = deviceMapper.selectById(deviceId);
            if (dbDevice == null) {
                // 设备不存在，清除所有相关缓存
                clearDeviceCache(deviceId);
                return true;
            }

            // 检查缓存中的数据
            String deviceCacheKey = CacheConstants.Business.DEVICE_PREFIX + deviceId;
            Device cachedDevice = cacheService.get(deviceCacheKey, Device.class);

            boolean inconsistent = false;

            if (cachedDevice != null) {
                // 检查关键字段是否一致
                if (!java.util.Objects.equals(cachedDevice.getIsBound(), dbDevice.getIsBound()) ||
                    !java.util.Objects.equals(cachedDevice.getShopId(), dbDevice.getShopId()) ||
                    !java.util.Objects.equals(cachedDevice.getPartnerId(), dbDevice.getPartnerId()) ||
                    !java.util.Objects.equals(cachedDevice.getEntityId(), dbDevice.getEntityId()) ||
                    !java.util.Objects.equals(cachedDevice.getStatus(), dbDevice.getStatus())) {
                    
                    inconsistent = true;
                    log.warn("发现设备缓存不一致，设备ID: {}, 数据库绑定状态: {}, 缓存绑定状态: {}, " +
                            "数据库门店ID: {}, 缓存门店ID: {}", 
                            deviceId, dbDevice.getIsBound(), cachedDevice.getIsBound(),
                            dbDevice.getShopId(), cachedDevice.getShopId());
                }
            }

            if (inconsistent) {
                // 修复缓存不一致
                clearDeviceAllRelatedCache(dbDevice, cachedDevice != null ? cachedDevice.getShopId() : null);
                refreshDeviceCache(dbDevice);
                log.info("已修复设备缓存不一致问题，设备ID: {}", deviceId);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("检查设备缓存一致性失败，设备ID: {}", deviceId, e);
            return false;
        }
    }

    @Override
    public int batchCheckAndFixCacheInconsistency(List<Integer> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return 0;
        }

        log.info("批量检查并修复设备缓存不一致，数量: {}", deviceIds.size());

        int fixedCount = 0;
        for (Integer deviceId : deviceIds) {
            if (checkAndFixCacheInconsistency(deviceId)) {
                fixedCount++;
            }
        }

        log.info("批量修复设备缓存不一致完成，总数: {}, 修复数: {}", deviceIds.size(), fixedCount);
        return fixedCount;
    }

    @Override
    public void clearAllDeviceCache() {
        log.warn("清除所有设备相关缓存");

        try {
            // 清除所有设备相关的缓存模式
            cacheService.deletePattern(CacheConstants.Business.DEVICE_PREFIX + "*");
            cacheService.deletePattern(CacheConstants.Business.DEVICE_DETAIL_PREFIX + "*");
            cacheService.deletePattern(CacheConstants.Business.DEVICE_MAC_PREFIX + "*");
            cacheService.deletePattern(CacheConstants.Business.DEVICE_NO_PREFIX + "*");
            cacheService.deletePattern(CacheConstants.Business.DEVICE_BIND_STATUS_PREFIX + "*");
            cacheService.deletePattern(CacheConstants.Business.SHOP_DEVICES_PREFIX + "*");
            
            // 清除Spring Cache注解缓存
            cacheService.deletePattern("device::*");
            cacheService.deletePattern("deviceByNo::*");

            log.warn("所有设备相关缓存清除完成");
        } catch (Exception e) {
            log.error("清除所有设备缓存失败", e);
        }
    }
}
