{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/App.vue?44af", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/App.vue?519d", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/App.vue?bc88", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/App.vue?4625"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "prototype", "$store", "store", "$api", "API", "$utils", "utils", "$auth", "auth", "App", "mpType", "dispatch", "app", "$mount", "onLaunch", "console", "setTimeout", "onShow", "onHide", "methods", "checkLoginStatus", "uni", "url", "initTabBar", "getSafeAreaInfo", "top", "right", "bottom", "left", "width", "height", "setSafeAreaStyle"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AAA8C;AAAA;AAL9C;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAO1DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCF,YAAG,CAACG,SAAS,CAACC,MAAM,GAAGC,cAAK;AAC5BL,YAAG,CAACG,SAAS,CAACG,IAAI,GAAGC,YAAG;AACxBP,YAAG,CAACG,SAAS,CAACK,MAAM,GAAGC,YAAK;AAC5BT,YAAG,CAACG,SAAS,CAACO,KAAK,GAAGC,WAAI;AAC1BC,YAAG,CAACC,MAAM,GAAG,KAAK;;AAElB;AACAR,cAAK,CAACS,QAAQ,CAAC,cAAc,CAAC;AAE9B,IAAMC,GAAG,GAAG,IAAIf,YAAG;EACfK,KAAK,EAALA;AAAK,GACFO,YAAG,EACR;AACF,UAAAG,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACvBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC0L;AAC1L,gBAAgB,uMAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAmtB,CAAgB,+uBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCvuB;EACAC;IAAA;IACAC;;IAEA;IACA;;IAEA;IACAC;MACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACAF;;IAEA;IACA;;IAEA;IACA;EACA;EACAG;IACAH;EACA;EACAI;IACA;IACAC;MACA;QACA;QACA;QACA;QAEAL;;QAEA;QACA;UACA;UACA;UAEA;YACAA;YACAM;cACAC;YACA;UACA;QACA;MACA;QACAP;MACA;IACA;IACA;IACAQ;MACAR;;MAEA;MACA;IACA;IAEA;IACAS;MACA;QACA;QACAT;;QAEA;QACA;UACA;YAAAU;YAAAC;YAAAC;YAAAC;YAAAC;YAAAC;UACA;YACAL;YACAC;YACAC;YACAC;UACA;;UAEA;UACAP;UACAN;;UAEA;UACA;YACAA;YACAM;UACA;YACAA;UACA;QACA;MACA;QACAN;MACA;IACA;IACA;IACAgB;MACA;MACA;MACA;;MAEA;MACAf;QACA;QACA;UACA;UACA;UACA;YACA;YACA;YACAD;UACA;;UAEA;UACAM;UAEAN;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAohC,CAAgB,whCAAG,EAAC,C;;;;;;;;;;;ACAxiC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\nimport App from './App'\nimport store from './store'\nimport API from './static/js/api.js'\nimport { utils, auth } from './utils/index.js'\n\n\nVue.config.productionTip = false\nVue.prototype.$store = store\nVue.prototype.$api = API\nVue.prototype.$utils = utils\nVue.prototype.$auth = auth\nApp.mpType = 'app'\n\n// 初始化应用状态\nstore.dispatch('initAppState')\n\nconst app = new Vue({\n    store,\n    ...App\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\texport default {\n\t\tonLaunch: function() {\n\t\t\tconsole.log('App Launch')\n\t\t\t\n\t\t\t// 立即隐藏TabBar，避免初始闪烁\n\t\t\tthis.hideTabBar();\n\t\t\t\n\t\t\t// 延迟初始化TabBar，确保应用完全加载\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.initTabBar();\n\t\t\t}, 50);\n\t\t\t\n\t\t\t// 检查登录状态\n\t\t\tthis.checkLoginStatus();\n\t\t\t\n\t\t\t// 获取安全区域信息\n\t\t\tthis.getSafeAreaInfo();\n\t\t\t\n\t\t\t// 设置安全区域颜色\n\t\t\tthis.setSafeAreaStyle();\n\t\t},\n\t\tonShow: function() {\n\t\t\tconsole.log('App Show')\n\t\t\t\n\t\t\t// 每次显示应用时隐藏系统tabbar\n\t\t\tthis.hideTabBar();\n\t\t\t\n\t\t\t// 更新安全区域样式\n\t\t\tthis.setSafeAreaStyle();\n\t\t},\n\t\tonHide: function() {\n\t\t\tconsole.log('App Hide')\n\t\t},\n\t\tmethods: {\n\t\t\t// 检查登录状态\n\t\t\tcheckLoginStatus() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取存储的token\n\t\t\t\t\tconst token = uni.getStorageSync('token');\n\t\t\t\t\tconst userType = uni.getStorageSync('userType');\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('检查登录状态:', token ? '已登录' : '未登录');\n\t\t\t\t\t\n\t\t\t\t\t// 如果已登录且当前页面是登录页，则跳转到首页\n\t\t\t\t\tif (token && userType === 'store') {\n\t\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\t\tconst currentPage = pages.length > 0 ? pages[pages.length - 1].route : '';\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (currentPage === 'pages/login/login') {\n\t\t\t\t\t\t\tconsole.log('已登录，跳转到首页');\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: '/pages/dashboard/dashboard'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('检查登录状态失败', e);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 初始化TabBar\n\t\t\tinitTabBar() {\n\t\t\t\tconsole.log('初始化TabBar');\n\n\t\t\t\t// 由于使用自定义TabBar，不需要隐藏原生TabBar\n\t\t\t\t// 自定义TabBar通过组件控制显示/隐藏\n\t\t\t},\n\n\t\t\t// 获取安全区域信息并存储\n\t\t\tgetSafeAreaInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tconsole.log('系统信息:', systemInfo);\n\t\t\t\t\t\n\t\t\t\t\t// 存储安全区域信息\n\t\t\t\t\tif (systemInfo.safeArea) {\n\t\t\t\t\t\tconst { top, right, bottom, left, width, height } = systemInfo.safeArea;\n\t\t\t\t\t\tconst safeAreaInsets = {\n\t\t\t\t\t\t\ttop: top,\n\t\t\t\t\t\t\tright: systemInfo.screenWidth - right,\n\t\t\t\t\t\t\tbottom: systemInfo.screenHeight - bottom,\n\t\t\t\t\t\t\tleft: left\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存到全局存储\n\t\t\t\t\t\tuni.setStorageSync('safeAreaInsets', safeAreaInsets);\n\t\t\t\t\t\tconsole.log('安全区域信息:', safeAreaInsets);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 特别处理iOS底部安全区域\n\t\t\t\t\t\tif (systemInfo.platform === 'ios' && safeAreaInsets.bottom > 0) {\n\t\t\t\t\t\t\tconsole.log('iOS底部安全区域高度:', safeAreaInsets.bottom);\n\t\t\t\t\t\t\tuni.setStorageSync('isIPhoneX', true);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.setStorageSync('isIPhoneX', false);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取系统信息失败', e);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 设置安全区域颜色\n\t\t\tsetSafeAreaStyle() {\n\t\t\t\t// 检查是否为需要适配的设备\n\t\t\t\tconst isIPhoneX = uni.getStorageSync('isIPhoneX');\n\t\t\t\tif (!isIPhoneX) return;\n\t\t\t\t\n\t\t\t\t// 获取当前页面路径\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 使用uni-app API设置样式，而不是DOM API\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 添加自定义样式类到页面根元素\n\t\t\t\t\t\tconst currentPages = getCurrentPages();\n\t\t\t\t\t\tif (currentPages && currentPages.length > 0) {\n\t\t\t\t\t\t\tconst currentPage = currentPages[currentPages.length - 1];\n\t\t\t\t\t\t\t// 记录设置了安全区域样式\n\t\t\t\t\t\t\tconsole.log('已为页面设置安全区域样式');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用样式变量而不是直接操作DOM\n\t\t\t\t\t\tuni.setStorageSync('safeAreaBgColor', 'rgba(30, 30, 30, 0.95)');\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('安全区域样式已更新: 统一导航栏颜色');\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('设置安全区域样式失败', e);\n\t\t\t\t\t}\n\t\t\t\t}, 300);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 引入通用样式和图标 */\n\t@import './static/css/common.css';\n\t@import './static/css/iconfont.css';\n\t@import './static/css/material-icons.css';\n\t@import './static/css/safe-area.css';\n\t@import './static/css/safe-area-colors.css';\n\t\n\t/* iOS安全区域适配 */\n\t.safe-area-inset-bottom {\n\t\tpadding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */\n\t\tpadding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */\n\t}\n\t\n\t.safe-area-inset-top {\n\t\tpadding-top: constant(safe-area-inset-top); /* iOS 11.0 */\n\t\tpadding-top: env(safe-area-inset-top); /* iOS 11.2+ */\n\t}\n\t\n\t/* 防止页面闪烁 */\n\tpage {\n\t\tbackground-color: #121212;\n\t}\n\t\n\t/* 容器通用样式，确保内容不被底部安全区域遮挡 */\n\t.container {\n\t\tpadding-bottom: calc(110rpx + constant(safe-area-inset-bottom)) !important; /* iOS 11.0 */\n\t\tpadding-bottom: calc(110rpx + env(safe-area-inset-bottom)) !important; /* iOS 11.2+ */\n\t}\n\t\n\t/* 安全区域颜色变量 */\n\t:root {\n\t\t--safe-area-bg-color: rgba(30, 30, 30, 0.95);\n\t}\n</style>\n", "import mod from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753035246142\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}