#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
今夜城堡 - 合作商角色API自动测试脚本
测试用户: partner_admin
密码: 123456
"""

import requests
import json
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('partner_api_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class PartnerAPITester:
    def __init__(self, base_url="https://api.jycb888.com"):
        self.base_url = base_url
        self.token = None
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        self.partner_id = None
        self.shop_id = None
        self.device_id = None
        self.bank_card_id = None
        
    def log_test(self, test_name, success, response_data=None, error=None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        logging.info(f"{status} {test_name}")
        if error:
            logging.error(f"Error: {error}")
        if response_data and isinstance(response_data, dict):
            logging.info(f"Response: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
    
    def make_request(self, method, endpoint, data=None, params=None):
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, params=params)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=self.headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return True, response.json()
        except requests.exceptions.RequestException as e:
            return False, str(e)
        except json.JSONDecodeError as e:
            return False, f"JSON decode error: {e}"
    
    def test_auth_login(self):
        """测试1.1 管理员登录"""
        data = {
            "username": "partner_admin",
            "password": "123456"
        }
        success, response = self.make_request('POST', '/auth/admin/login', data)
        
        if success and response.get('code') == 200:
            self.token = response['data']['token']
            self.partner_id = response['data'].get('partnerId')
            self.headers['Authorization'] = f"{self.token}"
            self.log_test("管理员登录", True, response)
            return True
        else:
            self.log_test("管理员登录", False, error=response)
            return False
    
    def test_auth_info(self):
        """测试1.2 获取用户信息"""
        success, response = self.make_request('GET', '/auth/admin/info')
        self.log_test("获取用户信息", success and response.get('code') == 200, response)
        return success
    
    def test_auth_profile(self):
        """测试1.3 获取当前用户信息"""
        success, response = self.make_request('GET', '/auth/profile')
        self.log_test("获取当前用户信息", success and response.get('code') == 200, response)
        return success
    
    def test_auth_permissions(self):
        """测试1.4 获取权限列表"""
        success, response = self.make_request('GET', '/auth/permissions')
        self.log_test("获取权限列表", success and response.get('code') == 200, response)
        return success
    
    def test_auth_menus(self):
        """测试1.5 获取菜单列表"""
        success, response = self.make_request('GET', '/auth/menus')
        self.log_test("获取菜单列表", success and response.get('code') == 200, response)
        return success
    
    def test_auth_verify_password(self):
        """测试1.6 验证密码"""
        # 使用表单数据而不是JSON
        import requests
        url = f"{self.base_url}/auth/verify-password"
        headers = self.headers.copy()
        headers['Content-Type'] = 'application/x-www-form-urlencoded'

        try:
            response = requests.post(url, headers=headers, data={'password': '123456'})
            response.raise_for_status()
            result = response.json()
            success = result.get('code') == 200
            self.log_test("验证密码", success, result)
            return success
        except Exception as e:
            self.log_test("验证密码", False, error=str(e))
            return False
    
    def test_partner_info(self):
        """测试2.1 获取合作商详情"""
        success, response = self.make_request('GET', '/partner/info')
        self.log_test("获取合作商详情", success and response.get('code') == 200, response)
        return success
    
    def test_partner_update_info(self):
        """测试2.2 更新合作商基本信息"""
        # 不包含entityId，系统会自动从用户上下文获取
        data = {
            "partnerName": "测试合作商",
            "contactName": "测试联系人",
            "contactPhone": "***********",
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区",
            "address": "朝阳区测试地址",
            "remark": "API测试更新"
        }
        success, response = self.make_request('PUT', '/partner/info', data)
        self.log_test("更新合作商基本信息", success and response.get('code') == 200, response)
        return success
    
    def test_partner_statistics(self):
        """测试2.5 获取合作商统计数据"""
        success, response = self.make_request('GET', '/partner/statistics')
        self.log_test("获取合作商统计数据", success and response.get('code') == 200, response)
        return success
    
    def test_shop_page(self):
        """测试3.1 获取门店分页列表"""
        params = {
            'pageNum': 1,
            'pageSize': 10,
            'status': 1
        }
        success, response = self.make_request('GET', '/partner/shop/page', params=params)
        
        if success and response.get('code') == 200 and response.get('data', {}).get('list'):
            # 获取第一个门店ID用于后续测试
            self.shop_id = response['data']['list'][0]['id']
        
        self.log_test("获取门店分页列表", success and response.get('code') == 200, response)
        return success
    
    def test_shop_list(self):
        """测试3.2 获取门店列表"""
        params = {
            'pageNum': 1,
            'pageSize': 10
        }
        success, response = self.make_request('GET', '/partner/shop/list', params=params)
        self.log_test("获取门店列表", success and response.get('code') == 200, response)
        return success
    
    def test_shop_detail(self):
        """测试3.3 获取门店详情"""
        if not self.shop_id:
            self.log_test("获取门店详情", False, error="没有可用的门店ID")
            return False
        
        success, response = self.make_request('GET', f'/partner/shop/{self.shop_id}')
        self.log_test("获取门店详情", success and response.get('code') == 200, response)
        return success
    
    def test_shop_create(self):
        """测试3.4 创建门店"""
        # 包含必要的字段，系统会自动从用户上下文获取partnerId
        data = {
            "entityId": 2,  # 添加业务主体ID
            "partnerId": 1,  # 添加合作商ID
            "name": "API测试门店",
            "code": f"TEST{int(time.time())}",
            "contactPerson": "测试联系人",
            "contactPhone": "13800138001",
            "province": "北京市",
            "city": "北京市",
            "district": "海淀区",
            "address": "海淀区测试地址",
            "longitude": 116.297128,
            "latitude": 39.916527,
            "revenueRatio": 70.00,
            "status": 1,  # 添加状态字段
            "remark": "API测试创建的门店",
            "adminInfo": {
                "username": f"test_shop_{int(time.time())}",
                "password": "123456",
                "realName": "测试管理员",
                "mobile": "13800138001",
                "email": "<EMAIL>"
            }
        }
        success, response = self.make_request('POST', '/partner/shop', data)

        if success and response.get('code') == 200:
            # 保存新创建的门店ID
            if 'data' in response and 'id' in response['data']:
                self.shop_id = response['data']['id']

        self.log_test("创建门店", success and response.get('code') == 200, response)
        return success
    
    def test_shop_statistics(self):
        """测试3.8 获取门店统计数据"""
        success, response = self.make_request('GET', '/partner/shop/statistics')
        self.log_test("获取门店统计数据", success and response.get('code') == 200, response)
        return success
    
    def test_shop_overview(self):
        """测试3.9 获取门店营业概况"""
        success, response = self.make_request('GET', '/partner/shop/overview')
        self.log_test("获取门店营业概况", success and response.get('code') == 200, response)
        return success
    
    def run_auth_tests(self):
        """运行认证相关测试"""
        logging.info("=" * 50)
        logging.info("开始测试认证登录API")
        logging.info("=" * 50)
        
        tests = [
            self.test_auth_login,
            self.test_auth_info,
            self.test_auth_profile,
            self.test_auth_permissions,
            self.test_auth_menus,
            self.test_auth_verify_password
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                time.sleep(0.5)  # 避免请求过快
            except Exception as e:
                logging.error(f"测试执行异常: {e}")
                results.append(False)
        
        return results
    
    def run_partner_tests(self):
        """运行合作商信息管理测试"""
        logging.info("=" * 50)
        logging.info("开始测试合作商信息管理API")
        logging.info("=" * 50)
        
        tests = [
            self.test_partner_info,
            self.test_partner_update_info,
            self.test_partner_statistics
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                time.sleep(0.5)
            except Exception as e:
                logging.error(f"测试执行异常: {e}")
                results.append(False)
        
        return results
    
    def run_shop_tests(self):
        """运行门店管理测试"""
        logging.info("=" * 50)
        logging.info("开始测试门店管理API")
        logging.info("=" * 50)

        tests = [
            self.test_shop_page,
            self.test_shop_list,
            self.test_shop_detail,
            self.test_shop_create,
            self.test_shop_statistics,
            self.test_shop_overview
        ]

        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                time.sleep(0.5)
            except Exception as e:
                logging.error(f"测试执行异常: {e}")
                results.append(False)

        return results

    def test_device_list(self):
        """测试4.1 获取设备列表"""
        params = {
            'pageNum': 1,
            'pageSize': 10,
            'status': 1
        }
        success, response = self.make_request('GET', '/partner/device/list', params=params)

        if success and response.get('code') == 200 and response.get('data', {}).get('list'):
            # 获取第一个设备ID用于后续测试
            self.device_id = response['data']['list'][0]['id']

        self.log_test("获取设备列表", success and response.get('code') == 200, response)
        return success

    def test_device_detail(self):
        """测试4.2 获取设备详情"""
        if not self.device_id:
            self.log_test("获取设备详情", False, error="没有可用的设备ID")
            return False

        success, response = self.make_request('GET', f'/partner/device/{self.device_id}')
        self.log_test("获取设备详情", success and response.get('code') == 200, response)
        return success

    def test_device_create(self):
        """测试4.3 添加设备"""
        data = {
            "deviceName": "API测试设备",
            "deviceType": 1,
            "macAddress": f"AA:BB:CC:DD:EE:{int(time.time()) % 100:02d}",
            "shopId": self.shop_id if self.shop_id else 1,
            "latitude": 39.916527,
            "longitude": 116.397128,
            "address": "测试地址",
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区",
            "roomNumber": "TEST001",
            "maxUsers": 1,
            "remark": "API测试设备"
        }
        success, response = self.make_request('POST', '/partner/device', data)

        if success and response.get('code') == 200:
            if 'data' in response and 'id' in response['data']:
                self.device_id = response['data']['id']

        self.log_test("添加设备", success and response.get('code') == 200, response)
        return success

    def test_device_statistics(self):
        """测试4.8 获取设备统计"""
        success, response = self.make_request('GET', '/partner/device/statistics')
        self.log_test("获取设备统计", success and response.get('code') == 200, response)
        return success

    def test_device_bind(self):
        """测试4.6 绑定设备"""
        if not self.shop_id:
            self.log_test("绑定设备", False, error="没有可用的门店ID")
            return False

        data = {
            "macAddress": "AB:CD:EF:12:34:56",  # 使用数据库中实际存在的MAC地址
            "shopId": self.shop_id,
            "roomNumber": "TEST002"
        }
        success, response = self.make_request('POST', '/partner/device/bind', data)
        self.log_test("绑定设备", success and response.get('code') == 200, response)
        return success

    def test_finance_statistics(self):
        """测试5.1 获取财务统计信息"""
        success, response = self.make_request('GET', '/partner/finance/statistics')
        self.log_test("获取财务统计信息", success and response.get('code') == 200, response)
        return success

    def test_finance_account(self):
        """测试5.3 获取财务账户信息"""
        success, response = self.make_request('GET', '/partner/finance/account')
        self.log_test("获取财务账户信息", success and response.get('code') == 200, response)
        return success

    def test_finance_logs(self):
        """测试5.4 获取财务流水列表"""
        params = {
            'pageNum': 1,
            'pageSize': 10
        }
        success, response = self.make_request('GET', '/partner/finance/logs', params=params)
        self.log_test("获取财务流水列表", success and response.get('code') == 200, response)
        return success

    def test_finance_withdraw_records(self):
        """测试5.7 获取提现记录"""
        params = {
            'pageNum': 1,
            'pageSize': 10
        }
        success, response = self.make_request('GET', '/partner/finance/withdraw-records', params=params)
        self.log_test("获取提现记录", success and response.get('code') == 200, response)
        return success

    def test_finance_bank_cards(self):
        """测试5.9.1 获取银行卡列表"""
        success, response = self.make_request('GET', '/partner/finance/bank-cards')

        if success and response.get('code') == 200 and response.get('data'):
            # 获取第一个银行卡ID用于后续测试
            if isinstance(response['data'], list) and len(response['data']) > 0:
                self.bank_card_id = response['data'][0]['id']

        self.log_test("获取银行卡列表", success and response.get('code') == 200, response)
        return success

    def test_finance_add_bank_card(self):
        """测试5.9.2 添加银行卡"""
        data = {
            "bankName": "测试银行",
            "cardNumber": "****************",
            "cardHolderName": "测试用户",
            "idCard": "110101199001011234",
            "mobile": "***********",
            "isDefault": 0,  # 使用整数而不是布尔值
            "remark": "API测试银行卡"
        }
        success, response = self.make_request('POST', '/partner/finance/bank-card', data)

        if success and response.get('code') == 200:
            if 'data' in response and 'id' in response['data']:
                self.bank_card_id = response['data']['id']

        self.log_test("添加银行卡", success and response.get('code') == 200, response)
        return success

    def test_finance_withdrawable_balance(self):
        """测试5.10.2 获取可提现余额"""
        success, response = self.make_request('GET', '/partner/finance/withdrawable-balance')
        self.log_test("获取可提现余额", success and response.get('code') == 200, response)
        return success

    def test_order_list(self):
        """测试7.1 获取订单列表"""
        params = {
            'pageNum': 1,
            'pageSize': 10
        }
        success, response = self.make_request('GET', '/partner/order/list', params=params)
        self.log_test("获取订单列表", success and response.get('code') == 200, response)
        return success

    def test_order_statistics(self):
        """测试7.3 获取订单统计"""
        # 根据后端代码，订单统计需要必需的时间参数
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)  # 最近30天

        params = {
            'startTime': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'endTime': end_time.strftime('%Y-%m-%d %H:%M:%S')
        }
        success, response = self.make_request('GET', '/partner/order/statistics', params=params)
        self.log_test("获取订单统计", success and response.get('code') == 200, response)
        return success

    def run_device_tests(self):
        """运行设备管理测试"""
        logging.info("=" * 50)
        logging.info("开始测试设备管理API")
        logging.info("=" * 50)

        tests = [
            self.test_device_list,
            self.test_device_detail,
            self.test_device_create,
            self.test_device_statistics,
            self.test_device_bind,
            # 设备费用管理测试
            self.test_device_fee_list,
            self.test_device_fee_create,
            self.test_device_fee_detail,
            self.test_device_fee_update,
            self.test_device_fee_set_default,
            self.test_device_fee_delete
        ]

        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                time.sleep(0.5)
            except Exception as e:
                logging.error(f"测试执行异常: {e}")
                results.append(False)

        return results

    def run_finance_tests(self):
        """运行财务管理测试"""
        logging.info("=" * 50)
        logging.info("开始测试财务管理API")
        logging.info("=" * 50)

        tests = [
            self.test_finance_statistics,
            self.test_finance_account,
            self.test_finance_logs,
            self.test_finance_withdraw_records,
            self.test_finance_bank_cards,
            self.test_finance_add_bank_card,
            self.test_finance_withdrawable_balance
        ]

        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                time.sleep(0.5)
            except Exception as e:
                logging.error(f"测试执行异常: {e}")
                results.append(False)

        return results

    def run_order_tests(self):
        """运行订单管理测试"""
        logging.info("=" * 50)
        logging.info("开始测试订单管理API")
        logging.info("=" * 50)

        tests = [
            self.test_order_list,
            self.test_order_statistics
        ]

        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                time.sleep(0.5)
            except Exception as e:
                logging.error(f"测试执行异常: {e}")
                results.append(False)

        return results

    # ==================== 设备费用管理测试 ====================

    def test_device_fee_list(self):
        """测试获取设备费用列表"""
        params = {
            "pageNum": 1,
            "pageSize": 10
        }
        success, response = self.make_request('GET', '/partner/device/fee', params=params)
        self.log_test("获取设备费用列表", success and response.get('code') == 200, response)

        # 保存费用ID用于后续测试
        if success and response.get('code') == 200 and response.get('data', {}).get('list'):
            self.device_fee_id = response['data']['list'][0]['id']

        return success

    def test_device_fee_create(self):
        """测试创建设备费用配置"""
        if not self.device_id:
            self.log_test("创建设备费用配置", False, "Error: 没有可用的设备ID")
            return False

        data = {
            "feeName": "API测试费用配置",
            "feeType": 1,  # 按时间计费
            "deviceId": self.device_id,
            "price": 10.00,
            "unit": "分钟",
            "minTime": 5,
            "maxTime": 120,
            "discountType": 0,  # 无折扣
            "status": 1,  # 启用
            "isDefault": 0,  # 非默认
            "remark": "API测试创建的费用配置"
        }
        success, response = self.make_request('POST', '/partner/device/fee', data)
        self.log_test("创建设备费用配置", success and response.get('code') == 200, response)

        # 保存创建的费用ID
        if success and response.get('code') == 200:
            self.device_fee_id = response['data']['id']

        return success

    def test_device_fee_detail(self):
        """测试获取设备费用详情"""
        if not hasattr(self, 'device_fee_id') or not self.device_fee_id:
            self.log_test("获取设备费用详情", False, "Error: 没有可用的费用ID")
            return False

        success, response = self.make_request('GET', f'/partner/device/fee/{self.device_fee_id}')
        self.log_test("获取设备费用详情", success and response.get('code') == 200, response)
        return success

    def test_device_fee_update(self):
        """测试更新设备费用配置"""
        if not hasattr(self, 'device_fee_id') or not self.device_fee_id:
            self.log_test("更新设备费用配置", False, "Error: 没有可用的费用ID")
            return False

        data = {
            "feeName": "API测试费用配置（已更新）",
            "feeType": 1,
            "deviceId": self.device_id,
            "price": 15.00,  # 更新价格
            "unit": "分钟",
            "minTime": 10,  # 更新最小时间
            "maxTime": 180,  # 更新最大时间
            "discountType": 1,  # 百分比折扣
            "discountValue": 0.9,  # 9折
            "discountCondition": 50.00,  # 满50元享受折扣
            "status": 1,
            "isDefault": 0,
            "remark": "API测试更新的费用配置"
        }
        success, response = self.make_request('PUT', f'/partner/device/fee/{self.device_fee_id}', data)
        self.log_test("更新设备费用配置", success and response.get('code') == 200, response)
        return success

    def test_device_fee_set_default(self):
        """测试设置默认费用配置"""
        if not hasattr(self, 'device_fee_id') or not self.device_fee_id:
            self.log_test("设置默认费用配置", False, "Error: 没有可用的费用ID")
            return False

        success, response = self.make_request('PUT', f'/partner/device/fee/{self.device_fee_id}/default')
        self.log_test("设置默认费用配置", success and response.get('code') == 200, response)
        return success

    def test_device_fee_delete(self):
        """测试删除设备费用配置"""
        if not hasattr(self, 'device_fee_id') or not self.device_fee_id:
            self.log_test("删除设备费用配置", False, "Error: 没有可用的费用ID")
            return False

        success, response = self.make_request('DELETE', f'/partner/device/fee/{self.device_fee_id}')
        self.log_test("删除设备费用配置", success and response.get('code') == 200, response)
        return success

def main():
    """主函数"""
    print("🚀 开始合作商角色API自动测试")
    print("=" * 60)
    print("测试用户: partner_admin")
    print("测试密码: 123456")
    print("=" * 60)

    # 创建测试实例
    tester = PartnerAPITester()

    # 运行测试
    auth_results = tester.run_auth_tests()

    if not auth_results[0]:  # 如果登录失败，停止测试
        print("❌ 登录失败，停止测试")
        return

    partner_results = tester.run_partner_tests()
    shop_results = tester.run_shop_tests()
    device_results = tester.run_device_tests()
    finance_results = tester.run_finance_tests()
    order_results = tester.run_order_tests()

    # 统计结果
    all_results = [auth_results, partner_results, shop_results, device_results, finance_results, order_results]
    total_tests = sum(len(results) for results in all_results)
    passed_tests = sum(sum(results) for results in all_results)

    print("\n" + "=" * 60)
    print("📊 测试结果统计")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过数: {passed_tests}")
    print(f"失败数: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")

    print(f"\n📋 各模块测试结果:")
    print(f"🔐 认证模块: {sum(auth_results)}/{len(auth_results)} 通过")
    print(f"👤 合作商模块: {sum(partner_results)}/{len(partner_results)} 通过")
    print(f"🏪 门店模块: {sum(shop_results)}/{len(shop_results)} 通过")
    print(f"📱 设备模块: {sum(device_results)}/{len(device_results)} 通过")
    print(f"💰 财务模块: {sum(finance_results)}/{len(finance_results)} 通过")
    print(f"📋 订单模块: {sum(order_results)}/{len(order_results)} 通过")

    print("\n📝 详细日志请查看 partner_api_test.log 文件")

    # 生成测试报告
    generate_test_report(tester, all_results)

def generate_test_report(tester, all_results):
    """生成测试报告"""
    report_file = f"partner_api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>合作商API测试报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
            .summary {{ margin: 20px 0; }}
            .module {{ margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }}
            .module-header {{ background-color: #e9ecef; padding: 10px; font-weight: bold; }}
            .test-item {{ padding: 10px; border-bottom: 1px solid #eee; }}
            .pass {{ color: green; }}
            .fail {{ color: red; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🏰 今夜城堡 - 合作商角色API测试报告</h1>
            <p>测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>测试用户: partner_admin</p>
            <p>服务器地址: {tester.base_url}</p>
        </div>

        <div class="summary">
            <h2>📊 测试概要</h2>
            <p>总测试数: {sum(len(results) for results in all_results)}</p>
            <p>通过数: {sum(sum(results) for results in all_results)}</p>
            <p>失败数: {sum(len(results) for results in all_results) - sum(sum(results) for results in all_results)}</p>
            <p>通过率: {sum(sum(results) for results in all_results)/sum(len(results) for results in all_results)*100:.1f}%</p>
        </div>

        <div class="modules">
            <h2>📋 详细结果</h2>
            <!-- 这里可以添加更详细的测试结果 -->
        </div>
    </body>
    </html>
    """

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"\n📄 测试报告已生成: {report_file}")

if __name__ == "__main__":
    main()
