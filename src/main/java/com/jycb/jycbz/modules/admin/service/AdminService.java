package com.jycb.jycbz.modules.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jycb.jycbz.modules.admin.dto.AdminCreateDTO;
import com.jycb.jycbz.modules.admin.dto.AdminPasswordDTO;
import com.jycb.jycbz.modules.admin.dto.AdminQueryDTO;
import com.jycb.jycbz.modules.admin.dto.AdminUpdateDTO;
import com.jycb.jycbz.modules.admin.entity.Admin;
import com.jycb.jycbz.modules.admin.vo.AdminVO;
import com.jycb.jycbz.modules.system.vo.RoleVO;

import java.util.List;

/**
 * 管理员服务接口
 */
public interface AdminService extends IService<Admin> {

    /**
     * 分页查询管理员列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<AdminVO> getAdminPage(AdminQueryDTO queryDTO);

    /**
     * 根据用户名获取管理员信息
     *
     * @param username 用户名
     * @return 管理员信息
     */
    Admin getByUsername(String username);

    /**
     * 获取管理员详细信息
     *
     * @param adminId 管理员ID
     * @return 管理员详细信息
     */
    AdminVO getAdminDetail(Long adminId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean checkUsernameExists(String username);

    /**
     * 检查手机号是否存在
     *
     * @param mobile 手机号
     * @param excludeId 排除的管理员ID
     * @return 是否存在
     */
    boolean checkMobileExists(String mobile, Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的管理员ID
     * @return 是否存在
     */
    boolean checkEmailExists(String email, Long excludeId);

    /**
     * 创建管理员
     *
     * @param createDTO 创建请求
     * @return 是否成功
     */
    boolean createAdmin(AdminCreateDTO createDTO);

    /**
     * 更新管理员信息
     *
     * @param updateDTO 更新请求
     * @return 是否成功
     */
    boolean updateAdmin(AdminUpdateDTO updateDTO);

    /**
     * 修改密码
     *
     * @param passwordDTO 密码修改请求
     * @return 是否成功
     */
    boolean updatePassword(AdminPasswordDTO passwordDTO);

    /**
     * 重置密码
     *
     * @param adminId 管理员ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(Long adminId, String newPassword);

    /**
     * 启用或禁用管理员
     *
     * @param adminId 管理员ID
     * @param status 状态：1-启用 0-禁用
     * @return 是否成功
     */
    boolean updateStatus(Long adminId, Integer status);

    /**
     * 删除管理员（逻辑删除）
     *
     * @param adminId 管理员ID
     * @return 是否成功
     */
    boolean deleteAdmin(Long adminId);

    /**
     * 批量删除管理员
     *
     * @param adminIds 管理员ID列表
     * @return 是否成功
     */
    boolean batchDeleteAdmin(List<Long> adminIds);

    /**
     * 验证密码
     *
     * @param admin 管理员信息
     * @param password 密码
     * @return 是否匹配
     */
    boolean verifyPassword(Admin admin, String password);

    /**
     * 加密密码
     *
     * @param password 原始密码
     * @return 加密后的密码
     */
    String encryptPassword(String password);

    /**
     * 获取当前登录的管理员信息
     *
     * @return 当前登录的管理员信息
     */
    Admin getCurrentAdmin();

    /**
     * 更新登录信息
     *
     * @param adminId 管理员ID
     * @param loginIp 登录IP
     * @return 是否成功
     */
    boolean updateLoginInfo(Long adminId, String loginIp);

    /**
     * 根据角色ID查询管理员列表
     *
     * @param roleId 角色ID
     * @return 管理员列表
     */
    List<AdminVO> getAdminsByRoleId(Long roleId);

    /**
     * 根据业务主体ID查询管理员列表
     *
     * @param entityId 业务主体ID
     * @return 管理员列表
     */
    List<AdminVO> getAdminsByEntityId(Long entityId);

    /**
     * 根据合作商ID查询管理员列表
     *
     * @param partnerId 合作商ID
     * @return 管理员列表
     */
    List<AdminVO> getAdminsByPartnerId(Long partnerId);

    /**
     * 根据门店ID查询管理员列表
     *
     * @param shopId 门店ID
     * @return 管理员列表
     */
    List<AdminVO> getAdminsByShopId(Long shopId);

    /**
     * 检查管理员是否有权限管理指定的下级管理员
     *
     * @param currentAdminId 当前管理员ID
     * @param targetAdminId 目标管理员ID
     * @return 是否有权限
     */
    boolean hasPermissionToManage(Long currentAdminId, Long targetAdminId);

    /**
     * 为管理员分配角色
     *
     * @param adminId 管理员ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean assignRole(Long adminId, Long roleId);

    /**
     * 移除管理员角色
     *
     * @param adminId 管理员ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean removeRole(Long adminId, Long roleId);

    /**
     * 获取管理员的角色列表
     *
     * @param adminId 管理员ID
     * @return 角色列表
     */
    List<RoleVO> getAdminRoles(Long adminId);

    /**
     * 获取管理员的权限列表
     *
     * @param adminId 管理员ID
     * @return 权限列表
     */
    List<String> getAdminPermissions(Long adminId);

}