package com.jycb.jycbz.modules.finance.service.impl;

import com.jycb.jycbz.common.annotation.FinanceValidation;
import com.jycb.jycbz.common.aspect.FinanceValidationAspect;
import com.jycb.jycbz.modules.finance.entity.FinanceAccount;
import com.jycb.jycbz.modules.finance.service.FinanceAccountService;
import com.jycb.jycbz.modules.finance.service.FinanceDataValidationService;
import com.jycb.jycbz.modules.finance.service.FinanceRealTimeValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 财务实时验证服务实现类
 */
@Slf4j
@Service
public class FinanceRealTimeValidationServiceImpl implements FinanceRealTimeValidationService {

    private final FinanceDataValidationService financeDataValidationService;

    // 使用懒加载避免循环依赖
    @Lazy
    @Autowired
    private FinanceAccountService financeAccountService;

    public FinanceRealTimeValidationServiceImpl(FinanceDataValidationService financeDataValidationService) {
        this.financeDataValidationService = financeDataValidationService;
    }

    @Override
    public boolean validateFinanceOperation(FinanceValidationAspect.ValidationContext context, 
                                          FinanceValidation.ValidationType[] validationTypes) {
        boolean overallResult = true;
        
        for (FinanceValidation.ValidationType type : validationTypes) {
            boolean result = false;
            String details = "";
            
            try {
                switch (type) {
                    case BALANCE_CONSISTENCY:
                        result = validateAccountBalance(context.getAccountId());
                        details = "余额一致性验证";
                        break;
                        
                    case TRANSACTION_INTEGRITY:
                        result = validateTransactionIntegrity(context.getAccountId());
                        details = "交易记录完整性验证";
                        break;
                        
                    case ACCOUNT_STATUS:
                        result = validateAccountStatus(context.getAccountId());
                        details = "账户状态验证";
                        break;
                        
                    case COMMISSION_CALCULATION:
                        result = true; // 暂时默认通过，后续可以实现具体逻辑
                        details = "分成计算验证";
                        break;
                        
                    case COMPREHENSIVE:
                        result = comprehensiveValidation(context.getAccountId());
                        details = "全面验证";
                        break;
                        
                    default:
                        result = true;
                        details = "未知验证类型";
                        break;
                }
                
            } catch (Exception e) {
                log.error("验证类型 {} 执行失败", type, e);
                result = false;
                details = "验证执行异常: " + e.getMessage();
            }
            
            // 记录验证结果
            recordValidationResult(context, type, result, details);
            
            // 如果验证失败，发送告警
            if (!result) {
                sendValidationFailureAlert(context, type, details);
                overallResult = false;
            }
        }
        
        return overallResult;
    }

    @Override
    public boolean validateAccountBalance(Long accountId) {
        if (accountId == null) {
            return true; // 如果没有账户ID，跳过验证
        }
        
        try {
            Map<String, Object> result = financeAccountService.validateAccountBalance(accountId);
            return (Boolean) result.getOrDefault("valid", false);
        } catch (Exception e) {
            log.error("账户余额验证失败，账户ID: {}", accountId, e);
            return false;
        }
    }

    @Override
    public boolean validateTransactionIntegrity(Long accountId) {
        if (accountId == null) {
            return true;
        }
        
        try {
            // 检查最近的交易记录是否完整
            // 这里可以实现具体的交易完整性检查逻辑
            return true;
        } catch (Exception e) {
            log.error("交易记录完整性验证失败，账户ID: {}", accountId, e);
            return false;
        }
    }

    @Override
    public boolean validateAccountStatus(Long accountId) {
        if (accountId == null) {
            return true;
        }
        
        try {
            FinanceAccount account = financeAccountService.getById(accountId);
            if (account == null) {
                return false;
            }
            
            // 检查账户状态是否正常
            if (account.getStatus() == null || account.getStatus() != 1) {
                return false;
            }
            
            // 检查余额是否为负数
            BigDecimal availableBalance = account.getAvailableBalance();
            if (availableBalance != null && availableBalance.compareTo(BigDecimal.ZERO) < 0) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("账户状态验证失败，账户ID: {}", accountId, e);
            return false;
        }
    }

    @Override
    public boolean validateCommissionCalculation(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return true;
        }
        
        try {
            // 这里可以实现分成计算验证逻辑
            // 比如验证分成金额是否正确，分成比例是否符合配置等
            return true;
        } catch (Exception e) {
            log.error("分成计算验证失败，订单ID: {}", orderId, e);
            return false;
        }
    }

    @Override
    public boolean comprehensiveValidation(Long accountId) {
        if (accountId == null) {
            return true;
        }
        
        try {
            // 执行全面验证
            boolean balanceValid = validateAccountBalance(accountId);
            boolean transactionValid = validateTransactionIntegrity(accountId);
            boolean statusValid = validateAccountStatus(accountId);
            
            return balanceValid && transactionValid && statusValid;
        } catch (Exception e) {
            log.error("全面验证失败，账户ID: {}", accountId, e);
            return false;
        }
    }

    @Override
    public void recordValidationResult(FinanceValidationAspect.ValidationContext context, 
                                     FinanceValidation.ValidationType validationType,
                                     boolean isValid, String details) {
        try {
            log.info("财务验证结果记录: 类={}, 方法={}, 账户ID={}, 验证类型={}, 结果={}, 详情={}", 
                    context.getClassName(), context.getMethodName(), context.getAccountId(),
                    validationType, isValid ? "通过" : "失败", details);
            
            // 这里可以将验证结果保存到数据库或发送到监控系统
            
        } catch (Exception e) {
            log.error("记录验证结果失败", e);
        }
    }

    @Override
    public void sendValidationFailureAlert(FinanceValidationAspect.ValidationContext context,
                                         FinanceValidation.ValidationType validationType,
                                         String details) {
        try {
            String alertMessage = String.format(
                "财务验证失败告警: 类=%s, 方法=%s, 账户ID=%s, 验证类型=%s, 详情=%s, 时间=%s",
                context.getClassName(), context.getMethodName(), context.getAccountId(),
                validationType, details, LocalDateTime.now()
            );
            
            log.warn(alertMessage);
            
            // 这里可以实现具体的告警逻辑，比如：
            // 1. 发送邮件
            // 2. 发送短信
            // 3. 推送到监控系统
            // 4. 记录到告警日志
            
        } catch (Exception e) {
            log.error("发送验证失败告警异常", e);
        }
    }
}
