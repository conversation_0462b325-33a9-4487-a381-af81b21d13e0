package com.jycb.jycbz.modules.finance.service.commission.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.finance.domain.Commission;
import com.jycb.jycbz.modules.finance.domain.factory.CommissionFactory;
import com.jycb.jycbz.modules.finance.dto.BalanceOperationDTO;
import com.jycb.jycbz.modules.finance.entity.CommissionConfig;
import com.jycb.jycbz.modules.finance.entity.CommissionDetail;
import com.jycb.jycbz.modules.finance.mapper.CommissionDetailMapper;
import com.jycb.jycbz.modules.finance.service.commission.CommissionConfigService;
import com.jycb.jycbz.modules.finance.service.commission.CommissionService;
import com.jycb.jycbz.modules.finance.service.core.AccountCoreService;
import com.jycb.jycbz.modules.finance.strategy.CommissionStrategyFactory;
import com.jycb.jycbz.modules.finance.vo.CommissionStatisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 佣金服务实现类
 */
@Service("commissionServiceImplCommission")
@Slf4j
public class CommissionServiceImpl implements CommissionService {

    @Autowired
    private CommissionDetailMapper commissionDetailMapper;
    
    @Autowired
    @Qualifier("commissionConfigServiceImplCommission")
    private CommissionConfigService commissionConfigService;
    
    @Autowired
    private AccountCoreService accountCoreService;
    
    @Autowired
    private CommissionStrategyFactory commissionStrategyFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCommission(String orderId, String orderNo, Long entityId, Long partnerId, 
                                  Long shopId, BigDecimal orderAmount) {
        try {
            // 获取分成配置
            CommissionConfig config = commissionConfigService.getEffectiveConfig("shop", shopId.intValue());
            if (config == null) {
                log.warn("未找到有效的分成配置: entityId={}, partnerId={}, shopId={}", entityId, partnerId, shopId);
                return false;
            }
            
            // 使用策略模式计算分成金额
            Map<String, BigDecimal> commissionResult = commissionStrategyFactory.getStrategy("standard")
                    .calculate(orderAmount, config);
            
            // 创建佣金领域对象
            Commission commission = Commission.builder()
                .orderId(orderId)
                .orderNo(orderNo)
                .entityId(entityId)
                .partnerId(partnerId)
                .shopId(shopId)
                .orderAmount(orderAmount)
                .platformCommission(commissionResult.get("platformAmount"))
                .entityCommission(commissionResult.get("entityAmount"))
                .partnerCommission(commissionResult.get("partnerAmount"))
                .shopCommission(commissionResult.get("shopAmount"))
                .status(0)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
            
            // 保存佣金记录并更新账户余额
            CommissionDetail entity = CommissionFactory.toEntity(commission);
            boolean saved = commissionDetailMapper.insert(entity) > 0;
            
            if (saved) {
                // 更新各方账户余额
                // 平台账户
                BalanceOperationDTO platformOp = new BalanceOperationDTO();
                platformOp.setAccountType("system");
                platformOp.setAccountId(1L);
                platformOp.setAmount(commission.getPlatformCommission());
                platformOp.setOrderId(orderId);
                platformOp.setDescription("订单分成收入");
                accountCoreService.increaseBalance(platformOp);
                
                // 业务主体账户
                BalanceOperationDTO entityOp = new BalanceOperationDTO();
                entityOp.setAccountType("entity");
                entityOp.setAccountId(entityId);
                entityOp.setAmount(commission.getEntityCommission());
                entityOp.setOrderId(orderId);
                entityOp.setDescription("订单分成收入");
                accountCoreService.increaseBalance(entityOp);
                
                // 合作商账户
                BalanceOperationDTO partnerOp = new BalanceOperationDTO();
                partnerOp.setAccountType("partner");
                partnerOp.setAccountId(partnerId);
                partnerOp.setAmount(commission.getPartnerCommission());
                partnerOp.setOrderId(orderId);
                partnerOp.setDescription("订单分成收入");
                accountCoreService.increaseBalance(partnerOp);
                
                // 门店账户
                BalanceOperationDTO shopOp = new BalanceOperationDTO();
                shopOp.setAccountType("shop");
                shopOp.setAccountId(shopId);
                shopOp.setAmount(commission.getShopCommission());
                shopOp.setOrderId(orderId);
                shopOp.setDescription("订单分成收入");
                accountCoreService.increaseBalance(shopOp);
            }
            
            return saved;
        } catch (Exception e) {
            log.error("创建佣金记录失败", e);
            throw new BusinessException("创建佣金记录失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, BigDecimal> calculateRefundCommission(String orderId, BigDecimal refundAmount) {
        // 查找原始佣金记录
        LambdaQueryWrapper<CommissionDetail> query = new LambdaQueryWrapper<>();
        query.eq(CommissionDetail::getOrderId, orderId);
        CommissionDetail detail = commissionDetailMapper.selectOne(query);
        
        if (detail == null) {
            log.warn("未找到订单佣金记录: orderId={}", orderId);
            return new HashMap<>();
        }
        
        // 计算退款比例
        BigDecimal refundRatio = refundAmount.divide(detail.getOrderAmount(), 4, BigDecimal.ROUND_HALF_UP);
        
        // 计算各方需要退回的佣金
        Map<String, BigDecimal> refundCommission = new HashMap<>();
        refundCommission.put("platformAmount", detail.getPlatformAmount().multiply(refundRatio).setScale(2, BigDecimal.ROUND_HALF_UP));
        refundCommission.put("entityAmount", detail.getEntityAmount().multiply(refundRatio).setScale(2, BigDecimal.ROUND_HALF_UP));
        refundCommission.put("partnerAmount", detail.getPartnerAmount().multiply(refundRatio).setScale(2, BigDecimal.ROUND_HALF_UP));
        refundCommission.put("shopAmount", detail.getShopAmount().multiply(refundRatio).setScale(2, BigDecimal.ROUND_HALF_UP));
        
        return refundCommission;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markCommissionSettled(Long id) {
        CommissionDetail detail = commissionDetailMapper.selectById(id.intValue());
        if (detail == null) {
            return false;
        }
        detail.setSettlementStatus(1);
        detail.setSettlementTime(LocalDateTime.now());
        detail.setUpdateTime(LocalDateTime.now());
        return commissionDetailMapper.updateById(detail) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchMarkCommissionSettled(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        List<Integer> intIds = ids.stream().map(Long::intValue).collect(Collectors.toList());
        int count = 0;
        
        for (Integer id : intIds) {
            CommissionDetail detail = commissionDetailMapper.selectById(id);
            if (detail != null) {
                detail.setSettlementStatus(1);
                detail.setSettlementTime(LocalDateTime.now());
                detail.setUpdateTime(LocalDateTime.now());
                count += commissionDetailMapper.updateById(detail);
            }
        }
        
        return count > 0;
    }

    @Override
    public BigDecimal getUnsettledAmount(String accountType, Long accountId) {
        LambdaQueryWrapper<CommissionDetail> query = new LambdaQueryWrapper<>();
        
        // 根据账户类型设置查询条件
        if ("entity".equals(accountType)) {
            query.eq(CommissionDetail::getEntityId, accountId.intValue());
        } else if ("partner".equals(accountType)) {
            query.eq(CommissionDetail::getPartnerId, accountId.intValue());
        } else if ("shop".equals(accountType)) {
            query.eq(CommissionDetail::getShopId, accountId.intValue());
        }
        
        query.eq(CommissionDetail::getSettlementStatus, 0);
        
        // 查询未结算佣金
        List<CommissionDetail> details = commissionDetailMapper.selectList(query);
        
        // 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CommissionDetail detail : details) {
            if ("entity".equals(accountType)) {
                totalAmount = totalAmount.add(detail.getEntityAmount());
            } else if ("partner".equals(accountType)) {
                totalAmount = totalAmount.add(detail.getPartnerAmount());
            } else if ("shop".equals(accountType)) {
                totalAmount = totalAmount.add(detail.getShopAmount());
            }
        }
        
        return totalAmount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal settleCommission(String accountType, Long accountId, BigDecimal minAmount) {
        // 获取未结算的分成金额
        BigDecimal unsettledAmount = getUnsettledAmount(accountType, accountId);
        if (unsettledAmount == null || unsettledAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 如果未结算金额小于最小结算金额，则不结算
        if (minAmount != null && unsettledAmount.compareTo(minAmount) < 0) {
            return BigDecimal.ZERO;
        }

        // 更新结算状态
        LambdaQueryWrapper<CommissionDetail> query = new LambdaQueryWrapper<>();
        
        // 根据账户类型设置查询条件
        if ("entity".equals(accountType)) {
            query.eq(CommissionDetail::getEntityId, accountId.intValue());
        } else if ("partner".equals(accountType)) {
            query.eq(CommissionDetail::getPartnerId, accountId.intValue());
        } else if ("shop".equals(accountType)) {
            query.eq(CommissionDetail::getShopId, accountId.intValue());
        }
        
        query.eq(CommissionDetail::getSettlementStatus, 0);
        
        // 查询未结算佣金记录
        List<CommissionDetail> details = commissionDetailMapper.selectList(query);
        
        // 更新结算状态
        for (CommissionDetail detail : details) {
            detail.setSettlementStatus(1);
            detail.setSettlementTime(LocalDateTime.now());
            detail.setUpdateTime(LocalDateTime.now());
            commissionDetailMapper.updateById(detail);
        }

        // 更新账户最后结算时间
        accountCoreService.updateLastSettlementTime(accountType, accountId);
        
        return unsettledAmount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSettleCommission(String accountType, List<Long> accountIds) {
        if (accountIds == null || accountIds.isEmpty()) {
            return false;
        }
        
        boolean success = false;
        
        // 批量处理结算
        for (Long accountId : accountIds) {
            BigDecimal settled = settleCommission(accountType, accountId, null);
            if (settled.compareTo(BigDecimal.ZERO) > 0) {
                success = true;
            }
        }
        
        return success;
    }

    @Override
    public PageResult<Commission> getCommissionPage(String accountType, Long accountId, Integer status, 
                                                 LocalDate startDate, LocalDate endDate, int pageNum, int pageSize) {
        // 查询佣金明细
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.plusDays(1).atStartOfDay() : null;
        
        Page<CommissionDetail> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CommissionDetail> query = new LambdaQueryWrapper<>();
        
        // 根据账户类型设置查询条件
        if ("entity".equals(accountType)) {
            query.eq(CommissionDetail::getEntityId, accountId.intValue());
        } else if ("partner".equals(accountType)) {
            query.eq(CommissionDetail::getPartnerId, accountId.intValue());
        } else if ("shop".equals(accountType)) {
            query.eq(CommissionDetail::getShopId, accountId.intValue());
        }
        
        // 设置其他查询条件
        if (status != null) {
            query.eq(CommissionDetail::getSettlementStatus, status);
        }
        
        if (startDateTime != null) {
            query.ge(CommissionDetail::getCreateTime, startDateTime);
        }
        
        if (endDateTime != null) {
            query.lt(CommissionDetail::getCreateTime, endDateTime);
        }
        
        query.orderByDesc(CommissionDetail::getCreateTime);
        
        // 执行分页查询
        IPage<CommissionDetail> result = commissionDetailMapper.selectPage(page, query);
        
        // 将实体转换为领域对象
        List<Commission> commissions = result.getRecords().stream()
                .map(CommissionFactory::fromEntity)
                .collect(Collectors.toList());
        
        // 使用PageResult的正确构造方式
        PageResult<Commission> pageResult = new PageResult<>();
        pageResult.setPageNum(result.getCurrent());
        pageResult.setPageSize(result.getSize());
        pageResult.setTotal(result.getTotal());
        pageResult.setPages(result.getPages());
        pageResult.setList(commissions);
        return pageResult;
    }

    @Override
    public Commission getCommissionDetail(Long id) {
        CommissionDetail detail = commissionDetailMapper.selectById(id.intValue());
        if (detail == null) {
            return null;
        }
        
        return CommissionFactory.fromEntity(detail);
    }

    @Override
    public CommissionStatisticsVO getCommissionStatistics(String accountType, Long accountId, String period) {
        // 根据时间周期计算统计数据
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate = today;
        
        switch (period) {
            case "day":
                startDate = today;
                break;
            case "week":
                startDate = today.minusDays(today.getDayOfWeek().getValue() - 1);
                break;
            case "month":
                startDate = today.withDayOfMonth(1);
                break;
            case "year":
                startDate = today.withDayOfYear(1);
                break;
            default:
                startDate = today;
        }
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        // 查询统计数据
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal periodAmount = BigDecimal.ZERO;
        BigDecimal unsettledAmount = BigDecimal.ZERO;
        
        // 查询所有佣金记录
        LambdaQueryWrapper<CommissionDetail> query = new LambdaQueryWrapper<>();
        
        // 根据账户类型设置查询条件
        if ("entity".equals(accountType)) {
            query.eq(CommissionDetail::getEntityId, accountId.intValue());
        } else if ("partner".equals(accountType)) {
            query.eq(CommissionDetail::getPartnerId, accountId.intValue());
        } else if ("shop".equals(accountType)) {
            query.eq(CommissionDetail::getShopId, accountId.intValue());
        }
        
        List<CommissionDetail> allDetails = commissionDetailMapper.selectList(query);
        
        // 计算总金额和未结算金额
        for (CommissionDetail detail : allDetails) {
            BigDecimal amount = BigDecimal.ZERO;
            
            if ("entity".equals(accountType)) {
                amount = detail.getEntityAmount();
            } else if ("partner".equals(accountType)) {
                amount = detail.getPartnerAmount();
            } else if ("shop".equals(accountType)) {
                amount = detail.getShopAmount();
            }
            
            totalAmount = totalAmount.add(amount);
            
            if (detail.getSettlementStatus() == 0) {
                unsettledAmount = unsettledAmount.add(amount);
            }
            
            // 计算时间段内的金额
            if (detail.getCreateTime() != null && 
                detail.getCreateTime().isAfter(startDateTime) && 
                detail.getCreateTime().isBefore(endDateTime)) {
                periodAmount = periodAmount.add(amount);
            }
        }
        
        BigDecimal settledAmount = totalAmount.subtract(unsettledAmount);
        
        // 构建统计结果
        CommissionStatisticsVO statisticsVO = new CommissionStatisticsVO();
        statisticsVO.setTotalAmount(totalAmount);
        // 不使用setPeriodAmount方法，因为CommissionStatisticsVO中没有这个字段
        statisticsVO.setUnsettledAmount(unsettledAmount);
        statisticsVO.setSettledAmount(settledAmount);
        statisticsVO.setOrderCount(allDetails.size());
        
        return statisticsVO;
    }

    @Override
    public List<Map<String, Object>> statisticsByDate(String accountType, Long accountId, 
                                                   LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endDateTime = endDate != null ? endDate.plusDays(1).atStartOfDay() : null;
        
        // 查询佣金记录
        LambdaQueryWrapper<CommissionDetail> query = new LambdaQueryWrapper<>();
        
        // 根据账户类型设置查询条件
        if ("entity".equals(accountType)) {
            query.eq(CommissionDetail::getEntityId, accountId.intValue());
        } else if ("partner".equals(accountType)) {
            query.eq(CommissionDetail::getPartnerId, accountId.intValue());
        } else if ("shop".equals(accountType)) {
            query.eq(CommissionDetail::getShopId, accountId.intValue());
        }
        
        if (startDateTime != null) {
            query.ge(CommissionDetail::getCreateTime, startDateTime);
        }
        
        if (endDateTime != null) {
            query.lt(CommissionDetail::getCreateTime, endDateTime);
        }
        
        List<CommissionDetail> details = commissionDetailMapper.selectList(query);
        
        // 按日期分组统计
        Map<LocalDate, BigDecimal> dateMap = new HashMap<>();
        
        for (CommissionDetail detail : details) {
            if (detail.getCreateTime() == null) {
                continue;
            }
            
            LocalDate date = detail.getCreateTime().toLocalDate();
            BigDecimal amount = BigDecimal.ZERO;
            
            if ("entity".equals(accountType)) {
                amount = detail.getEntityAmount();
            } else if ("partner".equals(accountType)) {
                amount = detail.getPartnerAmount();
            } else if ("shop".equals(accountType)) {
                amount = detail.getShopAmount();
            }
            
            dateMap.put(date, dateMap.getOrDefault(date, BigDecimal.ZERO).add(amount));
        }
        
        // 转换为结果列表
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<LocalDate, BigDecimal> entry : dateMap.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("date", entry.getKey().toString());
            item.put("amount", entry.getValue());
            result.add(item);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void triggerAutoSettlement() {
        // 查找需要自动结算的账户
        List<Map<String, Object>> accountsToSettle = new ArrayList<>();
        
        // 实际实现中应该从配置或数据库中获取需要自动结算的账户
        // 这里简化处理，仅作示例
        
        // 批量处理结算
        for (Map<String, Object> account : accountsToSettle) {
            String accountType = (String) account.get("accountType");
            Long accountId = ((Number) account.get("accountId")).longValue();
            BigDecimal minAmount = (BigDecimal) account.get("minSettlementAmount");
            
            try {
                settleCommission(accountType, accountId, minAmount);
            } catch (Exception e) {
                log.error("自动结算失败: accountType={}, accountId={}", accountType, accountId, e);
            }
        }
    }
} 