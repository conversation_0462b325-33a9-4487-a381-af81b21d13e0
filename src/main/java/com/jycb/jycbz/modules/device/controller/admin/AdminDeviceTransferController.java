package com.jycb.jycbz.modules.device.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.utils.SecurityUtils;
import com.jycb.jycbz.modules.device.dto.DeviceTransferDTO;
import com.jycb.jycbz.modules.device.entity.DeviceTransferRecord;
import com.jycb.jycbz.modules.device.service.DeviceTransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员设备转移控制器
 */
@Tag(name = "管理员设备转移管理", description = "管理员设备转移相关接口")
@RestController
@RequestMapping("/admin/device/transfer")
public class AdminDeviceTransferController {

    @Autowired
    private DeviceTransferService deviceTransferService;

    /**
     * 申请设备转移
     */
    @PostMapping("/apply")
    @Operation(summary = "申请设备转移")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.CREATE,
            description = "申请设备转移"
    )
    public Result<Integer> applyDeviceTransfer(@RequestBody @Valid DeviceTransferDTO transferDTO) {
        // 设置操作人信息
        transferDTO.setOperatorId(SecurityUtils.getCurrentUserId().intValue());
        transferDTO.setOperatorName(SecurityUtils.getCurrentUsername());
        
        Integer recordId = deviceTransferService.applyDeviceTransfer(transferDTO);
        return Result.success(recordId);
    }

    /**
     * 审核设备转移申请
     */
    @PostMapping("/{recordId}/audit")
    @Operation(summary = "审核设备转移申请")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "审核设备转移申请",
            targetId = "#recordId"
    )
    public Result<Void> auditDeviceTransfer(
            @PathVariable Integer recordId,
            @Parameter(description = "是否通过") @RequestParam Boolean approved,
            @Parameter(description = "审核意见") @RequestParam(required = false) String auditComment
    ) {
        boolean result = deviceTransferService.auditDeviceTransfer(
                recordId, approved, auditComment,
                SecurityUtils.getCurrentUserId().intValue(), SecurityUtils.getCurrentUsername());
        
        return result ? Result.success() : Result.failed("审核失败");
    }

    /**
     * 取消设备转移申请
     */
    @PostMapping("/{recordId}/cancel")
    @Operation(summary = "取消设备转移申请")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "取消设备转移申请",
            targetId = "#recordId"
    )
    public Result<Void> cancelDeviceTransfer(
            @PathVariable Integer recordId,
            @Parameter(description = "取消原因") @RequestParam(required = false) String cancelReason
    ) {
        boolean result = deviceTransferService.cancelDeviceTransfer(recordId, cancelReason);
        return result ? Result.success() : Result.failed("取消失败");
    }

    /**
     * 分页查询设备转移记录
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询设备转移记录")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备转移记录"
    )
    public Result<Page<DeviceTransferRecord>> pageTransferRecords(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "设备ID") @RequestParam(required = false) Integer deviceId,
            @Parameter(description = "源门店ID") @RequestParam(required = false) Integer fromShopId,
            @Parameter(description = "目标门店ID") @RequestParam(required = false) Integer toShopId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status
    ) {
        Page<DeviceTransferRecord> page = new Page<>(pageNum, pageSize);
        Page<DeviceTransferRecord> result = deviceTransferService.pageTransferRecords(
                page, deviceId, fromShopId, toShopId, status);
        
        return Result.success(result);
    }

    /**
     * 获取设备转移记录详情
     */
    @GetMapping("/{recordId}")
    @Operation(summary = "获取设备转移记录详情")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备转移记录详情",
            targetId = "#recordId"
    )
    public Result<DeviceTransferRecord> getTransferRecord(@PathVariable Integer recordId) {
        DeviceTransferRecord record = deviceTransferService.getTransferRecord(recordId);
        return record != null ? Result.success(record) : Result.failed("记录不存在");
    }

    /**
     * 检查设备是否可以转移
     */
    @GetMapping("/check/{deviceId}")
    @Operation(summary = "检查设备是否可以转移")
    public Result<Map<String, Object>> checkDeviceTransferable(
            @PathVariable Integer deviceId,
            @Parameter(description = "源门店ID") @RequestParam Integer fromShopId
    ) {
        Map<String, Object> result = deviceTransferService.checkDeviceTransferable(deviceId, fromShopId);
        return Result.success(result);
    }

    /**
     * 获取设备转移统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取设备转移统计信息")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备转移统计信息"
    )
    public Result<Map<String, Object>> getTransferStatistics(
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId
    ) {
        Map<String, Object> statistics = deviceTransferService.getTransferStatistics(shopId);
        return Result.success(statistics);
    }
}
