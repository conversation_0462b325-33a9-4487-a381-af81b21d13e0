{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/reset-password.vue?5faa", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/reset-password.vue?2776", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/reset-password.vue?8cec", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/reset-password.vue?9db9", "uni-app:///pages/login/reset-password.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/reset-password.vue?4c33", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/login/reset-password.vue?0734"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "resetEmail", "verificationCode", "newPassword", "confirmPassword", "onLoad", "console", "onShow", "onUnload", "methods", "disableScroll", "uni", "pageStyle", "overflow", "currentWebview", "bounce", "e", "document", "passive", "enableScroll", "goBack", "sendVerificationCode", "title", "icon", "mask", "setTimeout", "resetPassword"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4vB,CAAgB,0vBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmFhxB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;;IAEA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;UACAC;YACAC;cACAC;YACA;UACA;QACA;QACA;QAAA,KACA;UACA;UACAC;YACAC;UACA;QACA;QACA;QAAA,KACA;UACA;YACAC;UACA;;UAEA;UACA;;UAEA;UACAC;YAAAC;UAAA;QACA;MACA;QACAZ;MACA;IACA;IACAa;MACA;MACA;QACA;QACA;UACAR;YACAC;cACAC;YACA;UACA;QACA;QACA;QAAA,KACA;UACA;UACAC;YACAC;UACA;QACA;QACA;QAAA,KACA;UACAE;YAAAC;UAAA;UACA;QACA;MACA;QACAZ;MACA;IACA;IACAc;MACAT;IACA;IACAU;MAAA;MACA;MACA;QACAV;UACAW;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAZ;UACAW;UACAC;QACA;QACA;MACA;;MAEA;MACAZ;QACAW;QACAE;MACA;;MAEA;MACAC;QACAd;QAEAA;UACAW;UACAC;QACA;QAEAjB;;QAEA;QACA;MACA;IACA;IACAoB;MACA;MACA;QACAf;UACAW;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAZ;UACAW;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAZ;UACAW;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAZ;UACAW;UACAC;QACA;QACA;MACA;;MAEA;MACAZ;QACAW;QACAE;MACA;;MAEA;MACAC;QACAd;QAEAA;UACAW;UACAC;QACA;QAEAjB;;QAEA;QACAmB;UACAd;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClRA;AAAA;AAAA;AAAA;AAAykC,CAAgB,miCAAG,EAAC,C;;;;;;;;;;;ACA7lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/reset-password.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/reset-password.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./reset-password.vue?vue&type=template&id=0a59ed90&\"\nvar renderjs\nimport script from \"./reset-password.vue?vue&type=script&lang=js&\"\nexport * from \"./reset-password.vue?vue&type=script&lang=js&\"\nimport style0 from \"./reset-password.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/reset-password.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reset-password.vue?vue&type=template&id=0a59ed90&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reset-password.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reset-password.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-container safe-area-bottom page-reset-password\">\n    <view class=\"login-background\"></view>\n    <view class=\"reset-container\">\n      <view class=\"scrollable-content\">\n        <view class=\"reset-header\">\n          <view class=\"close-button\" @tap=\"goBack\">\n            <text class=\"material-icons\">close</text>\n          </view>\n          <view class=\"reset-logo\">\n            <image src=\"/static/logo2.svg\" mode=\"aspectFit\"></image>\n          </view>\n        </view>\n        \n        <view class=\"reset-title\">\n          <text>找回密码</text>\n        </view>\n        \n        <view class=\"form-container\">\n          <view class=\"form-group\">\n            <text class=\"form-label\">邮箱地址</text>\n            <view class=\"input-icon\">\n              <text class=\"material-icons icon-input\">email</text>\n              <view class=\"form-input form-input-readonly\">\n                <text><EMAIL></text>\n              </view>\n            </view>\n          </view>\n          \n          <button class=\"button primary send-btn\" @tap=\"sendVerificationCode\">\n            发送验证码\n          </button>\n          \n          <view class=\"form-group verification-code-container\">\n            <text class=\"form-label\">验证码</text>\n            <view class=\"input-icon\">\n              <text class=\"material-icons icon-input\">vpn_key</text>\n              <input type=\"text\" class=\"form-input\" placeholder=\"请输入验证码\" v-model=\"verificationCode\" placeholder-style=\"color: rgba(255, 255, 255, 0.3);\" />\n            </view>\n          </view>\n          \n          <view class=\"form-group\">\n            <text class=\"form-label\">新密码</text>\n            <view class=\"input-icon\">\n              <text class=\"material-icons icon-input\">lock</text>\n              <input type=\"password\" class=\"form-input\" placeholder=\"请输入新密码\" v-model=\"newPassword\" placeholder-style=\"color: rgba(255, 255, 255, 0.3);\" />\n            </view>\n          </view>\n          \n          <view class=\"form-group\">\n            <text class=\"form-label\">确认密码</text>\n            <view class=\"input-icon\">\n              <text class=\"material-icons icon-input\">lock</text>\n              <input type=\"password\" class=\"form-input\" placeholder=\"请再次输入新密码\" v-model=\"confirmPassword\" placeholder-style=\"color: rgba(255, 255, 255, 0.3);\" />\n            </view>\n          </view>\n          \n          <view class=\"info-notice\">\n            <text class=\"material-icons notice-icon\">info</text>\n            <text class=\"notice-text\">请查收邮件输入验证码，密码长度不低于6位数</text>\n          </view>\n          \n          <button class=\"button primary\" @tap=\"resetPassword\">\n            修改密码\n          </button>\n          \n          <view class=\"back-to-login\" @tap=\"goBack\">\n            <text>返回登录</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"version-info version-safe-bottom\">\n      <text>版本号: v1.0.0</text>\n    </view>\n    \n    <!-- iOS安全区域 - 透明背景 -->\n    <view class=\"login-safe-area\"></view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      resetEmail: '',\n      verificationCode: '',\n      newPassword: '',\n      confirmPassword: ''\n    }\n  },\n  onLoad() {\n    // 页面加载完成\n    console.log('找回密码页面加载完成');\n    \n    // 禁止页面滚动\n    this.disableScroll();\n  },\n  onShow() {\n    // 禁止页面滚动\n    this.disableScroll();\n  },\n  onUnload() {\n    // 页面卸载时移除事件监听\n    this.enableScroll();\n  },\n  methods: {\n    disableScroll() {\n      // 禁止页面滚动 - 使用uni-app API\n      try {\n        // 小程序环境\n        if (uni.canIUse('setPageMeta')) {\n          uni.setPageMeta({\n            pageStyle: {\n              overflow: 'hidden'\n            }\n          });\n        } \n        // App环境\n        else if (plus && plus.webview) {\n          const currentWebview = plus.webview.currentWebview();\n          currentWebview.setStyle({\n            bounce: 'none'\n          });\n        }\n        // H5环境\n        else if (typeof document !== 'undefined') {\n          const preventDefault = (e) => {\n            e.preventDefault();\n          };\n          \n          // 保存函数引用以便移除\n          this.preventDefault = preventDefault;\n          \n          // 添加事件监听\n          document.addEventListener('touchmove', preventDefault, { passive: false });\n        }\n      } catch (e) {\n        console.error('禁止滚动失败', e);\n      }\n    },\n    enableScroll() {\n      // 移除滚动限制 - 使用uni-app API\n      try {\n        // 小程序环境\n        if (uni.canIUse('setPageMeta')) {\n          uni.setPageMeta({\n            pageStyle: {\n              overflow: 'auto'\n            }\n          });\n        } \n        // App环境\n        else if (plus && plus.webview) {\n          const currentWebview = plus.webview.currentWebview();\n          currentWebview.setStyle({\n            bounce: 'vertical'\n          });\n        }\n        // H5环境\n        else if (typeof document !== 'undefined' && this.preventDefault) {\n          document.removeEventListener('touchmove', this.preventDefault, { passive: false });\n          this.preventDefault = null;\n        }\n      } catch (e) {\n        console.error('启用滚动失败', e);\n      }\n    },\n    goBack() {\n      uni.navigateBack();\n    },\n    sendVerificationCode() {\n      // 验证邮箱格式\n      if (!this.resetEmail) {\n        uni.showToast({\n          title: '请输入邮箱地址',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 简单的邮箱格式验证\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(this.resetEmail)) {\n        uni.showToast({\n          title: '邮箱格式不正确',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '发送中...',\n        mask: true\n      });\n      \n      // 模拟发送验证码\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: '验证码已发送',\n          icon: 'success'\n        });\n        \n        console.log('发送验证码到邮箱:', this.resetEmail);\n        \n        // 这里应该调用后端接口发送验证码\n        // 模拟验证码为1234\n      }, 1500);\n    },\n    resetPassword() {\n      // 验证表单\n      if (!this.verificationCode || !this.newPassword || !this.confirmPassword) {\n        uni.showToast({\n          title: '请填写完整信息',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 验证验证码\n      if (this.verificationCode !== '1234') {\n        uni.showToast({\n          title: '验证码错误',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 验证密码长度\n      if (this.newPassword.length < 8) {\n        uni.showToast({\n          title: '密码长度不能少于8位',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 验证两次密码是否一致\n      if (this.newPassword !== this.confirmPassword) {\n        uni.showToast({\n          title: '两次输入的密码不一致',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '修改中...',\n        mask: true\n      });\n      \n      // 模拟密码重置\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: '密码修改成功',\n          icon: 'success'\n        });\n        \n        console.log('密码重置成功');\n        \n        // 1.5秒后返回登录页\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }, 1500);\n    }\n  }\n}\n</script>\n\n<style>\n/* 引入Material Icons图标库 */\n@font-face {\n  font-family: 'Material Icons';\n  font-style: normal;\n  font-weight: 400;\n  src: url(https://fonts.gstatic.com/s/materialicons/v139/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n}\n\n.material-icons {\n  font-family: 'Material Icons';\n  font-weight: normal;\n  font-style: normal;\n  font-size: 48rpx;\n  line-height: 1;\n  letter-spacing: normal;\n  text-transform: none;\n  display: inline-block;\n  white-space: nowrap;\n  word-wrap: normal;\n  direction: ltr;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: rgba(255, 255, 255, 0.7);\n}\n\npage {\n  background-color: #121212;\n  color: #FFFFFF;\n  overflow: hidden; /* 禁止页面滚动 */\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n  position: fixed; /* 固定页面位置 */\n}\n\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n  max-width: 100%;\n}\n\n.page-container {\n  min-height: 100vh;\n  height: 100vh; /* 限制高度为视口高度 */\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  position: relative;\n  overflow: hidden; /* 禁止容器滚动 */\n  box-sizing: border-box; /* 确保padding不会增加宽度 */\n  width: 100%;\n  position: fixed; /* 固定容器位置 */\n  top: 0;\n  left: 0;\n}\n\n.login-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #121212;\n  background-image: radial-gradient(circle at top right, rgba(149, 39, 198, 0.15) 0%, transparent 40%), \n                    radial-gradient(circle at bottom left, rgba(255, 46, 147, 0.15) 0%, transparent 40%);\n  z-index: 0;\n}\n\n.reset-container {\n  width: 100%;\n  max-width: 750rpx;\n  padding: 0rpx 30rpx 80rpx 30rpx;\n  position: relative;\n  z-index: 1;\n  display: flex;\n  flex-direction: column;\n  overflow-y: hidden;\n  max-height: 94vh;\n  box-sizing: border-box;\n  -webkit-overflow-scrolling: none;\n  touch-action: none;\n}\n\n/* 创建一个内部滚动容器 */\n.scrollable-content {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  max-height: calc(100vh - 100rpx);\n  overflow-y: auto;\n  padding-right: 10rpx;\n  -webkit-overflow-scrolling: touch;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.reset-header {\n  text-align: center;\n  margin-bottom: 10rpx;\n  margin-top: 60rpx;\n  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);\n  position: relative;\n}\n\n.close-button {\n  position: absolute;\n  top: 40rpx;\n  right: 0;\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.05);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.close-button .material-icons {\n  font-size: 40rpx;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.close-button:active {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.reset-logo {\n  margin-bottom: 10rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);\n}\n\n.reset-logo image {\n  height: 360rpx;\n  width: 520rpx;\n  max-width: 100%;\n  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);\n}\n\n.reset-title {\n  text-align: center;\n  margin-bottom: 20rpx;\n}\n\n.reset-title text {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #A855F7;\n  font-family: \"幼圆\", \"YouYuan\", \"Microsoft YaHei\", sans-serif;\n  text-shadow: 0 0 10rpx rgba(168, 85, 247, 0.3);\n  letter-spacing: 2rpx;\n}\n\n.form-container {\n  width: 100%;\n  margin-bottom: 30rpx;\n  padding: 0 20rpx;\n  max-width: 650rpx;\n  margin-left: auto;\n  margin-right: auto;\n  overflow-y: hidden;\n  touch-action: none;\n  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);\n}\n\n.form-group {\n  margin-bottom: 30rpx;\n  opacity: 1;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 16rpx;\n  color: rgba(255, 255, 255, 0.7);\n  font-weight: 500;\n  font-size: 32rpx;\n}\n\n.form-input {\n  width: 100%;\n  height: 90rpx;\n  padding: 0 30rpx 0 90rpx;\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 24rpx;\n  color: #FFFFFF;\n  font-size: 34rpx;\n  box-sizing: border-box;\n}\n\n.input-icon {\n  position: relative;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.icon-input {\n  position: absolute;\n  left: 30rpx;\n  top: 50%;\n  transform: translateY(-50%);\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 36rpx;\n  z-index: 1;\n}\n\n.info-notice {\n  display: flex;\n  align-items: center;\n  margin: 20rpx 0;\n  padding: 20rpx;\n  border-radius: 16rpx;\n  background: rgba(106, 27, 154, 0.05);\n  border: 1px solid #9527C6;\n}\n\n.notice-icon {\n  font-size: 36rpx;\n  color: #A855F7;\n  margin-right: 10rpx;\n}\n\n.notice-text {\n  font-size: 26rpx;\n  color: rgba(255, 255, 255, 0.7);\n  line-height: 1.5;\n}\n\n.button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100rpx;\n  border-radius: 9999rpx;\n  font-weight: 600;\n  border: none;\n  width: 70%;\n  margin: 0 auto;\n  font-size: 36rpx;\n  margin-top: 40rpx;\n  margin-bottom: 30rpx;\n  font-family: \"幼圆\", \"YouYuan\", \"Microsoft YaHei\", sans-serif;\n}\n\n.button.primary {\n  background: linear-gradient(to right, #9527C6, #A875FF);\n  color: white;\n  box-shadow: 0 8rpx 20rpx rgba(149, 39, 198, 0.3);\n  position: relative;\n  letter-spacing: 4rpx;\n  opacity: 1;\n}\n\n.send-btn {\n  margin-top: 16rpx;\n  margin-bottom: 40rpx;\n  width: 50%;\n}\n\n.verification-code-container {\n  margin-top: 10rpx;\n}\n\n/* 添加伪元素增强阴影效果 */\n.button.primary::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border-radius: 9999rpx;\n  background: linear-gradient(to right, #9527C6, #A875FF);\n  opacity: 0.6;\n  filter: blur(15rpx);\n  z-index: -1;\n  transform: translateY(10rpx) scale(0.95);\n}\n\n/* 按钮点击效果 */\n.button.primary:active {\n  transform: translateY(2rpx);\n}\n\n.button.primary:active::after {\n  transform: translateY(5rpx) scale(0.95);\n  opacity: 0.4;\n}\n\n.back-to-login {\n  text-align: center;\n  margin-top: 30rpx;\n  font-size: 28rpx;\n  color: #A855F7;\n  text-decoration: underline;\n}\n\n.version-info {\n  position: fixed;\n  bottom: 30rpx;\n  left: 0;\n  width: 100%;\n  text-align: center;\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.5);\n  z-index: 1;\n  padding: 10rpx;\n  background-color: transparent;\n}\n\n/* 小屏幕适配 */\n@media screen and (max-width: 375px) {\n  .reset-logo image {\n    height: 300rpx;\n    width: 430rpx;\n  }\n  \n  .form-input {\n    height: 80rpx;\n    font-size: 28rpx;\n  }\n  \n  .button {\n    height: 80rpx;\n    font-size: 30rpx;\n  }\n  \n  .reset-title text {\n    font-size: 42rpx;\n  }\n}\n\n.form-input-readonly {\n  display: flex;\n  align-items: center;\n  color: rgba(255, 255, 255, 0.7);\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: not-allowed;\n}\n\n.form-input-readonly text {\n  font-size: 34rpx;\n}\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reset-password.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./reset-password.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753035246037\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}