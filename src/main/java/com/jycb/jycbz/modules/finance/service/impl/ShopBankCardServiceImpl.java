package com.jycb.jycbz.modules.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.finance.entity.ShopBankCard;
import com.jycb.jycbz.modules.finance.mapper.ShopBankCardMapper;
import com.jycb.jycbz.modules.finance.service.ShopBankCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店银行卡服务实现类
 */
@Slf4j
@Service
public class ShopBankCardServiceImpl extends ServiceImpl<ShopBankCardMapper, ShopBankCard> implements ShopBankCardService {

    @Autowired
    private ShopBankCardMapper shopBankCardMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addBankCard(Integer shopId, String bankName, String bankAccount, String accountName, String bankBranch, Integer isDefault) {
        // 创建银行卡对象
        ShopBankCard bankCard = new ShopBankCard();
        bankCard.setShopId(shopId);
        bankCard.setBankName(bankName);
        bankCard.setBankAccount(bankAccount);
        bankCard.setAccountName(accountName);
        bankCard.setBankBranch(bankBranch);
        bankCard.setIsDefault(isDefault);
        bankCard.setStatus(1); // 默认启用
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        bankCard.setCreateTime(now);
        bankCard.setUpdateTime(now);
        
        // 如果设置为默认银行卡，则清除其他默认标记
        if (isDefault != null && isDefault == 1) {
            shopBankCardMapper.clearDefaultBankCard(shopId);
        }
        
        // 保存银行卡
        save(bankCard);
        
        return bankCard.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBankCard(Integer id, String bankName, String bankAccount, String accountName, String bankBranch, Integer isDefault) {
        // 查询银行卡
        ShopBankCard bankCard = getById(id);
        if (bankCard == null) {
            throw new BusinessException("银行卡不存在");
        }
        
        // 更新银行卡信息
        bankCard.setBankName(bankName);
        bankCard.setBankAccount(bankAccount);
        bankCard.setAccountName(accountName);
        bankCard.setBankBranch(bankBranch);
        bankCard.setUpdateTime(LocalDateTime.now());
        
        // 如果设置为默认银行卡，则清除其他默认标记
        if (isDefault != null && isDefault == 1 && (bankCard.getIsDefault() == null || bankCard.getIsDefault() != 1)) {
            shopBankCardMapper.clearDefaultBankCard(bankCard.getShopId());
            bankCard.setIsDefault(1);
        } else if (isDefault != null) {
            bankCard.setIsDefault(isDefault);
        }
        
        return updateById(bankCard);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBankCard(Integer id) {
        // 查询银行卡
        ShopBankCard bankCard = getById(id);
        if (bankCard == null) {
            throw new BusinessException("银行卡不存在");
        }
        
        // 如果是默认银行卡，不允许删除
        if (bankCard.getIsDefault() != null && bankCard.getIsDefault() == 1) {
            throw new BusinessException("默认银行卡不能删除");
        }
        
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultBankCard(Integer id, Integer shopId) {
        // 查询银行卡
        ShopBankCard bankCard = getById(id);
        if (bankCard == null) {
            throw new BusinessException("银行卡不存在");
        }
        
        // 验证银行卡是否属于指定门店
        if (!bankCard.getShopId().equals(shopId)) {
            throw new BusinessException("银行卡不属于该门店");
        }
        
        // 清除其他默认标记
        shopBankCardMapper.clearDefaultBankCard(shopId);
        
        // 设置为默认
        return shopBankCardMapper.setDefaultBankCard(id) > 0;
    }

    @Override
    public ShopBankCard getDefaultBankCard(Integer shopId) {
        return shopBankCardMapper.selectDefaultBankCard(shopId);
    }

    @Override
    public List<ShopBankCard> getBankCardsByShopId(Integer shopId) {
        return shopBankCardMapper.selectBankCardsByShopId(shopId);
    }

    @Override
    public PageResult<ShopBankCard> getBankCardPage(Integer shopId, Integer page, Integer size) {
        // 创建分页参数
        Page<ShopBankCard> pageParam = new Page<>(page, size);
        
        // 创建查询条件
        LambdaQueryWrapper<ShopBankCard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopBankCard::getShopId, shopId);
        queryWrapper.orderByDesc(ShopBankCard::getIsDefault);
        queryWrapper.orderByDesc(ShopBankCard::getCreateTime);
        
        // 执行分页查询
        Page<ShopBankCard> pageResult = page(pageParam, queryWrapper);
        
        // 转换为PageResult对象
        PageResult<ShopBankCard> result = new PageResult<>();
        result.setTotal(pageResult.getTotal());
        result.setList(pageResult.getRecords());
        
        return result;
    }

    @Override
    public ShopBankCard getBankCardDetail(Integer id) {
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBankCardStatus(Integer id, Integer status) {
        // 查询银行卡
        ShopBankCard bankCard = getById(id);
        if (bankCard == null) {
            throw new BusinessException("银行卡不存在");
        }
        
        // 如果是默认银行卡且要禁用，不允许操作
        if (bankCard.getIsDefault() != null && bankCard.getIsDefault() == 1 && status == 0) {
            throw new BusinessException("默认银行卡不能禁用");
        }
        
        // 更新状态
        bankCard.setStatus(status);
        bankCard.setUpdateTime(LocalDateTime.now());
        
        return updateById(bankCard);
    }
} 