package com.jycb.jycbz.modules.device.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.constant.RoleConstants;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.dto.DeviceCreateDTO;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.device.vo.DeviceBindVO;
import com.jycb.jycbz.modules.device.vo.DeviceVO;
import com.jycb.jycbz.modules.device.dto.DeviceFeeDTO;
import com.jycb.jycbz.modules.device.vo.DeviceFeeVO;
import com.jycb.jycbz.modules.device.service.DeviceFeeService;
import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.modules.partner.entity.Partner;
import com.jycb.jycbz.modules.partner.service.PartnerService;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合作商设备管理控制器
 * 用于合作商管理旗下设备
 */
@Slf4j
@RestController
@RequestMapping("/partner/device")
@Tag(name = "合作商设备管理", description = "合作商管理旗下设备相关接口")
@RequiredArgsConstructor
@SaCheckRole(RoleConstants.PARTNER_ADMIN)
@Validated
public class PartnerDeviceManagementController {

    private final DeviceService deviceService;
    private final ShopService shopService;
    private final DeviceFeeService deviceFeeService;
    private final PartnerService partnerService;

    /**
     * 获取设备列表
     *
     * @param partnerId 合作商ID
     * @param shopId 门店ID
     * @param status 设备状态
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 设备列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取设备列表", description = "分页获取合作商下的设备列表")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.QUERY,
        description = "合作商查询设备列表",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<PageResult<DeviceVO>> getDeviceList(
            @Parameter(description = "合作商ID") @RequestParam(required = false) Integer partnerId,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "设备状态：1-正常 2-维护中 3-故障") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int pageSize) {
        
        // 获取当前登录的合作商ID
        Integer currentPartnerId = getPartnerIdFromToken();
        
        // 验证门店是否属于当前合作商
        if (shopId != null) {
            validateShopOwnership(shopId, currentPartnerId);
        }
        
        // 查询设备列表（合作商只能查看自己的设备）
        Page<Device> resultPage = deviceService.getPartnerDevicePage(pageNum, pageSize,
                                                                   Long.valueOf(currentPartnerId),
                                                                   shopId != null ? Long.valueOf(shopId) : null,
                                                                   status, null, null);

        // 转换为VO
        PageResult<DeviceVO> pageResult = new PageResult<>();
        pageResult.setPageNum(resultPage.getCurrent());
        pageResult.setPageSize(resultPage.getSize());
        pageResult.setTotal(resultPage.getTotal());
        pageResult.setPages(resultPage.getPages());

        log.info("开始转换设备列表，共{}个设备", resultPage.getRecords().size());

        // 转换Device为DeviceVO（直接转换，避免权限验证问题）
        List<DeviceVO> deviceVOList = resultPage.getRecords().stream()
                .map(device -> {
                    try {
                        log.debug("转换设备: ID={}, 编号={}, 名称={}", device.getId(), device.getDeviceNo(), device.getDeviceName());

                        // 直接转换Device为DeviceVO，避免调用可能有权限问题的getDeviceDetail方法
                        DeviceVO deviceVO = new DeviceVO();
                        deviceVO.setId(device.getId());
                        deviceVO.setDeviceNo(device.getDeviceNo());
                        deviceVO.setDeviceName(device.getDeviceName());
                        deviceVO.setDeviceType(device.getDeviceType());
                        deviceVO.setEntityId(device.getEntityId());
                        deviceVO.setPartnerId(device.getPartnerId());
                        deviceVO.setShopId(device.getShopId());
                        deviceVO.setBindCode(device.getBindCode());
                        deviceVO.setMacAddress(device.getMacAddress());
                        deviceVO.setQrcodeUrl(device.getQrcodeUrl());
                        deviceVO.setIsBound(device.getIsBound());
                        deviceVO.setStatus(device.getStatus());
                        deviceVO.setOnlineStatus(device.getOnlineStatus());
                        deviceVO.setInUse(device.getInUse());
                        // currentUsers和maxUsers字段在DeviceVO中不存在，跳过映射
                        deviceVO.setLatitude(device.getLatitude());
                        deviceVO.setLongitude(device.getLongitude());
                        deviceVO.setBatteryLevel(device.getBatteryLevel());
                        deviceVO.setAddress(device.getAddress());
                        deviceVO.setProvince(device.getProvince());
                        deviceVO.setCity(device.getCity());
                        deviceVO.setDistrict(device.getDistrict());
                        deviceVO.setRegionId(device.getRegionId());
                        deviceVO.setBindTime(device.getBindTime());
                        deviceVO.setLastOnlineTime(device.getLastOnlineTime());
                        deviceVO.setLastLocationTime(device.getLastLocationTime());
                        deviceVO.setActivateTime(device.getActivateTime());
                        deviceVO.setRemark(device.getRemark());
                        deviceVO.setSalesId(device.getSalesId());
                        deviceVO.setSalesName(device.getSalesName());
                        deviceVO.setCreateTime(device.getCreateTime());
                        deviceVO.setUpdateTime(device.getUpdateTime());

                        log.debug("设备转换成功: ID={}", device.getId());
                        return deviceVO;
                    } catch (Exception e) {
                        log.error("转换设备VO失败，设备ID: {}, 错误: {}", device.getId(), e.getMessage(), e);
                        return null;
                    }
                })
                .filter(deviceVO -> deviceVO != null) // 过滤掉null值
                .collect(Collectors.toList());

        log.info("设备转换完成，成功转换{}个设备", deviceVOList.size());
        pageResult.setList(deviceVOList);

        return Result.success(pageResult);
    }

    /**
     * 获取设备详情
     *
     * @param id 设备ID
     * @return 设备详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取设备详情", description = "获取指定设备的详细信息")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询设备详情",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<DeviceVO> getDeviceDetail(@Parameter(description = "设备ID") @PathVariable Integer id) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 获取设备详情
        DeviceVO device = deviceService.getDeviceDetail(id);
        
        if (device == null) {
            return Result.failed("设备不存在");
        }
        
        // 验证设备是否属于当前合作商
        if (!partnerId.equals(device.getPartnerId())) {
            return Result.failed("您无权查看该设备");
        }
        
        return Result.success(device);
    }

    /**
     * 添加设备
     *
     * @param createDTO 设备创建信息
     * @return 添加结果
     */
    @PostMapping
    @Operation(summary = "添加设备", description = "添加新设备")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.CREATE,
        description = "合作商添加设备",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<DeviceVO> addDevice(@Parameter(description = "设备创建信息") @Valid @RequestBody DeviceCreateDTO createDTO) {

        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 获取合作商信息以获取entityId
        Partner partner = partnerService.getById(partnerId);
        if (partner == null) {
            return Result.failed("合作商信息不存在");
        }

        // 自动设置设备所属合作商ID和业务主体ID，确保安全性
        createDTO.setPartnerId(partnerId);
        createDTO.setEntityId(partner.getEntityId().intValue());

        // 创建设备
        DeviceVO deviceVO = deviceService.createDevice(createDTO);

        return Result.success(deviceVO);
    }

    /**
     * 更新设备
     *
     * @param id 设备ID
     * @param device 设备信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新设备", description = "更新设备信息")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.UPDATE,
        description = "合作商更新设备信息",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<DeviceVO> updateDevice(
            @Parameter(description = "设备ID") @PathVariable Integer id,
            @Parameter(description = "设备信息") @Valid @RequestBody Device device) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 获取现有设备信息
        DeviceVO existingDevice = deviceService.getDeviceDetail(id);
        if (existingDevice == null) {
            return Result.failed("设备不存在");
        }
        
        // 验证设备是否属于当前合作商
        if (!partnerId.equals(existingDevice.getPartnerId())) {
            return Result.failed("您无权修改该设备");
        }
        
        // 设置设备ID和合作商ID
        device.setId(id);
        device.setPartnerId(partnerId);
        
        // 更新设备
        deviceService.updateById(device);
        
        return Result.success(deviceService.getDeviceDetail(id));
    }

    /**
     * 删除设备
     *
     * @param id 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备", description = "删除设备")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.DELETE,
        description = "合作商删除设备",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<Void> deleteDevice(@Parameter(description = "设备ID") @PathVariable Integer id) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 获取设备详情
        DeviceVO device = deviceService.getDeviceDetail(id);
        if (device == null) {
            return Result.failed("设备不存在");
        }
        
        // 验证设备是否属于当前合作商
        if (!partnerId.equals(device.getPartnerId())) {
            return Result.failed("您无权删除该设备");
        }
        
        // 删除设备
        deviceService.deleteDevice(id);
        
        return Result.success();
    }

    /**
     * 绑定设备
     *
     * @param bindRequest 绑定请求
     * @return 绑定结果
     */
    @PostMapping("/bind")
    @Operation(summary = "绑定设备", description = "将设备绑定到门店")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.UPDATE,
        description = "合作商绑定设备",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<Void> bindDevice(@Parameter(description = "绑定信息") @Valid @RequestBody Map<String, Object> bindRequest) {

        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        String macAddress = (String) bindRequest.get("macAddress");
        Integer shopId = (Integer) bindRequest.get("shopId");
        String roomNumber = (String) bindRequest.get("roomNumber");

        // 验证门店是否属于当前合作商
        validateShopOwnership(shopId, partnerId);

        // 验证设备是否属于当前合作商
        Device device = deviceService.getDeviceByMacAddress(macAddress);
        if (device == null || !device.getPartnerId().equals(partnerId)) {
            return Result.failed("设备不存在或不属于当前合作商");
        }

        // 创建绑定VO
        DeviceBindVO bindVO = new DeviceBindVO();
        bindVO.setMacAddress(macAddress);
        bindVO.setPartnerId(partnerId);
        bindVO.setShopId(shopId);
        bindVO.setRoomNumber(roomNumber);

        // 执行绑定操作
        boolean success = deviceService.bindDevice(bindVO);
        if (success) {
            return Result.success();
        } else {
            return Result.failed("绑定设备失败");
        }
    }

    /**
     * 解绑设备
     *
     * @param id 设备ID
     * @return 解绑结果
     */
    @PostMapping("/{id}/unbind")
    @Operation(summary = "解绑设备", description = "解绑设备")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.UPDATE,
        description = "合作商解绑设备",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<Void> unbindDevice(@Parameter(description = "设备ID") @PathVariable Integer id) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 获取设备详情
        DeviceVO device = deviceService.getDeviceDetail(id);
        if (device == null) {
            return Result.failed("设备不存在");
        }
        
        // 验证设备是否属于当前合作商
        if (!partnerId.equals(device.getPartnerId())) {
            return Result.failed("您无权解绑该设备");
        }
        
        // 解绑设备
        deviceService.unbindDevice(id);
        
        return Result.success();
    }

    /**
     * 获取设备统计
     *
     * @return 设备统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取设备统计", description = "获取合作商设备统计信息")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询设备统计",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<Map<String, Object>> getDeviceStatistics() {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 获取设备统计信息
        Map<String, Object> statistics = deviceService.getPartnerDeviceStatistics(Long.valueOf(partnerId));
        
        return Result.success(statistics);
    }

    /**
     * 获取门店设备列表
     *
     * @param shopId 门店ID
     * @return 设备列表
     */
    @GetMapping("/shops/{shopId}")
    @Operation(summary = "获取门店设备列表", description = "获取合作商下门店的设备列表")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.READ,
        description = "合作商查询门店设备列表",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<List<DeviceVO>> getDevicesByShopId(@Parameter(description = "门店ID") @PathVariable Integer shopId) {
        
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();
        
        // 验证门店是否属于当前合作商
        validateShopOwnership(shopId, partnerId);
        
        // 获取门店设备列表
        List<DeviceVO> devices = deviceService.getDevicesByShopId(shopId);
        
        return Result.success(devices);
    }

    // ==================== 设备费用管理接口 ====================

    /**
     * 获取设备费用列表
     *
     * @param deviceId 设备ID（可选）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 设备费用列表
     */
    @GetMapping("/fee")
    @Operation(summary = "获取设备费用列表", description = "获取当前合作商的设备费用配置列表")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.QUERY,
        description = "查询设备费用列表",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<Page<DeviceFeeVO>> getDeviceFeeList(
            @Parameter(description = "设备ID") @RequestParam(required = false) Integer deviceId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Integer pageSize) {

        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 获取设备费用列表
        PageResult<DeviceFeeVO> pageResult = deviceFeeService.getDeviceFeeList(pageNum, pageSize, null, null, null, partnerId, null, null);

        // 转换为Page对象
        Page<DeviceFeeVO> feePage = new Page<>();
        feePage.setRecords(pageResult.getList());
        feePage.setTotal(pageResult.getTotal());
        feePage.setCurrent(pageResult.getPageNum());
        feePage.setSize(pageResult.getPageSize());

        return Result.success(feePage);
    }

    /**
     * 获取设备费用详情
     *
     * @param id 费用ID
     * @return 设备费用详情
     */
    @GetMapping("/fee/{id}")
    @Operation(summary = "获取设备费用详情", description = "根据费用ID获取设备费用详细信息")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.READ,
        description = "查询设备费用详情",
        targetType = AuditConstants.TargetType.DEVICE,
        targetIdParam = "id"
    )
    public Result<DeviceFeeVO> getDeviceFeeDetail(@Parameter(description = "费用ID") @PathVariable Integer id) {
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 获取设备费用详情
        DeviceFeeVO deviceFee = deviceFeeService.getDeviceFeeDetail(id);

        // 验证费用是否属于当前合作商
        if (!partnerId.equals(deviceFee.getPartnerId())) {
            return Result.failed("无权访问非本合作商的设备费用信息");
        }

        return Result.success(deviceFee);
    }

    /**
     * 创建设备费用配置
     *
     * @param deviceFeeDTO 设备费用信息
     * @return 创建结果
     */
    @PostMapping("/fee")
    @Operation(summary = "创建设备费用配置", description = "为设备创建费用配置")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.CREATE,
        description = "创建设备费用配置",
        targetType = AuditConstants.TargetType.DEVICE
    )
    public Result<DeviceFeeVO> createDeviceFee(@RequestBody @Valid DeviceFeeDTO deviceFeeDTO) {
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 设置合作商ID
        deviceFeeDTO.setPartnerId(partnerId);

        // 验证设备是否属于当前合作商
        if (deviceFeeDTO.getDeviceId() != null) {
            // 简单验证设备存在性，详细验证可以在service层实现
            // validateDeviceOwnership(deviceFeeDTO.getDeviceId(), partnerId);
        }

        // 转换DTO为Entity
        DeviceFee deviceFee = new DeviceFee();
        deviceFee.setFeeName(deviceFeeDTO.getFeeName());
        deviceFee.setFeeType(deviceFeeDTO.getFeeType());
        deviceFee.setEntityId(deviceFeeDTO.getEntityId());
        deviceFee.setPartnerId(deviceFeeDTO.getPartnerId());
        deviceFee.setShopId(deviceFeeDTO.getShopId());
        deviceFee.setDeviceId(deviceFeeDTO.getDeviceId());
        deviceFee.setPrice(deviceFeeDTO.getPrice());
        deviceFee.setUnit(deviceFeeDTO.getUnit());
        deviceFee.setMinTime(deviceFeeDTO.getMinTime());
        deviceFee.setMaxTime(deviceFeeDTO.getMaxTime());
        deviceFee.setDiscountType(deviceFeeDTO.getDiscountType());
        deviceFee.setDiscountValue(deviceFeeDTO.getDiscountValue());
        deviceFee.setDiscountCondition(deviceFeeDTO.getDiscountCondition());
        deviceFee.setStartTime(deviceFeeDTO.getStartTime());
        deviceFee.setEndTime(deviceFeeDTO.getEndTime());
        deviceFee.setStatus(deviceFeeDTO.getStatus());
        deviceFee.setIsDefault(deviceFeeDTO.getIsDefault());
        deviceFee.setRemark(deviceFeeDTO.getRemark());

        // 创建设备费用配置
        boolean success = deviceFeeService.addDeviceFee(deviceFee);
        if (!success) {
            return Result.failed("创建设备费用配置失败");
        }

        // 返回创建的费用配置
        DeviceFeeVO deviceFeeVO = deviceFeeService.getDeviceFeeDetail(deviceFee.getId());

        return Result.success(deviceFeeVO);
    }

    /**
     * 更新设备费用配置
     *
     * @param id 费用ID
     * @param deviceFeeDTO 设备费用信息
     * @return 更新结果
     */
    @PutMapping("/fee/{id}")
    @Operation(summary = "更新设备费用配置", description = "更新设备费用配置信息")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.UPDATE,
        description = "更新设备费用配置",
        targetType = AuditConstants.TargetType.DEVICE,
        targetIdParam = "id"
    )
    public Result<DeviceFeeVO> updateDeviceFee(
            @Parameter(description = "费用ID") @PathVariable Integer id,
            @RequestBody @Valid DeviceFeeDTO deviceFeeDTO) {

        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 验证费用是否属于当前合作商
        DeviceFeeVO existingFee = deviceFeeService.getDeviceFeeDetail(id);
        if (!partnerId.equals(existingFee.getPartnerId())) {
            return Result.failed("无权修改非本合作商的设备费用配置");
        }

        // 设置费用ID和合作商ID
        deviceFeeDTO.setId(id);
        deviceFeeDTO.setPartnerId(partnerId);

        // 验证设备是否属于当前合作商
        if (deviceFeeDTO.getDeviceId() != null) {
            // 简单验证设备存在性，详细验证可以在service层实现
            // validateDeviceOwnership(deviceFeeDTO.getDeviceId(), partnerId);
        }

        // 转换DTO为Entity
        DeviceFee deviceFee = new DeviceFee();
        deviceFee.setId(deviceFeeDTO.getId());
        deviceFee.setFeeName(deviceFeeDTO.getFeeName());
        deviceFee.setFeeType(deviceFeeDTO.getFeeType());
        deviceFee.setEntityId(deviceFeeDTO.getEntityId());
        deviceFee.setPartnerId(deviceFeeDTO.getPartnerId());
        deviceFee.setShopId(deviceFeeDTO.getShopId());
        deviceFee.setDeviceId(deviceFeeDTO.getDeviceId());
        deviceFee.setPrice(deviceFeeDTO.getPrice());
        deviceFee.setUnit(deviceFeeDTO.getUnit());
        deviceFee.setMinTime(deviceFeeDTO.getMinTime());
        deviceFee.setMaxTime(deviceFeeDTO.getMaxTime());
        deviceFee.setDiscountType(deviceFeeDTO.getDiscountType());
        deviceFee.setDiscountValue(deviceFeeDTO.getDiscountValue());
        deviceFee.setDiscountCondition(deviceFeeDTO.getDiscountCondition());
        deviceFee.setStartTime(deviceFeeDTO.getStartTime());
        deviceFee.setEndTime(deviceFeeDTO.getEndTime());
        deviceFee.setStatus(deviceFeeDTO.getStatus());
        deviceFee.setIsDefault(deviceFeeDTO.getIsDefault());
        deviceFee.setRemark(deviceFeeDTO.getRemark());

        // 更新设备费用配置
        boolean success = deviceFeeService.updateDeviceFee(deviceFee);
        if (!success) {
            return Result.failed("更新设备费用配置失败");
        }

        // 返回更新后的费用配置
        DeviceFeeVO deviceFeeVO = deviceFeeService.getDeviceFeeDetail(id);

        return Result.success(deviceFeeVO);
    }

    /**
     * 删除设备费用配置
     *
     * @param id 费用ID
     * @return 删除结果
     */
    @DeleteMapping("/fee/{id}")
    @Operation(summary = "删除设备费用配置", description = "删除设备费用配置")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.DELETE,
        description = "删除设备费用配置",
        targetType = AuditConstants.TargetType.DEVICE,
        targetIdParam = "id"
    )
    public Result<Void> deleteDeviceFee(@Parameter(description = "费用ID") @PathVariable Integer id) {
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 验证费用是否属于当前合作商
        DeviceFeeVO existingFee = deviceFeeService.getDeviceFeeDetail(id);
        if (!partnerId.equals(existingFee.getPartnerId())) {
            return Result.failed("无权删除非本合作商的设备费用配置");
        }

        // 删除设备费用配置
        deviceFeeService.deleteDeviceFee(id);

        return Result.success();
    }

    /**
     * 设置默认费用配置
     *
     * @param id 费用ID
     * @return 设置结果
     */
    @PutMapping("/fee/{id}/default")
    @Operation(summary = "设置默认费用配置", description = "将指定费用配置设为默认")
    @Auditable(
        module = AuditConstants.Module.DEVICE,
        operation = AuditConstants.Operation.UPDATE,
        description = "设置默认费用配置",
        targetType = AuditConstants.TargetType.DEVICE,
        targetIdParam = "id"
    )
    public Result<Void> setDefaultDeviceFee(@Parameter(description = "费用ID") @PathVariable Integer id) {
        // 获取当前登录的合作商ID
        Integer partnerId = getPartnerIdFromToken();

        // 验证费用是否属于当前合作商
        DeviceFeeVO existingFee = deviceFeeService.getDeviceFeeDetail(id);
        if (!partnerId.equals(existingFee.getPartnerId())) {
            return Result.failed("无权设置非本合作商的设备费用配置");
        }

        // 设置默认费用配置
        boolean success = deviceFeeService.setDefaultDeviceFee(id);
        if (!success) {
            return Result.failed("设置默认费用配置失败");
        }

        return Result.success();
    }

    /**
     * 验证门店归属
     *
     * @param shopId 门店ID
     * @param partnerId 合作商ID
     */
    private void validateShopOwnership(Integer shopId, Integer partnerId) {
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }
        
        if (!Long.valueOf(partnerId).equals(shop.getPartnerId())) {
            throw new BusinessException("无权访问非本合作商的门店");
        }
    }

    /**
     * 获取当前登录的合作商ID
     *
     * @return 合作商ID
     */
    private Integer getPartnerIdFromToken() {
        // 从Session中获取合作商ID
        Object partnerId = StpUtil.getSession().get("partnerId");
        if (partnerId == null) {
            throw new BusinessException("未找到合作商信息，请重新登录");
        }
        return Integer.valueOf(partnerId.toString());
    }
}
