package com.jycb.jycbz.modules.device.controller.admin;

import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.controller.BaseDeviceController;
import lombok.extern.slf4j.Slf4j;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.dto.DeviceCreateDTO;
import com.jycb.jycbz.modules.device.vo.DeviceVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 管理员端-设备控制器
 * 管理员只负责导入MAC地址和生成二维码，设备绑定等操作由用户自行完成
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/devices")
@Tag(name = "管理员端-设备API")
public class AdminDeviceController extends BaseDeviceController {

    // 继承自BaseDeviceController的通用方法，管理员拥有所有设备的查看权限

    @Override
    protected void validateListPermission(Integer partnerId, Integer shopId) {
        // 管理员拥有查看所有设备的权限，无需额外验证
    }

    @Override
    protected void validateDevicePermission(DeviceVO device) {
        // 管理员拥有查看所有设备的权限，无需额外验证
    }

    @Override
    protected Map<String, Object> getFilteredStatistics() {
        // 管理员可以查看全局统计信息
        return deviceService.getDeviceStatistics();
    }

    /**
     * 添加设备
     */
    @PostMapping
    @Operation(summary = "添加设备", description = "通过MAC地址录入设备，系统自动生成二维码URL")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.CREATE,
            description = "添加设备"
    )
    public Result<DeviceVO> addDevice(@RequestBody @Valid Device device) {
        // 生成二维码URL
        String qrcodeUrl = deviceService.generateQrcodeUrl(device.getMacAddress());
        device.setQrcodeUrl(qrcodeUrl);
        
        deviceService.addDevice(device);
        return Result.success(deviceService.getDeviceDetail(device.getId()));
    }

    /**
     * 更新设备基本信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新设备基本信息", description = "仅更新设备的基本信息，不涉及绑定关系")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备基本信息",
            targetId = "#id"
    )
    public Result<Void> updateDevice(@PathVariable Integer id, @RequestBody @Valid Device device) {
        device.setId(id);
        deviceService.updateDevice(device);
        return Result.success();
    }

    /**
     * 删除设备
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.DELETE,
            description = "删除设备",
            targetId = "#id"
    )
    public Result<Void> deleteDevice(@PathVariable Integer id) {
        deviceService.deleteDevice(id);
        return Result.success();
    }

    /**
     * 更新设备状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新设备状态")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备状态",
            targetId = "#id"
    )
    public Result<Void> updateDeviceStatus(
            @PathVariable Integer id,
            @Parameter(description = "设备状态：1-正常 2-维护中 3-故障") @RequestParam Integer status
    ) {
        deviceService.updateDeviceStatus(id, status);
        return Result.success();
    }

    // 统计信息方法已在BaseDeviceController中实现

    /**
     * 获取合作商下的设备列表
     */
    @GetMapping("/partners/{partnerId}")
    @Operation(summary = "获取合作商下的设备列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询合作商下的设备列表",
            targetId = "#partnerId"
    )
    public Result<List<DeviceVO>> getDevicesByPartnerId(@PathVariable Integer partnerId) {
        return Result.success(deviceService.getDevicesByPartnerId(partnerId));
    }

    /**
     * 获取门店下的设备列表
     */
    @GetMapping("/shops/{shopId}")
    @Operation(summary = "获取门店下的设备列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询门店下的设备列表",
            targetId = "#shopId"
    )
    public Result<List<DeviceVO>> getDevicesByShopId(@PathVariable Integer shopId) {
        return Result.success(deviceService.getDevicesByShopId(shopId));
    }

    // MAC地址查询方法已在BaseDeviceController中实现

    /**
     * 重新生成设备二维码
     */
    @PutMapping("/{id}/qrcode")
    @Operation(summary = "重新生成设备二维码")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "重新生成设备二维码",
            targetId = "#id"
    )
    public Result<String> regenerateQrcode(@PathVariable Integer id) {
        DeviceVO device = deviceService.getDeviceDetail(id);
        if (device == null) {
            return Result.failed("设备不存在");
        }
        String qrcodeUrl = deviceService.generateQrcodeUrl(device.getMacAddress());
        Device updateDevice = new Device();
        updateDevice.setId(id);
        updateDevice.setQrcodeUrl(qrcodeUrl);
        deviceService.updateById(updateDevice);
        return Result.success(qrcodeUrl);
    }

    /**
     * 批量导入设备
     */
    @PostMapping("/batch-import")
    @Operation(summary = "批量导入设备", description = "批量导入设备MAC地址，系统自动生成二维码URL")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.CREATE,
            description = "批量导入设备"
    )
    public Result<Map<String, Object>> batchImportDevices(@RequestBody List<DeviceCreateDTO> devices) {
        try {
            Map<String, Object> result = deviceService.batchImportDevices(devices);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量导入设备失败", e);
            return Result.failed("批量导入设备失败：" + e.getMessage());
        }
    }

    /**
     * 导出设备列表
     */
    @GetMapping("/export")
    @Operation(summary = "导出设备列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.EXPORT,
            description = "导出设备列表"
    )
    public Result<String> exportDeviceList(
            @Parameter(description = "设备编号") @RequestParam(required = false) String deviceNo,
            @Parameter(description = "设备状态：1-正常 2-维护中 3-故障") @RequestParam(required = false) Integer status,
            @Parameter(description = "合作商ID") @RequestParam(required = false) Integer partnerId,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "是否已绑定：0-未绑定 1-已绑定") @RequestParam(required = false) Integer isBound
    ) {
        return Result.success(deviceService.exportDeviceList(deviceNo, status, partnerId, shopId, isBound));
    }
}