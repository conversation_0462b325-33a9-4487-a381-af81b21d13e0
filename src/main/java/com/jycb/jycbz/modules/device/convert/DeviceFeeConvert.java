package com.jycb.jycbz.modules.device.convert;

import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.modules.device.vo.DeviceFeeVO;

import java.util.List;

/**
 * 设备费用转换器
 */
public interface DeviceFeeConvert {

    /**
     * 实体转VO
     *
     * @param deviceFee 设备费用实体
     * @return 设备费用VO
     */
    DeviceFeeVO convert(DeviceFee deviceFee);

    /**
     * 实体列表转VO列表
     *
     * @param deviceFeeList 设备费用实体列表
     * @return 设备费用VO列表
     */
    List<DeviceFeeVO> convertList(List<DeviceFee> deviceFeeList);

    /**
     * VO转实体
     *
     * @param deviceFeeVO 设备费用VO
     * @return 设备费用实体
     */
    DeviceFee convertToEntity(DeviceFeeVO deviceFeeVO);
} 