package com.jycb.jycbz.modules.order.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.lock.DistributedLock;
import com.jycb.jycbz.modules.finance.service.CommissionDetailService;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单定时任务
 * 用于处理订单超时和自动完成等定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderTask {

    private final OrderService orderService;
    private final DistributedLock distributedLock;
    private final CommissionDetailService commissionDetailService;
    
    // 未支付订单超时时间（分钟）
    private static final int UNPAID_ORDER_TIMEOUT_MINUTES = 30;
    
    // 进行中订单实时完成（分钟）
    private static final int ACTIVE_ORDER_REAL_TIME_COMPLETE_MINUTES = 1;
    
    // 历史订单保留时间（月）
    private static final int ORDER_HISTORY_RETENTION_MONTHS = 12;

    /**
     * 处理未支付订单超时
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void handleUnpaidOrderTimeout() {
        log.info("开始处理未支付订单超时...");
        
        String lockKey = "order:task:unpaid_timeout";
        boolean locked = distributedLock.tryLock(lockKey, 30);
        
        if (!locked) {
            log.warn("未能获取到分布式锁，跳过本次未支付订单超时处理");
            return;
        }
        
        try {
            // 查询超过30分钟未支付的订单
            LocalDateTime timeoutPoint = LocalDateTime.now().minusMinutes(UNPAID_ORDER_TIMEOUT_MINUTES);
            
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getOrderStatus, 0) // 未支付状态
                    .eq(Order::getPayStatus, 0) // 未支付
                    .lt(Order::getCreateTime, timeoutPoint) // 创建时间早于超时时间点
                    .select(Order::getId, Order::getOrderNo, Order::getCreateTime, Order::getOrderStatus, Order::getPayStatus); // 明确指定查询字段

            List<Order> timeoutOrders = orderService.list(queryWrapper);
            
            log.info("找到 {} 个超时未支付订单", timeoutOrders.size());
            
            int successCount = 0;
            for (Order order : timeoutOrders) {
                try {
                    boolean result = orderService.cancelOrder(order.getOrderNo(), "超时未支付自动取消");
                    if (result) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("取消超时未支付订单失败，订单号: {}, 错误: {}", order.getOrderNo(), e.getMessage());
                }
            }
            
            log.info("成功取消 {} 个超时未支付订单", successCount);
        } catch (Exception e) {
            log.error("处理未支付订单超时异常", e);
        } finally {
            distributedLock.unlock(lockKey);
        }
        
        log.info("未支付订单超时处理完成");
    }
    
    /**
     * 处理进行中订单实时完成
     * 每分钟执行一次，实时完成订单
     */
    @Scheduled(cron = "0 * * * * ?")
    public void handleActiveOrderRealTimeComplete() {
        log.debug("开始处理进行中订单实时完成...");

        String lockKey = "order:task:real_time_complete";
        boolean locked = distributedLock.tryLock(lockKey, 30);

        if (!locked) {
            log.debug("未能获取到分布式锁，跳过本次进行中订单实时完成处理");
            return;
        }

        try {
            // 查询已支付且在进行中状态的订单（实时完成）
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getOrderStatus, 1) // 进行中状态
                    .eq(Order::getPayStatus, 1) // 已支付
                    .isNotNull(Order::getStartTime) // 已开始使用
                    .isNull(Order::getEndTime) // 结束时间为空
                    .select(Order::getId, Order::getOrderNo, Order::getEntityId, Order::getPartnerId,
                           Order::getShopId, Order::getAmount, Order::getStartTime, Order::getEndTime); // 明确指定查询字段

            List<Order> activeOrders = orderService.list(queryWrapper);

            if (!activeOrders.isEmpty()) {
                log.info("找到 {} 个进行中订单需要实时完成", activeOrders.size());

                int successCount = 0;
                for (Order order : activeOrders) {
                    try {
                        // 调用完成订单方法
                        boolean result = orderService.completeOrder(
                                String.valueOf(order.getId()),
                                order.getOrderNo(),
                                order.getEntityId() != null ? order.getEntityId().intValue() : null,
                                order.getPartnerId() != null ? order.getPartnerId().intValue() : null,
                                order.getShopId() != null ? order.getShopId().intValue() : null,
                                order.getAmount()
                        );

                        if (result) {
                            successCount++;
                            log.info("实时完成订单成功，订单号: {}", order.getOrderNo());
                        }
                    } catch (Exception e) {
                        log.error("实时完成订单失败，订单号: {}, 错误: {}", order.getOrderNo(), e.getMessage());
                    }
                }

                log.info("成功实时完成 {} 个进行中订单", successCount);
            }
        } catch (Exception e) {
            log.error("处理进行中订单实时完成异常", e);
        } finally {
            distributedLock.unlock(lockKey);
        }

        log.debug("进行中订单实时完成处理完成");
    }
    
    /**
     * 处理已完成订单自动结算
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void handleCompletedOrderAutoSettlement() {
        log.info("开始处理已完成订单自动结算...");
        
        String lockKey = "order:task:completed_auto_settlement";
        boolean locked = distributedLock.tryLock(lockKey, 60);
        
        if (!locked) {
            log.warn("未能获取到分布式锁，跳过本次已完成订单自动结算处理");
            return;
        }
        
        try {
            // 触发佣金自动结算
            commissionDetailService.triggerAutoSettlement();
            log.info("已触发佣金自动结算");
            
            // 查询前一天已完成但未结算的订单
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getOrderStatus, 2) // 已完成状态
                    .eq(Order::getPayStatus, 1) // 已支付
                    .eq(Order::getCommissionStatus, 0) // 未分账
                    .ge(Order::getEndTime, yesterday) // 结束时间大于等于昨天0点
                    .lt(Order::getEndTime, today) // 结束时间小于今天0点
                    .select(Order::getId, Order::getOrderNo, Order::getEntityId, Order::getPartnerId,
                           Order::getShopId, Order::getAmount, Order::getEndTime); // 明确指定查询字段
            
            List<Order> unsettledOrders = orderService.list(queryWrapper);
            
            log.info("找到 {} 个已完成未结算订单", unsettledOrders.size());
            
            int successCount = 0;
            for (Order order : unsettledOrders) {
                try {
                    // 创建分成明细
                    boolean result = commissionDetailService.createCommissionDetail(
                            String.valueOf(order.getId()),
                            order.getOrderNo(),
                            order.getEntityId() != null ? order.getEntityId().intValue() : null,
                            order.getPartnerId() != null ? order.getPartnerId().intValue() : null,
                            order.getShopId() != null ? order.getShopId().intValue() : null,
                            order.getAmount()
                    );
                    
                    if (result) {
                        // 更新订单分账状态
                        order.setCommissionStatus(1); // 已分账
                        order.setCommissionTime(LocalDateTime.now());
                        orderService.updateById(order);
                        
                        successCount++;
                        log.info("订单分账处理成功，订单号: {}", order.getOrderNo());
                    }
                } catch (Exception e) {
                    log.error("订单分账处理失败，订单号: {}, 错误: {}", order.getOrderNo(), e.getMessage());
                }
            }
            
            log.info("成功处理 {} 个订单分账", successCount);
        } catch (Exception e) {
            log.error("处理已完成订单自动结算异常", e);
        } finally {
            distributedLock.unlock(lockKey);
        }
        
        log.info("已完成订单自动结算处理完成");
    }
    
    /**
     * 清理历史订单数据
     * 每月1日凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 1 * ?")
    public void cleanHistoricalOrderData() {
        log.info("开始清理历史订单数据...");
        
        String lockKey = "order:task:clean_historical_data";
        boolean locked = distributedLock.tryLock(lockKey, 60);
        
        if (!locked) {
            log.warn("未能获取到分布式锁，跳过本次历史订单数据清理");
            return;
        }
        
        try {
            // 计算清理时间点（12个月前）
            LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(ORDER_HISTORY_RETENTION_MONTHS);
            log.info("清理 {} 之前的历史订单数据", cutoffTime);
            
            // 查询需要归档的订单
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.lt(Order::getCreateTime, cutoffTime) // 创建时间早于截止时间
                    .and(wrapper -> wrapper
                            .eq(Order::getOrderStatus, 2) // 已完成
                            .or()
                            .eq(Order::getOrderStatus, 3) // 已取消
                            .or()
                            .eq(Order::getOrderStatus, 4) // 已退款
                    )
                    .select(Order::getId, Order::getOrderNo, Order::getCreateTime, Order::getOrderStatus); // 明确指定查询字段
            
            // 分批处理，避免一次性处理太多数据
            long totalCount = orderService.count(queryWrapper);
            log.info("找到 {} 个历史订单需要归档", totalCount);
            
            if (totalCount == 0) {
                log.info("没有需要归档的历史订单数据");
                return;
            }
            
            // 实现归档逻辑 - 将数据导出到CSV文件
            int batchSize = 1000;
            long batches = (totalCount + batchSize - 1) / batchSize; // 向上取整
            
            // 创建归档目录
            String archiveDir = "archive/orders/" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            File archiveDirFile = new File(archiveDir);
            if (!archiveDirFile.exists()) {
                boolean created = archiveDirFile.mkdirs();
                if (!created) {
                    log.error("创建归档目录失败: {}", archiveDir);
                    return;
                }
            }
            
            // 创建归档文件
            String archiveFileName = archiveDir + "/orders_" + cutoffTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".csv";
            File archiveFile = new File(archiveFileName);
            
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(archiveFile))) {
                // 写入CSV头
                writer.write("订单ID,订单编号,用户ID,设备ID,设备编号,业务主体ID,合作商ID,门店ID,订单金额,订单状态,支付状态,支付方式,支付交易号,开始时间,结束时间,创建时间,更新时间,备注");
                writer.newLine();
                
                int totalArchived = 0;
                
                for (int i = 0; i < batches; i++) {
                    // 每次查询一批数据
                    Page<Order> page = new Page<>(i + 1, batchSize);
                    IPage<Order> orderPage = orderService.page(page, queryWrapper);
                    List<Order> orders = orderPage.getRecords();
                    
                    if (orders.isEmpty()) {
                        break;
                    }
                    
                    // 写入CSV数据
                    for (Order order : orders) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(order.getId()).append(",");
                        sb.append(escapeCSV(order.getOrderNo())).append(",");
                        sb.append(order.getUserId()).append(",");
                        sb.append(order.getDeviceId()).append(",");
                        sb.append(escapeCSV(order.getDeviceNo())).append(",");
                        sb.append(order.getEntityId()).append(",");
                        sb.append(order.getPartnerId()).append(",");
                        sb.append(order.getShopId()).append(",");
                        sb.append(order.getAmount()).append(",");
                        sb.append(order.getOrderStatus()).append(",");
                        sb.append(order.getPayStatus()).append(",");
                        sb.append(escapeCSV(order.getPayType())).append(",");
                        sb.append(escapeCSV(order.getTransactionId())).append(",");
                        sb.append(formatDateTime(order.getStartTime())).append(",");
                        sb.append(formatDateTime(order.getEndTime())).append(",");
                        sb.append(formatDateTime(order.getCreateTime())).append(",");
                        sb.append(formatDateTime(order.getUpdateTime())).append(",");
                        sb.append(escapeCSV(order.getRemark()));
                        
                        writer.write(sb.toString());
                        writer.newLine();
                    }
                    
                    totalArchived += orders.size();
                    log.info("已归档第 {} 批历史订单数据，共 {} 条", i + 1, orders.size());
                    
                    // 获取ID列表，用于打标记
                    List<Long> orderIds = orders.stream()
                            .map(Order::getId)
                            .collect(Collectors.toList());

                    // 更新订单归档标记
                    // 注意：我们不删除数据，而是打上归档标记
                    for (Long orderId : orderIds) {
                        Order order = new Order();
                        order.setId(orderId);
                        // 不再设置已归档标记，因为数据库中不存在该字段
                        // 可以在备注中添加归档信息
                        order.setRemark("已归档: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        orderService.updateById(order);
                    }
                }
                
                log.info("历史订单归档完成，共归档 {} 条订单数据，归档文件: {}", totalArchived, archiveFileName);
            } catch (IOException e) {
                log.error("写入归档文件失败", e);
            }
            
        } catch (Exception e) {
            log.error("清理历史订单数据异常", e);
        } finally {
            distributedLock.unlock(lockKey);
        }
        
        log.info("历史订单数据清理完成");
    }
    
    /**
     * 格式化日期时间为CSV格式
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 转义CSV字段中的特殊字符
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        // 如果字段包含逗号、双引号或换行符，需要用双引号包围并转义双引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
} 