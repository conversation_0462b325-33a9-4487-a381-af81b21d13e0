package com.jycb.jycbz.config;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.jycb.jycbz.common.context.UserContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus字段自动填充处理器
 */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始执行插入填充...");
        
        // 创建时间
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        // 更新时间
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        
        // 创建者
        String username = getLoginUsername();
        this.strictInsertFill(metaObject, "createBy", String.class, username);
        // 更新者
        this.strictInsertFill(metaObject, "updateBy", String.class, username);
        
        // 逻辑删除标志，默认为0（未删除）
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        // 版本号，默认为1
        this.strictInsertFill(metaObject, "version", Integer.class, 1);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始执行更新填充...");
        
        // 更新时间
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        
        // 更新者
        String username = getLoginUsername();
        this.strictUpdateFill(metaObject, "updateBy", String.class, username);
    }
    
    /**
     * 获取当前登录用户名（支持异步上下文）
     */
    private String getLoginUsername() {
        try {
            // 优先从ThreadLocal获取用户上下文
            String username = UserContextHolder.getCurrentUsername();
            if (!"system".equals(username)) {
                log.debug("从用户上下文获取用户名: {}", username);
                return username;
            }

            // 如果ThreadLocal中没有，尝试从Sa-Token获取
            if (StpUtil.isLogin()) {
                String saTokenUser = StpUtil.getLoginIdAsString();
                log.debug("从Sa-Token获取用户名: {}", saTokenUser);
                return saTokenUser;
            }

        } catch (Exception e) {
            log.debug("获取登录用户失败：非 web 上下文无法获取 HttpServletRequest");
        }

        log.debug("使用系统默认用户名: system");
        return "system";
    }

    /**
     * 获取当前用户ID（支持异步上下文）
     */
    private String getCurrentUserId() {
        try {
            // 优先从ThreadLocal获取用户上下文
            String userId = UserContextHolder.getCurrentUserId();
            if (!"system".equals(userId)) {
                return userId;
            }

            // 如果ThreadLocal中没有，尝试从Sa-Token获取
            if (StpUtil.isLogin()) {
                return StpUtil.getLoginIdAsString();
            }

        } catch (Exception e) {
            log.debug("获取用户ID失败: {}", e.getMessage());
        }

        return "system";
    }
} 