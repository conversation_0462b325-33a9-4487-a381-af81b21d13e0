{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/orders/orders.vue?c967", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/orders/orders.vue?37bf", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/orders/orders.vue?ae26", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/orders/orders.vue?bbf7", "uni-app:///pages/orders/orders.vue", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/orders/orders.vue?57e1", "webpack:///C:/Users/<USER>/Desktop/今夜城堡-正式项目-前端-后端/前端/今夜城堡-门店端/pages/orders/orders.vue?83fd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "TabBar", "data", "activeTab", "orders", "loading", "page", "pageSize", "hasMore", "statusMap", "all", "pending", "completed", "canceled", "computed", "filteredOrders", "onLoad", "uni", "animation", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "switchTab", "loadOrders", "current", "size", "status", "console", "title", "icon", "loadMoreOrders", "refreshOrders", "getStatusClass", "getStatusText", "viewOrderDetail", "url", "completeOrder", "content", "success", "then", "catch", "finally"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgM;AAChM,gBAAgB,uMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAovB,CAAgB,kvBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC4HxwB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACA;;IAEA;IACAC;MACAC;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAF;MACAC;IACA;EACA;EACA;EACAE;IACA;EACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;;MAEA;MACA;;MAEA;MACA;QACAC;QACAC;QACAC;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACAV;MACA;QACAW;;QAEA;QACAX;UACAY;UACAC;QACA;;QAEA;QACA;;QAEA;QACAb;MACA;IACA;IAEA;IACAc;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACAlB;QACAmB;MACA;IACA;IAEA;IACAC;MAAA;MACApB;QACAY;QACAS;QACAC;UACA;YACAtB;cACAY;YACA;YAEA,iDACAW;cACAvB;gBACAY;cACA;cACA;YACA,GACAY;cACAxB;gBACAY;gBACAC;cACA;YACA,GACAY;cACAzB;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvUA;AAAA;AAAA;AAAA;AAAikC,CAAgB,2hCAAG,EAAC,C;;;;;;;;;;;ACArlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/orders/orders.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/orders/orders.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orders.vue?vue&type=template&id=8a71bbb4&\"\nvar renderjs\nimport script from \"./orders.vue?vue&type=script&lang=js&\"\nexport * from \"./orders.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orders.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/orders/orders.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=template&id=8a71bbb4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.filteredOrders.length : null\n  var l0 = _vm.__map(_vm.filteredOrders, function (order, index) {\n    var $orig = _vm.__get_orig(order)\n    var m0 = _vm.getStatusClass(order.status)\n    var m1 = _vm.getStatusText(order.status)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g1 = !_vm.loading && _vm.hasMore && _vm.filteredOrders.length > 0\n  var g2 = !_vm.loading && !_vm.hasMore && _vm.filteredOrders.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 头部信息 -->\n    <view class=\"top-spacing\"></view>\n    <view class=\"page-header\">\n      <view class=\"header-content\">\n        <view class=\"page-title purple-title\">订单管理</view>\n        <view class=\"page-subtitle\">查看店内设备订单记录</view>\n      </view>\n      <view>\n        <view class=\"refresh-btn\" @tap=\"refreshOrders\">\n          <text class=\"material-icons\" style=\"font-size: 28rpx; margin-right: 8rpx;\">refresh</text>\n          <text>刷新</text>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"order-tabs\">\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'all' }\"\n        @tap=\"switchTab('all')\"\n      >\n        全部\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'pending' }\"\n        @tap=\"switchTab('pending')\"\n      >\n        进行中\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'completed' }\"\n        @tap=\"switchTab('completed')\"\n      >\n        已完成\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'canceled' }\"\n        @tap=\"switchTab('canceled')\"\n      >\n        已取消\n      </view>\n    </view>\n    \n    <view class=\"order-list\">\n      <!-- 加载中状态 -->\n      <view class=\"loading-state\" v-if=\"loading\">\n        <text class=\"material-icons rotating\" style=\"font-size: 80rpx; color: var(--text-tertiary);\">sync</text>\n        <text>加载中...</text>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-else-if=\"filteredOrders.length === 0\">\n        <text class=\"material-icons\" style=\"font-size: 80rpx; color: var(--text-tertiary);\">receipt_long</text>\n        <text>暂无订单数据</text>\n      </view>\n      \n      <view class=\"order-card\" v-for=\"(order, index) in filteredOrders\" :key=\"order.id\" @tap=\"viewOrderDetail(order.id)\">\n        <view class=\"order-header\">\n          <view class=\"order-id\">订单号：{{ order.orderNo }}</view>\n          <view :class=\"['order-status', getStatusClass(order.status)]\">{{ getStatusText(order.status) }}</view>\n        </view>\n        \n        <view class=\"order-content\">\n          <view class=\"order-device\">\n            <text class=\"material-icons info-icon\">devices</text>\n            <text class=\"label\">设备：</text>\n            <text>{{ order.deviceName }}</text>\n          </view>\n          \n          <view class=\"room-container\" v-if=\"order.roomNumber\">\n            <text class=\"material-icons room-icon\">meeting_room</text>\n            <text class=\"label\">房间号：</text>\n            <text class=\"room-number\">{{ order.roomNumber }}</text>\n          </view>\n          \n          <view class=\"amount-container\">\n            <text class=\"material-icons info-icon\">payments</text>\n            <text class=\"label\">订单金额：</text>\n            <text class=\"amount\">{{ order.amount }}元</text>\n          </view>\n          \n          <view class=\"order-time\">\n            <text class=\"material-icons info-icon\">access_time</text>\n            <text class=\"time-text\">{{ order.createTime }}</text>\n          </view>\n        </view>\n        \n        <view class=\"order-footer\">\n          <view class=\"order-income\">\n            <text class=\"material-icons income-icon\">payments</text>\n            <text class=\"label\">订单收入：</text>\n            <text class=\"income-amount\">{{ order.shopIncome || '0.00' }}元</text>\n          </view>\n          \n          <view class=\"order-actions\" v-if=\"order.status === 1\">\n            <button class=\"action-btn\" @tap.stop=\"completeOrder(order.id)\">结束订单</button>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 加载更多 -->\n    <view class=\"load-more\" v-if=\"!loading && hasMore && filteredOrders.length > 0\" @tap=\"loadMoreOrders\">\n      <text>加载更多</text>\n    </view>\n    \n    <!-- 没有更多数据 -->\n    <view class=\"no-more\" v-if=\"!loading && !hasMore && filteredOrders.length > 0\">\n      <text>没有更多数据了</text>\n    </view>\n    \n    <!-- 添加底部导航栏 -->\n    <tab-bar current-tab=\"/pages/orders/orders\"></tab-bar>\n  </view>\n</template>\n\n<script>\nimport TabBar from '../../components/tab-bar.vue'\n\nexport default {\n  components: {\n    TabBar\n  },\n  data() {\n    return {\n      activeTab: 'all',\n      orders: [],\n      loading: false,\n      page: 1,\n      pageSize: 10,\n      hasMore: true,\n      statusMap: {\n        all: null,\n        pending: 1,\n        completed: 2,\n        canceled: 3\n      }\n    }\n  },\n  computed: {\n    filteredOrders() {\n      if (this.activeTab === 'all') {\n        return this.orders;\n      } else {\n        return this.orders.filter(order => order.status === this.statusMap[this.activeTab]);\n      }\n    }\n  },\n  onLoad() {\n    // 页面加载时的逻辑\n    \n    // 确保系统TabBar被隐藏\n    uni.hideTabBar({\n      animation: false\n    });\n    \n    // 加载订单数据\n    this.loadOrders();\n  },\n  onShow() {\n    // 页面显示时再次确保系统TabBar被隐藏\n    uni.hideTabBar({\n      animation: false\n    });\n  },\n  // 下拉刷新\n  onPullDownRefresh() {\n    this.refreshOrders();\n  },\n  // 上拉加载更多\n  onReachBottom() {\n    if (this.hasMore && !this.loading) {\n      this.loadMoreOrders();\n    }\n  },\n  methods: {\n    // 切换标签\n    switchTab(tab) {\n      if (this.activeTab === tab) return;\n      \n      this.activeTab = tab;\n      // 切换标签时重置页码并重新加载数据\n      this.page = 1;\n      this.orders = [];\n      this.hasMore = true;\n      this.loadOrders();\n    },\n    \n    // 加载订单\n    loadOrders() {\n      if (this.loading) return;\n      \n      this.loading = true;\n      \n      // 获取当前标签对应的状态\n      const status = this.statusMap[this.activeTab];\n      \n      // 调用API获取订单列表\n      this.$api.order.getList({\n        current: this.page,\n        size: this.pageSize,\n        status: status\n      }).then(res => {\n        const records = res.data.records || [];\n        const total = res.data.total || 0;\n        \n        // 更新订单列表\n        if (this.page === 1) {\n          this.orders = records;\n        } else {\n          this.orders = [...this.orders, ...records];\n        }\n        \n        // 更新是否有更多数据\n        this.hasMore = records.length === this.pageSize && this.orders.length < total;\n        \n        // 完成加载\n        this.loading = false;\n        \n        // 停止下拉刷新\n        uni.stopPullDownRefresh();\n      }).catch(err => {\n        console.error('获取订单列表失败', err);\n        \n        // 显示错误提示\n        uni.showToast({\n          title: '获取订单列表失败',\n          icon: 'none'\n        });\n        \n        // 完成加载\n        this.loading = false;\n        \n        // 停止下拉刷新\n        uni.stopPullDownRefresh();\n      });\n    },\n    \n    // 加载更多订单\n    loadMoreOrders() {\n      if (this.loading || !this.hasMore) return;\n      \n      // 页码加1\n      this.page++;\n      \n      // 加载更多订单\n      this.loadOrders();\n    },\n    \n    // 刷新订单\n    refreshOrders() {\n      // 重置页码\n      this.page = 1;\n      // 清空订单列表\n      this.orders = [];\n      // 重置是否有更多数据\n      this.hasMore = true;\n      // 加载订单\n      this.loadOrders();\n    },\n    \n    // 获取订单状态样式类\n    getStatusClass(status) {\n      switch (status) {\n        case 0: return 'pending'; // 未支付\n        case 1: return 'active'; // 使用中\n        case 2: return 'completed'; // 已完成\n        case 3: return 'canceled'; // 已取消\n        default: return '';\n      }\n    },\n    \n    // 获取订单状态文本\n    getStatusText(status) {\n      switch (status) {\n        case 0: return '未支付';\n        case 1: return '使用中';\n        case 2: return '已完成';\n        case 3: return '已取消';\n        default: return '未知';\n      }\n    },\n    \n    // 查看订单详情\n    viewOrderDetail(orderId) {\n      uni.navigateTo({\n        url: `/pages/orders/detail?id=${orderId}`\n      });\n    },\n    \n    // 手动结束订单\n    completeOrder(orderId) {\n      uni.showModal({\n        title: '提示',\n        content: '确定要手动结束该订单吗？',\n        success: res => {\n          if (res.confirm) {\n            uni.showLoading({\n              title: '处理中...'\n            });\n            \n            this.$api.order.complete(orderId, '门店管理员手动结束')\n              .then(res => {\n                uni.showToast({\n                  title: '订单已结束'\n                });\n                this.refreshOrders();\n              })\n              .catch(err => {\n                uni.showToast({\n                  title: err.message || '操作失败',\n                  icon: 'none'\n                });\n              })\n              .finally(() => {\n                uni.hideLoading();\n              });\n          }\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style>\n.container {\n\tmin-height: 100vh;\n\tpadding: 60rpx 30rpx 120rpx;\n\tbackground-image: radial-gradient(circle at top right, rgba(149, 39, 198, 0.15) 0%, transparent 40%), \n\t\t\t\t\t  radial-gradient(circle at bottom left, rgba(255, 46, 147, 0.15) 0%, transparent 40%);\n\tbackground-attachment: fixed;\n\tposition: relative;\n}\n\n.top-spacing {\n\theight: 50rpx;\n}\n\n.page-header {\n  margin-bottom: var(--space-lg);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 60rpx;\n  font-weight: 800;\n  margin-bottom: var(--space-xs);\n  display: inline-block;\n}\n\n.purple-title {\n  color: var(--primary-light);\n  -webkit-text-fill-color: var(--primary-light);\n  background: none;\n  text-shadow: 0 0 15rpx rgba(168, 117, 255, 0.4);\n}\n\n.page-subtitle {\n  color: var(--text-secondary);\n  font-size: 28rpx;\n}\n\n.refresh-btn {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, rgba(149, 39, 198, 0.2), rgba(110, 58, 217, 0.2));\n  border: 1px solid rgba(167, 117, 255, 0.3);\n  border-radius: var(--radius-full);\n  padding: 8rpx 24rpx;\n  font-size: 26rpx;\n  color: var(--primary-light);\n  transition: all 0.3s ease;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\n  position: relative;\n  overflow: hidden;\n}\n\n.refresh-btn::before {\n  content: '';\n  position: absolute;\n  top: -100%;\n  left: 0;\n  width: 100%;\n  height: 50%;\n  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transform: none;\n  animation: shiningVertical 2s infinite;\n}\n\n@keyframes shiningVertical {\n  0% {\n    top: -50%;\n  }\n  100% {\n    top: 150%;\n  }\n}\n\n.refresh-btn:active {\n  transform: scale(0.95);\n  background: linear-gradient(135deg, rgba(149, 39, 198, 0.3), rgba(110, 58, 217, 0.3));\n}\n\n.order-tabs {\n  display: flex;\n  background-color: transparent;\n  border-radius: var(--radius-lg);\n  margin-bottom: var(--space-lg);\n  overflow: hidden;\n  border: 1px solid var(--border-light);\n}\n\n.tab-item {\n  flex: 1;\n  text-align: center;\n  padding: var(--space-md);\n  font-size: 28rpx;\n  color: var(--text-secondary);\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.tab-item.active {\n  color: var(--primary-light);\n  font-weight: 500;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40%;\n  height: 4rpx;\n  background: linear-gradient(to right, var(--primary-dark), var(--primary-light));\n  border-radius: 4rpx;\n}\n\n.tab-item:active {\n  background-color: var(--bg-card-hover);\n}\n\n.order-card {\n  background-color: transparent;\n  border-radius: var(--radius-lg);\n  padding: var(--space-lg);\n  margin-bottom: var(--space-md);\n  border: 1px solid var(--border-light);\n}\n\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--space-md);\n}\n\n.order-id {\n  font-size: 34rpx;\n  font-weight: 500;\n}\n\n.order-status {\n  font-size: 28rpx;\n  padding: 4rpx 16rpx;\n  border-radius: var(--radius-full);\n}\n\n.order-status.pending {\n  background-color: rgba(255, 222, 89, 0.15);\n  color: var(--neon-yellow);\n}\n\n.order-status.completed {\n  background-color: rgba(0, 255, 133, 0.15);\n  color: var(--neon-green);\n}\n\n.order-status.canceled {\n  background-color: rgba(255, 46, 147, 0.15);\n  color: var(--neon-pink);\n}\n\n.order-content {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: var(--space-md);\n  margin-bottom: var(--space-md);\n}\n\n.order-device, .room-container, .order-time, .amount-container {\n  font-size: 30rpx;\n  display: flex;\n  align-items: center;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.info-icon, .room-icon {\n  font-size: 26rpx;\n  color: var(--text-secondary);\n  margin-right: 8rpx;\n  flex-shrink: 0;\n}\n\n.label {\n  color: var(--text-secondary);\n  margin-right: var(--space-xs);\n  flex-shrink: 0;\n}\n\n.time-text {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.room-number {\n  font-weight: 600;\n  color: var(--text-primary);\n  font-size: 30rpx;\n}\n\n.amount {\n  font-weight: 600;\n  color: #FFFFFF;\n  font-size: 30rpx;\n}\n\n.order-footer {\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  padding-top: var(--space-md);\n  border-top: 1px solid var(--border-light);\n}\n\n.order-income {\n  display: flex;\n  align-items: center;\n  font-size: 32rpx;\n  padding: 8rpx 0;\n}\n\n.income-icon {\n  font-size: 30rpx;\n  color: var(--neon-green);\n  margin-right: 8rpx;\n}\n\n.income-amount {\n  font-weight: 600;\n  color: var(--neon-green);\n  font-size: 36rpx;\n}\n\n.empty-state, .loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n  color: var(--text-tertiary);\n  gap: var(--space-md);\n}\n\n.load-more, .no-more {\n  text-align: center;\n  padding: 30rpx 0;\n  color: var(--text-tertiary);\n  font-size: 28rpx;\n}\n\n.load-more {\n  color: var(--primary-light);\n}\n\n.rotating {\n  animation: rotate 1.5s linear infinite;\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n</style> ", "import mod from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753035246042\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX.4.65.2025051206/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}