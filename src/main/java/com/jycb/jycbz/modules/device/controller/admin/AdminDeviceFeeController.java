package com.jycb.jycbz.modules.device.controller.admin;

import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.modules.device.service.DeviceFeeService;
import com.jycb.jycbz.modules.device.vo.DeviceFeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理员端-设备费用控制器
 * 管理员负责设备费用配置的全局管理，包括创建、更新和删除费用配置
 */
@RestController
@RequestMapping("/api/admin/device-fees")
@RequiredArgsConstructor
@Tag(name = "管理员端-设备费用API")
public class AdminDeviceFeeController {

    private final DeviceFeeService deviceFeeService;

    /**
     * 获取设备费用配置列表
     */
    @GetMapping
    @Operation(summary = "获取设备费用配置列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备费用配置列表"
    )
    public Result<PageResult<DeviceFeeVO>> getDeviceFeeList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页条数") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "费用名称") @RequestParam(required = false) String feeName,
            @Parameter(description = "费用类型：1-按时间 2-按次数 3-包天") @RequestParam(required = false) Integer feeType,
            @Parameter(description = "业务主体ID") @RequestParam(required = false) Integer entityId,
            @Parameter(description = "合作商ID") @RequestParam(required = false) Integer partnerId,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "状态：1-启用 0-禁用") @RequestParam(required = false) Integer status
    ) {
        return Result.success(deviceFeeService.getDeviceFeeList(page, size, feeName, feeType, entityId, partnerId, shopId, status));
    }

    /**
     * 获取设备费用配置详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取设备费用配置详情")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备费用配置详情",
            targetId = "#id"
    )
    public Result<DeviceFeeVO> getDeviceFeeDetail(@PathVariable Integer id) {
        return Result.success(deviceFeeService.getDeviceFeeDetail(id));
    }

    /**
     * 添加设备费用配置
     */
    @PostMapping
    @Operation(summary = "添加设备费用配置", description = "创建新的设备费用配置模板")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.CREATE,
            description = "添加设备费用配置"
    )
    public Result<Void> addDeviceFee(@RequestBody @Valid DeviceFee deviceFee) {
        deviceFeeService.addDeviceFee(deviceFee);
        return Result.success();
    }

    /**
     * 更新设备费用配置
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新设备费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备费用配置",
            targetId = "#id"
    )
    public Result<Void> updateDeviceFee(@PathVariable Integer id, @RequestBody @Valid DeviceFee deviceFee) {
        deviceFee.setId(id);
        deviceFeeService.updateDeviceFee(deviceFee);
        return Result.success();
    }

    /**
     * 删除设备费用配置
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.DELETE,
            description = "删除设备费用配置",
            targetId = "#id"
    )
    public Result<Void> deleteDeviceFee(@PathVariable Integer id) {
        deviceFeeService.deleteDeviceFee(id);
        return Result.success();
    }

    /**
     * 更新设备费用配置状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新设备费用配置状态")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备费用配置状态",
            targetId = "#id"
    )
    public Result<Void> updateDeviceFeeStatus(
            @PathVariable Integer id,
            @Parameter(description = "状态：1-启用 0-禁用") @RequestParam Integer status
    ) {
        deviceFeeService.updateDeviceFeeStatus(id, status);
        return Result.success();
    }

    /**
     * 设置默认费用配置
     */
    @PutMapping("/{id}/default")
    @Operation(summary = "设置默认费用配置", description = "设置全局默认费用配置，新设备将使用此配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "设置默认费用配置",
            targetId = "#id"
    )
    public Result<Void> setDefaultDeviceFee(@PathVariable Integer id) {
        deviceFeeService.setDefaultDeviceFee(id);
        return Result.success();
    }

    /**
     * 获取设备的费用配置
     */
    @GetMapping("/devices/{deviceId}")
    @Operation(summary = "获取设备的费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备的费用配置",
            targetId = "#deviceId"
    )
    public Result<DeviceFeeVO> getDeviceFeeByDeviceId(@PathVariable Integer deviceId) {
        return Result.success(deviceFeeService.getDeviceFeeVOByDeviceId(deviceId));
    }

    /**
     * 获取门店的费用配置列表
     */
    @GetMapping("/shops/{shopId}")
    @Operation(summary = "获取门店的费用配置列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询门店的费用配置列表",
            targetId = "#shopId"
    )
    public Result<List<DeviceFeeVO>> getDeviceFeesByShopId(@PathVariable Integer shopId) {
        return Result.success(deviceFeeService.getDeviceFeesByShopId(shopId));
    }

    /**
     * 获取合作商的费用配置列表
     */
    @GetMapping("/partners/{partnerId}")
    @Operation(summary = "获取合作商的费用配置列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询合作商的费用配置列表",
            targetId = "#partnerId"
    )
    public Result<List<DeviceFeeVO>> getDeviceFeesByPartnerId(@PathVariable Integer partnerId) {
        return Result.success(deviceFeeService.getDeviceFeesByPartnerId(partnerId));
    }

    /**
     * 获取默认费用配置
     */
    @GetMapping("/default")
    @Operation(summary = "获取默认费用配置", description = "获取系统设置的全局默认费用配置")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询默认费用配置"
    )
    public Result<DeviceFeeVO> getDefaultDeviceFee() {
        return Result.success(deviceFeeService.getDefaultDeviceFee());
    }

    /**
     * 导出设备费用配置列表
     */
    @GetMapping("/export")
    @Operation(summary = "导出设备费用配置列表")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.EXPORT,
            description = "导出设备费用配置列表"
    )
    public Result<String> exportDeviceFeeList(
            @Parameter(description = "费用名称") @RequestParam(required = false) String feeName,
            @Parameter(description = "费用类型：1-按时间 2-按次数 3-包天") @RequestParam(required = false) Integer feeType,
            @Parameter(description = "业务主体ID") @RequestParam(required = false) Integer entityId,
            @Parameter(description = "合作商ID") @RequestParam(required = false) Integer partnerId,
            @Parameter(description = "门店ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "状态：1-启用 0-禁用") @RequestParam(required = false) Integer status
    ) {
        return Result.success(deviceFeeService.exportDeviceFeeList(feeName, feeType, entityId, partnerId, shopId, status));
    }
} 