package com.jycb.jycbz.modules.admin.controller;

import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.modules.admin.dto.WxPhoneDTO;
import com.jycb.jycbz.modules.admin.service.WeixinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/weixin")
@RequiredArgsConstructor
@Tag(name = "微信接口", description = "微信相关接口")
public class WeixinController {

    private final WeixinService weixinService;

    /**
     * 获取用户手机号
     */
    @PostMapping("/getPhoneNumber")
    @Operation(summary = "获取用户手机号", description = "通过微信获取用户绑定的手机号")
    public Result<Object> getPhoneNumber(@Validated @RequestBody WxPhoneDTO wxPhoneDTO) {
        log.info("获取用户手机号，code: {}", wxPhoneDTO.getCode());
        Object phoneInfo = weixinService.getPhoneNumber(wxPhoneDTO.getCode());
        return Result.success(phoneInfo);
    }
} 