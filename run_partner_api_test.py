#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
今夜城堡 - 合作商角色API自动测试运行脚本
修复版本 - 解决了参数传递和数据类型问题
"""

import sys
import os
import argparse
from partner_api_test import PartnerAPITester

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='合作商角色API自动测试')
    parser.add_argument('--host', default='localhost', help='服务器主机地址')
    parser.add_argument('--port', default='8081', help='服务器端口')
    parser.add_argument('--username', default='partner_admin', help='测试用户名')
    parser.add_argument('--password', default='123456', help='测试密码')
    parser.add_argument('--modules', nargs='*', 
                       choices=['auth', 'partner', 'shop', 'device', 'finance', 'order'],
                       help='指定要测试的模块，不指定则测试所有模块')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 构建基础URL
    base_url = f"http://{args.host}:{args.port}"
    
    print("🚀 今夜城堡 - 合作商角色API自动测试")
    print("=" * 60)
    print(f"服务器地址: {base_url}")
    print(f"测试用户: {args.username}")
    print(f"测试密码: {args.password}")
    if args.modules:
        print(f"测试模块: {', '.join(args.modules)}")
    else:
        print("测试模块: 全部模块")
    print("=" * 60)
    
    # 创建测试实例
    tester = PartnerAPITester(base_url)
    
    # 更新测试用户信息
    tester.headers['User-Agent'] = 'Partner-API-Tester/1.0'
    
    # 运行测试
    all_results = []
    module_names = []
    
    # 认证模块（必须先执行）
    if not args.modules or 'auth' in args.modules:
        print("\n🔐 开始测试认证登录模块...")
        auth_results = tester.run_auth_tests()
        all_results.append(auth_results)
        module_names.append('认证模块')
        
        if not auth_results[0]:  # 如果登录失败，停止测试
            print("❌ 登录失败，停止测试")
            return 1
    else:
        # 如果不测试认证模块，也需要先登录
        print("\n🔐 执行登录...")
        if not tester.test_auth_login():
            print("❌ 登录失败，停止测试")
            return 1
    
    # 其他模块测试
    if not args.modules or 'partner' in args.modules:
        print("\n👤 开始测试合作商信息管理模块...")
        partner_results = tester.run_partner_tests()
        all_results.append(partner_results)
        module_names.append('合作商模块')
    
    if not args.modules or 'shop' in args.modules:
        print("\n🏪 开始测试门店管理模块...")
        shop_results = tester.run_shop_tests()
        all_results.append(shop_results)
        module_names.append('门店模块')
    
    if not args.modules or 'device' in args.modules:
        print("\n📱 开始测试设备管理模块...")
        device_results = tester.run_device_tests()
        all_results.append(device_results)
        module_names.append('设备模块')
    
    if not args.modules or 'finance' in args.modules:
        print("\n💰 开始测试财务管理模块...")
        finance_results = tester.run_finance_tests()
        all_results.append(finance_results)
        module_names.append('财务模块')
    
    if not args.modules or 'order' in args.modules:
        print("\n📋 开始测试订单管理模块...")
        order_results = tester.run_order_tests()
        all_results.append(order_results)
        module_names.append('订单模块')
    
    # 统计结果
    total_tests = sum(len(results) for results in all_results)
    passed_tests = sum(sum(results) for results in all_results)
    failed_tests = total_tests - passed_tests
    
    print("\n" + "=" * 60)
    print("📊 测试结果统计")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过数: {passed_tests}")
    print(f"失败数: {failed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 各模块测试结果:")
    for i, (results, name) in enumerate(zip(all_results, module_names)):
        status = "✅" if sum(results) == len(results) else "❌"
        print(f"{status} {name}: {sum(results)}/{len(results)} 通过")
    
    print(f"\n📝 详细日志请查看 partner_api_test.log 文件")
    
    # 生成简化的测试报告
    generate_simple_report(tester, all_results, module_names, total_tests, passed_tests, failed_tests)
    
    # 返回退出码
    return 0 if failed_tests == 0 else 1

def generate_simple_report(tester, all_results, module_names, total_tests, passed_tests, failed_tests):
    """生成简化的测试报告"""
    from datetime import datetime
    
    report_file = f"partner_api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("今夜城堡 - 合作商角色API测试报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"服务器地址: {tester.base_url}\n")
        f.write(f"测试用户: partner_admin\n")
        f.write("\n")
        
        f.write("测试概要:\n")
        f.write(f"总测试数: {total_tests}\n")
        f.write(f"通过数: {passed_tests}\n")
        f.write(f"失败数: {failed_tests}\n")
        f.write(f"通过率: {passed_tests/total_tests*100:.1f}%\n")
        f.write("\n")
        
        f.write("各模块结果:\n")
        for results, name in zip(all_results, module_names):
            status = "PASS" if sum(results) == len(results) else "FAIL"
            f.write(f"[{status}] {name}: {sum(results)}/{len(results)} 通过\n")
        
        f.write("\n")
        f.write("详细日志请查看 partner_api_test.log 文件\n")
    
    print(f"\n📄 测试报告已生成: {report_file}")

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试执行出错: {e}")
        sys.exit(1)
