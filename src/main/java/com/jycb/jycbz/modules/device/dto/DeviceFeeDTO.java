package com.jycb.jycbz.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备费用DTO
 */
@Data
@Schema(description = "设备费用DTO")
public class DeviceFeeDTO {

    @Schema(description = "费用ID")
    private Integer id;

    @Schema(description = "费用名称", required = true)
    @NotBlank(message = "费用名称不能为空")
    private String feeName;

    @Schema(description = "费用类型：1-按时间计费，2-按次计费", required = true)
    @NotNull(message = "费用类型不能为空")
    private Integer feeType;

    @Schema(description = "业务主体ID")
    private Integer entityId;

    @Schema(description = "合作商ID")
    private Integer partnerId;

    @Schema(description = "门店ID")
    private Integer shopId;

    @Schema(description = "设备ID")
    private Integer deviceId;

    @Schema(description = "价格", required = true)
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    private BigDecimal price;

    @Schema(description = "计费单位：分钟、小时、天、月", required = true)
    @NotBlank(message = "计费单位不能为空")
    private String unit;

    @Schema(description = "最小使用时间（分钟）")
    private Integer minTime;

    @Schema(description = "最大使用时间（分钟）")
    private Integer maxTime;

    @Schema(description = "折扣类型：0-无折扣，1-百分比折扣，2-固定金额折扣")
    private Integer discountType;

    @Schema(description = "折扣值")
    private BigDecimal discountValue;

    @Schema(description = "折扣条件（满多少金额享受折扣）")
    private BigDecimal discountCondition;

    @Schema(description = "生效开始时间")
    private LocalDateTime startTime;

    @Schema(description = "生效结束时间")
    private LocalDateTime endTime;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "是否默认费用：0-否，1-是")
    private Integer isDefault;

    @Schema(description = "备注")
    private String remark;
}
