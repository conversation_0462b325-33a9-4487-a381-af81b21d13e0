package com.jycb.jycbz.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.ByteArraySchema;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.media.StringSchema;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Swagger配置类
 * 提供API文档的基本信息
 */
@Configuration
public class SwaggerConfig {
    @Bean
    @SuppressWarnings("rawtypes")
    public OpenAPI swaggerOpenAPI() {
        // 添加自定义Base64响应Schema
        Map<String, Schema> schemas = new HashMap<>();
        schemas.put("Base64String", new StringSchema().format("byte"));
        schemas.put("ByteArray", new ByteArraySchema());
        
        return new OpenAPI()
                .info(new Info().title("今夜城堡API文档")
                        // 联系信息
                        .contact(new Contact().name("今夜城堡技术团队").email("<EMAIL>").url("https://www.jycb888.com"))
                        // 简介
                        .description("今夜城堡后端接口文档")
                        // 版本
                        .version("v1.0.0")
                        // 许可证
                        .license(new License().name("Apache 2.0").url("https://www.apache.org/licenses/LICENSE-2.0.html")))
                .externalDocs(new ExternalDocumentation()
                        .description("项目文档")
                        .url("https://www.jycb888.com/docs"));
    }
    
    /**
     * 自定义OpenAPI配置，处理Base64编码响应
     */
    @Bean
    @SuppressWarnings("unchecked")
    public OpenApiCustomizer openApiCustomizer() {
        return openApi -> {
            // 确保所有响应中的byte[]类型都被正确处理
            if (openApi.getComponents() != null && openApi.getComponents().getSchemas() != null) {
                openApi.getComponents().getSchemas().forEach((name, schema) -> {
                    if (schema != null && schema.getProperties() != null) {
                        schema.getProperties().forEach((propName, propSchemaObj) -> {
                            // 需要进行安全的类型转换
                            if (propSchemaObj instanceof Schema) {
                                Schema<?> propSchema = (Schema<?>) propSchemaObj;
                                if ("string".equals(propSchema.getType()) &&
                                    "byte".equals(propSchema.getFormat())) {
                                    // 确保Base64编码的字符串被正确处理
                                    propSchema.example("base64EncodedString");
                                }
                            }
                        });
                    }
                });
            }
        };
    }

} 